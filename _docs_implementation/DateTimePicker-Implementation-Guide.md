# DateTimePicker Component Implementation Guide

## Overview

The DateTimePicker is a comprehensive React component that supports both Gregorian and Hijri (Islamic) calendar systems with bilingual support (Arabic/English). It features intelligent positioning, time selection, and calendar type switching.

## Features

- ✅ **Dual Calendar Support**: Gregorian and Hijri calendars
- ✅ **Accurate Hijri Conversion**: Uses 30-year lunar cycle calculations
- ✅ **Bilingual Interface**: Arabic and English support with RTL layout
- ✅ **Time Selection**: 12h/24h format support
- ✅ **Smart Positioning**: Auto-adjusts popup position based on available space
- ✅ **Calendar Type Switching**: Toggle between Hijri and Gregorian
- ✅ **Multiple Date Formats**: Full, short, and custom formats
- ✅ **Input Field Synchronization**: Type dates to navigate calendar
- ✅ **Hierarchical Navigation**: Click month/year for quick selection
- ✅ **RTL Icon Positioning**: Calendar icon adapts to language direction
- ✅ **TypeScript Support**: Full type safety

## Installation & Setup

### Step 1: Install Dependencies

```bash
npm install react lucide-react clsx tailwind-merge
```

### Step 2: Setup Tailwind CSS

Ensure your `tailwind.config.js` includes the component paths:

```javascript
module.exports = {
  content: ["./components/**/*.{js,ts,jsx,tsx}", "./app/**/*.{js,ts,jsx,tsx}"],
  // ... rest of config
};
```

### Step 3: Setup Language Context

Create a language context for bilingual support:

```typescript
// contexts/LanguageContext.tsx
"use client";
import React, { createContext, useContext, useState } from "react";

type Language = "ar" | "en";
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>("ar");

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within LanguageProvider");
  }
  return context;
}
```

### Step 4: Copy Component Files

1. Copy `components/shared/DateTimePicker.tsx` to your project
2. Copy `components/ui/button.tsx` (or install shadcn/ui button component)

## Basic Usage

### Simple Hijri Date Picker

```typescript
import React, { useState } from "react";
import DateTimePicker from "@/components/shared/DateTimePicker";

function MyComponent() {
  const [selectedDate, setSelectedDate] = useState<Date>();

  return (
    <DateTimePicker
      label="Select Hijri Date"
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="hijri"
      format="full"
      placeholder="اختر التاريخ الهجري"
    />
  );
}
```

### Hijri Date & Time Picker

```typescript
<DateTimePicker
  label="Hijri Date & Time"
  value={selectedDateTime}
  onChange={setSelectedDateTime}
  dateType="hijri"
  format="full"
  showTime={true}
  timeFormat="12h"
  placeholder="Select date and time"
/>
```

### Switchable Calendar Type

```typescript
<DateTimePicker
  label="Date (Hijri/Gregorian)"
  value={selectedDate}
  onChange={setSelectedDate}
  dateType="hijri"
  format="full"
  allowTypeSwitch={true}
  placeholder="Select date"
/>
```

## Component Props

| Prop              | Type                                | Default        | Description                            |
| ----------------- | ----------------------------------- | -------------- | -------------------------------------- |
| `label`           | `string`                            | -              | Label text for the input               |
| `value`           | `Date`                              | -              | Selected date value                    |
| `onChange`        | `(date: Date) => void`              | -              | Callback when date changes             |
| `placeholder`     | `string`                            | Auto-generated | Placeholder text                       |
| `dateType`        | `"gregorian" \| "hijri"`            | `"gregorian"`  | Initial calendar type                  |
| `format`          | `"full" \| "short" \| "dd/mm/yyyy"` | `"full"`       | Date display format                    |
| `showTime`        | `boolean`                           | `false`        | Enable time selection                  |
| `timeFormat`      | `"12h" \| "24h"`                    | `"24h"`        | Time format                            |
| `allowTypeSwitch` | `boolean`                           | `false`        | Allow switching between calendar types |
| `className`       | `string`                            | -              | Additional CSS classes                 |
| `disabled`        | `boolean`                           | `false`        | Disable the component                  |

## Date Formats

### Hijri Date Formats

- **Full**: `5 صفر 1447 هـ` (Arabic) / `5 Safar 1447 AH` (English)
- **Short**: `5/2/1447 هـ` (Arabic) / `5/2/1447 AH` (English)
- **DD/MM/YYYY**: `05/02/1447`

### Gregorian Date Formats

- **Full**: `July 30, 2025` (English) / `30 يوليو 2025` (Arabic)
- **Short**: `7/30/2025` (English) / `30/7/2025` (Arabic)
- **DD/MM/YYYY**: `30/07/2025`

## Advanced Usage

### Using HijriDate Utility Class

The component exports a `HijriDate` utility class for manual conversions:

```typescript
import { HijriDate } from "@/components/shared/DateTimePicker";

// Convert Gregorian to Hijri
const today = new Date();
const hijriDate = HijriDate.toHijri(today);
console.log(hijriDate); // { year: 1447, month: 2, day: 5, monthName: "صفر" }

// Convert Hijri to Gregorian
const gregorianDate = HijriDate.fromHijri(1447, 2, 5);

// Get current Hijri date as formatted string
const hijriString = HijriDate.getCurrentHijriDateString("ar");
console.log(hijriString); // "5 صفر 1447 هـ"
```

### Custom Date Formatting

```typescript
// Helper function to format selected dates
const formatSelectedDate = (date: Date, isHijri: boolean = false): string => {
  if (isHijri) {
    const hijriDate = HijriDate.toHijri(date);
    const monthNames =
      language === "ar" ? HijriDate.hijriMonthsAr : HijriDate.hijriMonthsEn;
    return `${hijriDate.day} ${monthNames[hijriDate.month - 1]} ${
      hijriDate.year
    } ${language === "ar" ? "هـ" : "AH"}`;
  } else {
    return date.toLocaleDateString(language === "ar" ? "ar-SA" : "en-US");
  }
};
```

## Styling & Customization

### CSS Classes

The component uses Tailwind CSS classes. Key customizable elements:

```css
/* Input field */
.date-picker-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* Calendar popup */
.date-picker-popup {
  @apply absolute bg-white border border-gray-200 rounded-lg shadow-lg z-[9999] min-w-[320px] max-w-[400px];
}

/* Selected date */
.date-selected {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

/* Today's date */
.date-today {
  @apply border border-blue-500 font-semibold;
}
```

### RTL Support

The component automatically handles RTL layout for Arabic text:

```typescript
<div dir={language === "ar" ? "rtl" : "ltr"}>
  {/* Arabic content flows right-to-left */}
</div>
```

## Integration Examples

### With Form Libraries (React Hook Form)

```typescript
import { useForm, Controller } from "react-hook-form";

function MyForm() {
  const { control, handleSubmit } = useForm();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="hijriDate"
        control={control}
        render={({ field }) => (
          <DateTimePicker
            label="Birth Date (Hijri)"
            value={field.value}
            onChange={field.onChange}
            dateType="hijri"
            format="full"
          />
        )}
      />
    </form>
  );
}
```

### With State Management (Zustand)

```typescript
import { create } from "zustand";

interface DateStore {
  selectedDate: Date | undefined;
  setSelectedDate: (date: Date) => void;
}

const useDateStore = create<DateStore>((set) => ({
  selectedDate: undefined,
  setSelectedDate: (date) => set({ selectedDate: date }),
}));

function DatePickerComponent() {
  const { selectedDate, setSelectedDate } = useDateStore();

  return (
    <DateTimePicker
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="hijri"
    />
  );
}
```

## Troubleshooting

### Common Issues

1. **Calendar appears at bottom of page**

   - Ensure parent container has `position: relative`
   - Check z-index conflicts

2. **Hijri dates are inaccurate**

   - The component uses astronomical calculations
   - For official dates, consider using external APIs

3. **RTL layout issues**

   - Ensure proper language context setup
   - Check CSS direction properties

4. **TypeScript errors**
   - Ensure all required props are provided
   - Check Date type compatibility

### Performance Optimization

```typescript
// Memoize the component for better performance
const MemoizedDateTimePicker = React.memo(DateTimePicker);

// Use callback refs for onChange handlers
const handleDateChange = useCallback((date: Date) => {
  setSelectedDate(date);
}, []);
```

## Latest Updates (Version 1.4.0)

### Self-Contained Component Mode

The DateTimePicker now supports a **self-contained mode** where it manages its own state internally. This is perfect for demos, simple usage, or when you don't need external state management.

#### New Props

| Prop                     | Type      | Default | Description                               |
| ------------------------ | --------- | ------- | ----------------------------------------- |
| **`title`**              | `string`  | -       | **NEW**: Title for self-contained mode    |
| **`showSelectedDate`**   | `boolean` | `false` | **NEW**: Show selected date display       |
| **`initialDate`**        | `Date`    | -       | **NEW**: Initial date for standalone mode |
| **`containerClassName`** | `string`  | `""`    | **NEW**: Container CSS classes            |

#### Usage Examples

```typescript
// Self-contained mode - component manages its own state
<DateTimePicker
  title="Hijri Date Picker"
  dateType="hijri"
  showSelectedDate={true}
  initialDate={new Date()}
/>

// With time selection
<DateTimePicker
  title="Hijri Date & Time Picker"
  dateType="hijri"
  showTime={true}
  showSelectedDate={true}
/>

// Switchable calendar type
<DateTimePicker
  title="Switchable Calendar Type"
  dateType="hijri"
  allowTypeSwitch={true}
  showSelectedDate={true}
/>
```

### Smart Display Logic

The selected date display now uses intelligent conditional rendering:

```typescript
// Only shows when ALL conditions are met:
{
  showSelectedDate && hasSelectedDate && displayFormattedDate && (
    <div className="selected-date-display">
      <strong>Selected Date:</strong> {displayFormattedDate}
    </div>
  );
}
```

#### Benefits:

- ✅ **No empty boxes** on page refresh
- ✅ **Clean initial state** before date selection
- ✅ **Automatic appearance** after date selection
- ✅ **Consistent behavior** across all picker types

### Automatic Color Coding

The component automatically applies color coding based on picker type:

| Picker Type | Color  | CSS Classes                                      |
| ----------- | ------ | ------------------------------------------------ |
| Basic Hijri | Green  | `bg-green-50 border-green-200 text-green-800`    |
| Date & Time | Blue   | `bg-blue-50 border-blue-200 text-blue-800`       |
| Switchable  | Purple | `bg-purple-50 border-purple-200 text-purple-800` |
| Gregorian   | Orange | `bg-orange-50 border-orange-200 text-orange-800` |

### Ultra-Clean Demo Page

The demo page is now extremely simplified with all logic moved to the DateTimePicker component:

```typescript
export default function HijriDatePickerDemo() {
  const { language } = useLanguage();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
            {language === "ar"
              ? "منتقي التاريخ الهجري"
              : "Hijri Date Picker Demo"}
          </h1>

          <div className="space-y-8">
            {/* Today's Date Display */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
              {/* Today's date in both calendars */}
            </div>

            {/* Demo Examples */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <DateTimePicker
                  title={
                    language === "ar"
                      ? "منتقي التاريخ الهجري"
                      : "Hijri Date Picker"
                  }
                  dateType="hijri"
                  showSelectedDate={true}
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <DateTimePicker
                  title={
                    language === "ar"
                      ? "منتقي التاريخ والوقت الهجري"
                      : "Hijri Date & Time Picker"
                  }
                  dateType="hijri"
                  showTime={true}
                  showSelectedDate={true}
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <DateTimePicker
                  title={
                    language === "ar"
                      ? "منتقي التاريخ القابل للتبديل"
                      : "Switchable Calendar Type"
                  }
                  dateType="hijri"
                  allowTypeSwitch={true}
                  showSelectedDate={true}
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <DateTimePicker
                  title={
                    language === "ar"
                      ? "منتقي التاريخ الميلادي"
                      : "Gregorian Date Picker"
                  }
                  dateType="gregorian"
                  showSelectedDate={true}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Component Mode Detection

The component automatically detects which mode to use:

- **Controlled Mode**: When `value` and `onChange` props are provided
- **Self-Contained Mode**: When `title` prop is provided (and no `value`/`onChange`)

### Fixed Issues

#### Empty Selected Date Display

- **Problem**: Empty green boxes showing "Selected Date:" with no actual date
- **Solution**: Enhanced conditional logic that checks for actual date selection
- **Result**: Clean interface that only shows relevant information

```typescript
// Enhanced logic prevents empty displays
const hasSelectedDate = isStandalone ? internalDate : selectedDate;
const displayFormattedDate = isStandalone
  ? internalFormattedDate
  : selectedDate
  ? HijriDate.formatSelectedDate(
      selectedDate,
      currentDateType === "hijri",
      language as "ar" | "en",
      showTime
    )
  : "";
```

### New Interactive Features (Version 1.4.0)

#### Input Field Synchronization

Users can now type dates directly into the input field to navigate the calendar:

**Supported Formats:**

- `DD/MM/YYYY` (e.g., `5/2/1447` for Hijri, `15/9/2025` for Gregorian)
- `DD-MM-YYYY` (e.g., `5-2-1447` for Hijri, `15-9-2025` for Gregorian)
- `DD MM YYYY` (e.g., `5 2 1447` for Hijri, `15 9 2025` for Gregorian)

**How it works:**

- Type a date in the input field
- Calendar automatically navigates to show that month/year
- Date is not automatically selected (just navigation)
- Works for both Hijri and Gregorian calendars
- Graceful error handling for invalid dates

**Example Usage:**

```typescript
// User types "5/2/1447" in Hijri mode
// Calendar navigates to Safar 1447 (February 2025)

// User types "25/12/2025" in Gregorian mode
// Calendar navigates to December 2025
```

#### Hierarchical Month/Year Navigation

Click on month and year headers for quick navigation:

**Month Navigation:**

- Click on the month name (e.g., "Safar" or "February")
- Shows a 3×4 grid of all 12 months
- Current month is highlighted in blue
- Click any month to navigate and return to days view

**Year Navigation:**

- Click on the year number (e.g., "1447" or "2025")
- Shows a 2×5 grid of years in decade ranges
- Current year is highlighted in blue
- Navigation arrows to move between decades
- Click any year to navigate to month selector

**Navigation Flow:**

```
Days View → Click Month → Months View → Click Year → Years View
    ↑                        ↑                        ↓
    ←── Select Month ←───────┘                 Select Year
```

**Visual Features:**

- Hover effects with blue highlighting
- Smooth transitions between views
- Consistent styling across calendar types
- Bilingual support (Arabic/English)
- Works with both Hijri and Gregorian calendars

#### RTL Icon Positioning

Calendar icon now adapts to language direction:

- **Arabic (RTL)**: Calendar icon appears on the left side
- **English (LTR)**: Calendar icon appears on the right side
- Automatic positioning based on `language` context
- Consistent with RTL text direction standards

**Implementation:**

```typescript
className={cn(
  "absolute top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",
  language === "ar" ? "left-3" : "right-3"
)}
```

## Complete Implementation Example

### Full Demo Page

```typescript
"use client";

import React, { useState } from "react";
import DateTimePicker, { HijriDate } from "@/components/shared/DateTimePicker";
import { useLanguage } from "@/contexts/LanguageContext";

export default function HijriDatePickerDemo() {
  const { language } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedDate2, setSelectedDate2] = useState<Date>();
  const [selectedDate3, setSelectedDate3] = useState<Date>();

  // Helper function to format selected date based on picker type
  const formatSelectedDate = (date: Date, isHijri: boolean = false): string => {
    if (isHijri) {
      const hijriDate = HijriDate.toHijri(date);
      const monthNames =
        language === "ar" ? HijriDate.hijriMonthsAr : HijriDate.hijriMonthsEn;
      const timeStr = date.toLocaleTimeString(
        language === "ar" ? "ar-SA" : "en-US",
        {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        }
      );
      return `${hijriDate.day} ${monthNames[hijriDate.month - 1]} ${
        hijriDate.year
      } ${language === "ar" ? "هـ" : "AH"}, ${timeStr}`;
    } else {
      return date.toLocaleString(language === "ar" ? "ar-SA" : "en-US");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
          </h1>

          {/* Today's Date Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الميلادي" : "Gregorian Date"}
              </h3>
              <p className="text-lg text-gray-900">
                {new Date().toLocaleDateString(
                  language === "ar" ? "ar-SA" : "en-US",
                  {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                  }
                )}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
              </h3>
              <p
                className="text-lg text-gray-900"
                dir={language === "ar" ? "rtl" : "ltr"}
              >
                {HijriDate.getCurrentHijriDateString(language as "ar" | "en")}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Basic Hijri Date Picker */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">
              {language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
            </h2>
            <DateTimePicker
              label={language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
              value={selectedDate}
              onChange={setSelectedDate}
              dateType="hijri"
              format="full"
              placeholder={
                language === "ar" ? "اختر التاريخ الهجري" : "Select Hijri date"
              }
            />
            {selectedDate && (
              <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  <strong>
                    {language === "ar" ? "التاريخ المحدد:" : "Selected Date:"}
                  </strong>{" "}
                  {formatSelectedDate(selectedDate, true)}
                </p>
              </div>
            )}
          </div>

          {/* Hijri Date & Time Picker */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">
              {language === "ar"
                ? "منتقي التاريخ والوقت الهجري"
                : "Hijri Date & Time Picker"}
            </h2>
            <DateTimePicker
              label={
                language === "ar"
                  ? "التاريخ والوقت الهجري"
                  : "Hijri Date & Time"
              }
              value={selectedDate2}
              onChange={setSelectedDate2}
              dateType="hijri"
              format="full"
              showTime={true}
              timeFormat="12h"
              placeholder={
                language === "ar"
                  ? "اختر التاريخ والوقت"
                  : "Select date and time"
              }
            />
            {selectedDate2 && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>
                    {language === "ar"
                      ? "التاريخ والوقت المحدد:"
                      : "Selected Date & Time:"}
                  </strong>{" "}
                  {formatSelectedDate(selectedDate2, true)}
                </p>
              </div>
            )}
          </div>

          {/* Switchable Calendar Type */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">
              {language === "ar"
                ? "منتقي التاريخ القابل للتبديل"
                : "Switchable Calendar Type"}
            </h2>
            <DateTimePicker
              label={
                language === "ar"
                  ? "التاريخ (هجري/ميلادي)"
                  : "Date (Hijri/Gregorian)"
              }
              value={selectedDate3}
              onChange={setSelectedDate3}
              dateType="hijri"
              format="full"
              allowTypeSwitch={true}
              placeholder={language === "ar" ? "اختر التاريخ" : "Select date"}
            />
            {selectedDate3 && (
              <div className="mt-2 p-3 bg-purple-50 border border-purple-200 rounded-md">
                <p className="text-sm text-purple-800">
                  <strong>
                    {language === "ar" ? "التاريخ المحدد:" : "Selected Date:"}
                  </strong>{" "}
                  {formatSelectedDate(selectedDate3, true)}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
```

## API Reference

### HijriDate Class Methods

#### `static toHijri(gregorianDate: Date): HijriInfo`

Converts a Gregorian date to Hijri date.

```typescript
const hijriDate = HijriDate.toHijri(new Date());
// Returns: { year: 1447, month: 2, day: 5, monthName: "صفر" }
```

#### `static fromHijri(year: number, month: number, day: number): Date`

Converts a Hijri date to Gregorian date.

```typescript
const gregorianDate = HijriDate.fromHijri(1447, 2, 5);
// Returns: Date object
```

#### `static getCurrentHijriDateString(language: "ar" | "en"): string`

Gets current date as formatted Hijri string.

```typescript
const hijriString = HijriDate.getCurrentHijriDateString("ar");
// Returns: "5 صفر 1447 هـ"
```

#### `static getHijriMonthDays(year: number, month: number): number`

Gets number of days in a Hijri month.

```typescript
const days = HijriDate.getHijriMonthDays(1447, 2);
// Returns: 29 or 30
```

#### `static isHijriLeapYear(year: number): boolean`

Checks if a Hijri year is a leap year.

```typescript
const isLeap = HijriDate.isHijriLeapYear(1447);
// Returns: boolean
```

### Type Definitions

```typescript
type DateType = "gregorian" | "hijri";
type DateFormat = "full" | "short" | "dd/mm/yyyy";

interface HijriInfo {
  year: number;
  month: number;
  day: number;
  monthName: string;
}

interface DateTimePickerProps {
  label?: string;
  value?: Date;
  onChange?: (date: Date) => void;
  placeholder?: string;
  dateType?: DateType;
  format?: DateFormat;
  showTime?: boolean;
  timeFormat?: "12h" | "24h";
  allowTypeSwitch?: boolean;
  className?: string;
  disabled?: boolean;
}
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License - feel free to use in your projects.
