# Quick Setup Guide: Zod + Zustand in Your Next.js Project

## Step 1: Install Dependencies

```bash
# Install Zod and Zustand
npm install zod zustand

# Optional: Install middleware for persistence and devtools
# (zustand middleware is included in the main package)
```

## Step 2: Update Your TypeScript Configuration

Make sure your `tsconfig.json` has strict mode enabled for the best experience:

```json
{
  "compilerOptions": {
    "strict": true,
    "strictNullChecks": true,
    "noImplicitAny": true,
    // ... other options
  }
}
```

## Step 3: Create Your First Schema and Store

Create a new file `lib/userStore.ts`:

```typescript
import { z } from 'zod';
import { create } from 'zustand';

// Define schema
const UserSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
});

type User = z.infer<typeof UserSchema>;

// Create store
interface UserState {
  users: User[];
  addUser: (user: Omit<User, 'id'>) => void;
}

export const useUserStore = create<UserState>((set) => ({
  users: [],
  addUser: (userData) => {
    try {
      const newUser = UserSchema.parse({
        ...userData,
        id: crypto.randomUUID(),
      });
      
      set((state) => ({
        users: [...state.users, newUser],
      }));
    } catch (error) {
      console.error('Validation failed:', error);
    }
  },
}));
```

## Step 4: Use in Your Components

Create a component `components/UserExample.tsx`:

```typescript
'use client';

import React, { useState } from 'react';
import { useUserStore } from '@/lib/userStore';

export default function UserExample() {
  const { users, addUser } = useUserStore();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addUser({ name, email });
    setName('');
    setEmail('');
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">User Management</h2>
      
      <form onSubmit={handleSubmit} className="mb-4 space-y-2">
        <input
          type="text"
          placeholder="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="w-full p-2 border rounded"
          required
        />
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full p-2 border rounded"
          required
        />
        <button
          type="submit"
          className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Add User
        </button>
      </form>

      <div>
        <h3 className="font-semibold mb-2">Users ({users.length})</h3>
        {users.map((user) => (
          <div key={user.id} className="p-2 border rounded mb-2">
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-600">{user.email}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Step 5: Add to Your Page

In your `app/page.tsx` or any page:

```typescript
import UserExample from '@/components/UserExample';

export default function Home() {
  return (
    <main className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Zod + Zustand Demo</h1>
      <UserExample />
    </main>
  );
}
```

## Step 6: Test Your Setup

1. Run your development server:
   ```bash
   npm run dev
   ```

2. Navigate to your page and try:
   - Adding valid users (should work)
   - Adding invalid data (should show console errors)
   - Check browser DevTools to see Zustand state changes

## Next Steps

1. **Read the comprehensive guide**: Check out `zod-zustand-beginner-guide.md` for detailed explanations and advanced examples.

2. **Try the practical example**: Import and use the component from `zod-zustand-practical-example.tsx` to see a full-featured implementation.

3. **Add persistence**: Use Zustand's persist middleware to save state to localStorage:
   ```typescript
   import { persist } from 'zustand/middleware';
   
   export const useUserStore = create<UserState>()(
     persist(
       (set) => ({
         // your store implementation
       }),
       {
         name: 'user-storage',
       }
     )
   );
   ```

4. **Enable DevTools**: Add devtools middleware for debugging:
   ```typescript
   import { devtools } from 'zustand/middleware';
   
   export const useUserStore = create<UserState>()(
     devtools(
       persist(
         (set) => ({
           // your store implementation
         }),
         { name: 'user-storage' }
       ),
       { name: 'user-store' }
     )
   );
   ```

## Common Issues and Solutions

### Issue: "crypto is not defined"
**Solution**: Use a different ID generation method:
```typescript
// Instead of crypto.randomUUID()
const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2);
```

### Issue: Hydration errors with persistence
**Solution**: Use dynamic imports or check for client-side:
```typescript
'use client';

import dynamic from 'next/dynamic';

const UserExample = dynamic(() => import('@/components/UserExample'), {
  ssr: false,
});
```

### Issue: TypeScript errors with Zod schemas
**Solution**: Make sure you're using `z.infer<typeof Schema>` for type generation and enable strict mode in TypeScript.

## Resources

- [Zod Documentation](https://zod.dev/)
- [Zustand Documentation](https://zustand-demo.pmnd.rs/)
- [Next.js Documentation](https://nextjs.org/docs)

Happy coding! 🚀
