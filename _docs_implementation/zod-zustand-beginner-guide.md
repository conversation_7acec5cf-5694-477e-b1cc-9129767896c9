# Zod and Zustand: Complete Beginner's Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Zod - Schema Validation](#zod---schema-validation)
3. [Zustand - State Management](#zustand---state-management)
4. [Using Zod and Zustand Together](#using-zod-and-zustand-together)
5. [Real-World Examples](#real-world-examples)
6. [Best Practices](#best-practices)

## Introduction

This guide covers two essential libraries for modern React/TypeScript development:

- **Zod**: A TypeScript-first schema validation library
- **Zustand**: A lightweight state management solution

Both libraries are designed to be simple, type-safe, and developer-friendly.

## Zod - Schema Validation

### What is Zod?

Zod is a TypeScript-first schema validation library that helps you:

- Validate data at runtime
- Generate TypeScript types automatically
- Parse and transform data
- Handle errors gracefully

### Installation

```bash
npm install zod
# or
yarn add zod
# or
pnpm add zod
```

### Basic Zod Examples

#### 1. Simple Schema Definition

```typescript
import { z } from "zod";

// Define a schema
const UserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  age: z.number().min(0).max(120),
  isActive: z.boolean().default(true),
});

// Generate TypeScript type from schema
type User = z.infer<typeof UserSchema>;
// This creates: { id: number; name: string; email: string; age: number; isActive: boolean; }
```

#### 2. Data Validation

```typescript
// Valid data
const validUser = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
  isActive: true,
};

// Validate data
try {
  const user = UserSchema.parse(validUser);
  console.log("Valid user:", user);
} catch (error) {
  console.error("Validation failed:", error);
}

// Safe parsing (doesn't throw)
const result = UserSchema.safeParse(validUser);
if (result.success) {
  console.log("Valid user:", result.data);
} else {
  console.error("Validation errors:", result.error.issues);
}
```

#### 3. Advanced Schema Features

```typescript
// Optional and nullable fields
const ProfileSchema = z.object({
  username: z.string(),
  bio: z.string().optional(), // Can be undefined
  avatar: z.string().nullable(), // Can be null
  settings: z
    .object({
      theme: z.enum(["light", "dark"]),
      notifications: z.boolean().default(true),
    })
    .optional(),
});

// Arrays and refinements
const PostSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(10, "Content must be at least 10 characters"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  publishedAt: z.date().optional(),
  slug: z.string().regex(/^[a-z0-9-]+$/, "Invalid slug format"),
});

// Custom validation
const PasswordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain uppercase letter")
  .regex(/[a-z]/, "Password must contain lowercase letter")
  .regex(/[0-9]/, "Password must contain number");
```

#### 4. Form Validation Example

```typescript
// Form schema
const ContactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  phone: z.string().optional(),
});

type ContactForm = z.infer<typeof ContactFormSchema>;

// Usage in React component
function ContactForm() {
  const [formData, setFormData] = useState<Partial<ContactForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const result = ContactFormSchema.safeParse(formData);

    if (!result.success) {
      const newErrors: Record<string, string> = {};
      result.error.issues.forEach((issue) => {
        if (issue.path[0]) {
          newErrors[issue.path[0] as string] = issue.message;
        }
      });
      setErrors(newErrors);
      return;
    }

    // Form is valid, submit data
    console.log("Valid form data:", result.data);
    setErrors({});
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Name"
        value={formData.name || ""}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      />
      {errors.name && <span className="error">{errors.name}</span>}

      <input
        type="email"
        placeholder="Email"
        value={formData.email || ""}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      />
      {errors.email && <span className="error">{errors.email}</span>}

      <textarea
        placeholder="Message"
        value={formData.message || ""}
        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
      />
      {errors.message && <span className="error">{errors.message}</span>}

      <button type="submit">Submit</button>
    </form>
  );
}
```

## Zustand - State Management

### What is Zustand?

Zustand is a lightweight state management library that provides:

- Simple API with minimal boilerplate
- TypeScript support out of the box
- No providers or context needed
- Excellent performance
- DevTools integration

### Installation

```bash
npm install zustand
# or
yarn add zustand
# or
pnpm add zustand
```

### Basic Zustand Examples

#### 1. Simple Counter Store

```typescript
import { create } from "zustand";

// Define store interface
interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

// Create store
const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));

// Usage in React component
function Counter() {
  const { count, increment, decrement, reset } = useCounterStore();

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}
```

#### 2. Todo List Store

```typescript
interface Todo {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

interface TodoState {
  todos: Todo[];
  addTodo: (text: string) => void;
  toggleTodo: (id: string) => void;
  deleteTodo: (id: string) => void;
  clearCompleted: () => void;
}

const useTodoStore = create<TodoState>((set) => ({
  todos: [],

  addTodo: (text: string) =>
    set((state) => ({
      todos: [
        ...state.todos,
        {
          id: crypto.randomUUID(),
          text,
          completed: false,
          createdAt: new Date(),
        },
      ],
    })),

  toggleTodo: (id: string) =>
    set((state) => ({
      todos: state.todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      ),
    })),

  deleteTodo: (id: string) =>
    set((state) => ({
      todos: state.todos.filter((todo) => todo.id !== id),
    })),

  clearCompleted: () =>
    set((state) => ({
      todos: state.todos.filter((todo) => !todo.completed),
    })),
}));

// Usage in React component
function TodoList() {
  const { todos, addTodo, toggleTodo, deleteTodo, clearCompleted } =
    useTodoStore();
  const [inputText, setInputText] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim()) {
      addTodo(inputText.trim());
      setInputText("");
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Add a todo..."
        />
        <button type="submit">Add</button>
      </form>

      <ul>
        {todos.map((todo) => (
          <li key={todo.id}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span
              style={{
                textDecoration: todo.completed ? "line-through" : "none",
              }}
            >
              {todo.text}
            </span>
            <button onClick={() => deleteTodo(todo.id)}>Delete</button>
          </li>
        ))}
      </ul>

      <button onClick={clearCompleted}>Clear Completed</button>
    </div>
  );
}
```

#### 3. Advanced Zustand Features

```typescript
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Store with persistence and devtools
interface UserState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => void;
}

const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        isLoading: false,
        error: null,

        login: async (email: string, password: string) => {
          set({ isLoading: true, error: null });
          try {
            // Simulate API call
            const response = await fetch("/api/login", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ email, password }),
            });

            if (!response.ok) throw new Error("Login failed");

            const user = await response.json();
            set({ user, isLoading: false });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : "Login failed",
              isLoading: false,
            });
          }
        },

        logout: () => {
          set({ user: null, error: null });
        },

        updateProfile: (updates: Partial<User>) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, ...updates } });
          }
        },
      }),
      {
        name: "user-storage", // localStorage key
        partialize: (state) => ({ user: state.user }), // Only persist user data
      }
    ),
    {
      name: "user-store", // DevTools name
    }
  )
);
```

#### 4. Computed Values and Selectors

```typescript
// Store with computed values
interface ShoppingCartState {
  items: CartItem[];
  addItem: (product: Product, quantity: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
}

interface CartItem {
  product: Product;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
  price: number;
}

const useShoppingCartStore = create<ShoppingCartState>((set) => ({
  items: [],

  addItem: (product: Product, quantity: number) =>
    set((state) => {
      const existingItem = state.items.find(
        (item) => item.product.id === product.id
      );

      if (existingItem) {
        return {
          items: state.items.map((item) =>
            item.product.id === product.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          ),
        };
      }

      return {
        items: [...state.items, { product, quantity }],
      };
    }),

  removeItem: (productId: string) =>
    set((state) => ({
      items: state.items.filter((item) => item.product.id !== productId),
    })),

  updateQuantity: (productId: string, quantity: number) =>
    set((state) => ({
      items: state.items.map((item) =>
        item.product.id === productId ? { ...item, quantity } : item
      ),
    })),

  clearCart: () => set({ items: [] }),
}));

// Computed selectors (use outside the store)
const useCartTotal = () => {
  return useShoppingCartStore((state) =>
    state.items.reduce(
      (total, item) => total + item.product.price * item.quantity,
      0
    )
  );
};

const useCartItemCount = () => {
  return useShoppingCartStore((state) =>
    state.items.reduce((count, item) => count + item.quantity, 0)
  );
};

// Usage in component
function ShoppingCart() {
  const { items, addItem, removeItem, updateQuantity, clearCart } =
    useShoppingCartStore();
  const total = useCartTotal();
  const itemCount = useCartItemCount();

  return (
    <div>
      <h2>Shopping Cart ({itemCount} items)</h2>
      {items.map((item) => (
        <div key={item.product.id}>
          <span>{item.product.name}</span>
          <span>${item.product.price}</span>
          <input
            type="number"
            value={item.quantity}
            onChange={(e) =>
              updateQuantity(item.product.id, parseInt(e.target.value))
            }
          />
          <button onClick={() => removeItem(item.product.id)}>Remove</button>
        </div>
      ))}
      <div>Total: ${total.toFixed(2)}</div>
      <button onClick={clearCart}>Clear Cart</button>
    </div>
  );
}
```

## Using Zod and Zustand Together

### API Response Validation with Zustand

```typescript
import { z } from "zod";
import { create } from "zustand";

// Define API response schemas
const ApiUserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  avatar: z.string().url().optional(),
  createdAt: z.string().datetime(),
});

const ApiResponseSchema = z.object({
  data: z.array(ApiUserSchema),
  meta: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
  }),
});

type ApiUser = z.infer<typeof ApiUserSchema>;
type ApiResponse = z.infer<typeof ApiResponseSchema>;

// Store with validated API calls
interface UsersState {
  users: ApiUser[];
  isLoading: boolean;
  error: string | null;
  fetchUsers: () => Promise<void>;
  addUser: (userData: Omit<ApiUser, "id" | "createdAt">) => Promise<void>;
}

const useUsersStore = create<UsersState>((set, get) => ({
  users: [],
  isLoading: false,
  error: null,

  fetchUsers: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch("/api/users");
      const rawData = await response.json();

      // Validate API response with Zod
      const validatedData = ApiResponseSchema.parse(rawData);

      set({
        users: validatedData.data,
        isLoading: false,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("API response validation failed:", error.issues);
        set({
          error: "Invalid data received from server",
          isLoading: false,
        });
      } else {
        set({
          error:
            error instanceof Error ? error.message : "Failed to fetch users",
          isLoading: false,
        });
      }
    }
  },

  addUser: async (userData) => {
    try {
      // Validate input data
      const validatedInput = ApiUserSchema.omit({
        id: true,
        createdAt: true,
      }).parse(userData);

      const response = await fetch("/api/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(validatedInput),
      });

      const rawData = await response.json();
      const newUser = ApiUserSchema.parse(rawData);

      set((state) => ({
        users: [...state.users, newUser],
      }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("Validation failed:", error.issues);
        set({ error: "Invalid user data" });
      } else {
        set({ error: "Failed to add user" });
      }
    }
  },
}));
```

### Form State Management with Validation

```typescript
import { z } from "zod";
import { create } from "zustand";

// Form schema
const ProfileFormSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  age: z
    .number()
    .min(18, "Must be at least 18 years old")
    .max(100, "Must be less than 100"),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  interests: z.array(z.string()).min(1, "Select at least one interest"),
});

type ProfileFormData = z.infer<typeof ProfileFormSchema>;
type FormErrors = Partial<Record<keyof ProfileFormData, string>>;

interface ProfileFormState {
  formData: Partial<ProfileFormData>;
  errors: FormErrors;
  isSubmitting: boolean;
  isValid: boolean;
  updateField: <K extends keyof ProfileFormData>(
    field: K,
    value: ProfileFormData[K]
  ) => void;
  validateForm: () => boolean;
  submitForm: () => Promise<void>;
  resetForm: () => void;
}

const useProfileFormStore = create<ProfileFormState>((set, get) => ({
  formData: {},
  errors: {},
  isSubmitting: false,
  isValid: false,

  updateField: (field, value) => {
    set((state) => {
      const newFormData = { ...state.formData, [field]: value };

      // Clear field error when user starts typing
      const newErrors = { ...state.errors };
      delete newErrors[field];

      return {
        formData: newFormData,
        errors: newErrors,
      };
    });
  },

  validateForm: () => {
    const { formData } = get();
    const result = ProfileFormSchema.safeParse(formData);

    if (result.success) {
      set({ errors: {}, isValid: true });
      return true;
    } else {
      const newErrors: FormErrors = {};
      result.error.issues.forEach((issue) => {
        const field = issue.path[0] as keyof ProfileFormData;
        if (field) {
          newErrors[field] = issue.message;
        }
      });

      set({ errors: newErrors, isValid: false });
      return false;
    }
  },

  submitForm: async () => {
    const { formData, validateForm } = get();

    if (!validateForm()) {
      return;
    }

    set({ isSubmitting: true });

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Form submitted successfully:", formData);

      // Reset form after successful submission
      set({
        formData: {},
        errors: {},
        isSubmitting: false,
        isValid: false,
      });
    } catch (error) {
      set({
        errors: { email: "Failed to submit form. Please try again." },
        isSubmitting: false,
      });
    }
  },

  resetForm: () => {
    set({
      formData: {},
      errors: {},
      isSubmitting: false,
      isValid: false,
    });
  },
}));

// Usage in React component
function ProfileForm() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateForm,
    submitForm,
    resetForm,
  } = useProfileFormStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    submitForm();
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <input
          type="text"
          placeholder="First Name"
          value={formData.firstName || ""}
          onChange={(e) => updateField("firstName", e.target.value)}
          onBlur={validateForm}
        />
        {errors.firstName && <span className="error">{errors.firstName}</span>}
      </div>

      <div>
        <input
          type="text"
          placeholder="Last Name"
          value={formData.lastName || ""}
          onChange={(e) => updateField("lastName", e.target.value)}
          onBlur={validateForm}
        />
        {errors.lastName && <span className="error">{errors.lastName}</span>}
      </div>

      <div>
        <input
          type="email"
          placeholder="Email"
          value={formData.email || ""}
          onChange={(e) => updateField("email", e.target.value)}
          onBlur={validateForm}
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>

      <div>
        <input
          type="number"
          placeholder="Age"
          value={formData.age || ""}
          onChange={(e) => updateField("age", parseInt(e.target.value))}
          onBlur={validateForm}
        />
        {errors.age && <span className="error">{errors.age}</span>}
      </div>

      <div>
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
        <button type="button" onClick={resetForm}>
          Reset
        </button>
      </div>
    </form>
  );
}
```

## Real-World Examples

### E-commerce Product Management

```typescript
import { z } from "zod";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Product schemas
const ProductSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
  category: z.enum(["electronics", "clothing", "books", "home"]),
  inStock: z.boolean(),
  images: z.array(z.string().url()).min(1, "At least one image is required"),
  tags: z.array(z.string()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const ProductFiltersSchema = z.object({
  category: z.enum(["electronics", "clothing", "books", "home"]).optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  inStock: z.boolean().optional(),
  searchTerm: z.string().optional(),
});

type Product = z.infer<typeof ProductSchema>;
type ProductFilters = z.infer<typeof ProductFiltersSchema>;

interface ProductState {
  products: Product[];
  filteredProducts: Product[];
  filters: ProductFilters;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProducts: () => Promise<void>;
  addProduct: (
    productData: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => Promise<void>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  setFilters: (filters: ProductFilters) => void;
  clearFilters: () => void;
}

const useProductStore = create<ProductState>()(
  devtools(
    persist(
      (set, get) => ({
        products: [],
        filteredProducts: [],
        filters: {},
        isLoading: false,
        error: null,

        fetchProducts: async () => {
          set({ isLoading: true, error: null });

          try {
            const response = await fetch("/api/products");
            const rawData = await response.json();

            // Validate each product
            const validatedProducts = rawData.map((item: unknown) =>
              ProductSchema.parse(item)
            );

            set({
              products: validatedProducts,
              filteredProducts: validatedProducts,
              isLoading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to fetch products",
              isLoading: false,
            });
          }
        },

        addProduct: async (productData) => {
          try {
            // Validate input
            const validatedData = ProductSchema.omit({
              id: true,
              createdAt: true,
              updatedAt: true,
            }).parse(productData);

            const newProduct: Product = {
              ...validatedData,
              id: crypto.randomUUID(),
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            // Simulate API call
            const response = await fetch("/api/products", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(newProduct),
            });

            if (!response.ok) throw new Error("Failed to add product");

            set((state) => {
              const updatedProducts = [...state.products, newProduct];
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to add product",
            });
          }
        },

        updateProduct: async (id, updates) => {
          try {
            const currentProduct = get().products.find((p) => p.id === id);
            if (!currentProduct) throw new Error("Product not found");

            const updatedProduct = {
              ...currentProduct,
              ...updates,
              updatedAt: new Date(),
            };

            // Validate updated product
            ProductSchema.parse(updatedProduct);

            // Simulate API call
            const response = await fetch(`/api/products/${id}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updatedProduct),
            });

            if (!response.ok) throw new Error("Failed to update product");

            set((state) => {
              const updatedProducts = state.products.map((p) =>
                p.id === id ? updatedProduct : p
              );
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to update product",
            });
          }
        },

        deleteProduct: async (id) => {
          try {
            const response = await fetch(`/api/products/${id}`, {
              method: "DELETE",
            });

            if (!response.ok) throw new Error("Failed to delete product");

            set((state) => {
              const updatedProducts = state.products.filter((p) => p.id !== id);
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to delete product",
            });
          }
        },

        setFilters: (filters) => {
          try {
            const validatedFilters = ProductFiltersSchema.parse(filters);

            set((state) => ({
              filters: validatedFilters,
              filteredProducts: applyFilters(state.products, validatedFilters),
            }));
          } catch (error) {
            console.error("Invalid filters:", error);
          }
        },

        clearFilters: () => {
          set((state) => ({
            filters: {},
            filteredProducts: state.products,
          }));
        },
      }),
      {
        name: "product-storage",
        partialize: (state) => ({
          products: state.products,
          filters: state.filters,
        }),
      }
    ),
    { name: "product-store" }
  )
);

// Helper function to apply filters
function applyFilters(products: Product[], filters: ProductFilters): Product[] {
  return products.filter((product) => {
    if (filters.category && product.category !== filters.category) return false;
    if (filters.minPrice && product.price < filters.minPrice) return false;
    if (filters.maxPrice && product.price > filters.maxPrice) return false;
    if (filters.inStock !== undefined && product.inStock !== filters.inStock)
      return false;
    if (
      filters.searchTerm &&
      !product.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
    )
      return false;

    return true;
  });
}

// Usage in React component
function ProductList() {
  const {
    filteredProducts,
    filters,
    isLoading,
    error,
    fetchProducts,
    setFilters,
    clearFilters,
  } = useProductStore();

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {
    setFilters({ ...filters, ...newFilters });
  };

  if (isLoading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <div className="filters">
        <select
          value={filters.category || ""}
          onChange={(e) =>
            handleFilterChange({ category: e.target.value as any })
          }
        >
          <option value="">All Categories</option>
          <option value="electronics">Electronics</option>
          <option value="clothing">Clothing</option>
          <option value="books">Books</option>
          <option value="home">Home</option>
        </select>

        <input
          type="number"
          placeholder="Min Price"
          value={filters.minPrice || ""}
          onChange={(e) =>
            handleFilterChange({ minPrice: parseFloat(e.target.value) })
          }
        />

        <input
          type="number"
          placeholder="Max Price"
          value={filters.maxPrice || ""}
          onChange={(e) =>
            handleFilterChange({ maxPrice: parseFloat(e.target.value) })
          }
        />

        <button onClick={clearFilters}>Clear Filters</button>
      </div>

      <div className="products">
        {filteredProducts.map((product) => (
          <div key={product.id} className="product-card">
            <h3>{product.name}</h3>
            <p>${product.price}</p>
            <p>Category: {product.category}</p>
            <p>In Stock: {product.inStock ? "Yes" : "No"}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Best Practices

### Zod Best Practices

#### 1. Schema Organization

```typescript
// ✅ Good: Organize schemas in separate files
// schemas/user.ts
export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
});

// schemas/product.ts
export const ProductSchema = z.object({
  id: z.string(),
  name: z.string(),
  price: z.number().positive(),
});

// schemas/index.ts
export * from "./user";
export * from "./product";
```

#### 2. Reusable Schema Patterns

```typescript
// Common validation patterns
const EmailSchema = z.string().email();
const PasswordSchema = z
  .string()
  .min(8)
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/);
const PhoneSchema = z.string().regex(/^\+?[\d\s-()]+$/);

// Base schemas for extension
const BaseEntitySchema = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const UserSchema = BaseEntitySchema.extend({
  name: z.string(),
  email: EmailSchema,
  password: PasswordSchema,
});
```

#### 3. Error Handling

```typescript
// ✅ Good: Comprehensive error handling
function validateUserData(data: unknown) {
  const result = UserSchema.safeParse(data);

  if (!result.success) {
    const errors = result.error.issues.map((issue) => ({
      field: issue.path.join("."),
      message: issue.message,
      code: issue.code,
    }));

    return { success: false, errors };
  }

  return { success: true, data: result.data };
}

// ✅ Good: Custom error messages
const UserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  age: z.number().min(18, "Must be 18 or older"),
  email: z.string().email("Please enter a valid email address"),
});
```

#### 4. Transform and Preprocess

```typescript
// Transform data during validation
const UserInputSchema = z.object({
  name: z.string().trim().toLowerCase(),
  age: z.string().transform((val) => parseInt(val, 10)),
  email: z.string().email().toLowerCase(),
  tags: z.string().transform((val) => val.split(",").map((tag) => tag.trim())),
});

// Preprocess data before validation
const DateSchema = z.preprocess((val) => {
  if (typeof val === "string") return new Date(val);
  return val;
}, z.date());
```

### Zustand Best Practices

#### 1. Store Organization

```typescript
// ✅ Good: Separate concerns into different stores
// stores/authStore.ts
export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  login: async (credentials) => {
    /* ... */
  },
  logout: () => {
    /* ... */
  },
}));

// stores/cartStore.ts
export const useCartStore = create<CartState>((set) => ({
  items: [],
  addItem: (item) => {
    /* ... */
  },
  removeItem: (id) => {
    /* ... */
  },
}));

// ❌ Avoid: One massive store for everything
```

#### 2. Action Patterns

```typescript
// ✅ Good: Async actions with proper error handling
const useDataStore = create<DataState>((set, get) => ({
  data: [],
  isLoading: false,
  error: null,

  fetchData: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await api.getData();
      set({ data: response, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  // ✅ Good: Optimistic updates
  updateItem: async (id, updates) => {
    // Optimistically update UI
    set((state) => ({
      data: state.data.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));

    try {
      await api.updateItem(id, updates);
    } catch (error) {
      // Revert on error
      get().fetchData();
      set({ error: "Failed to update item" });
    }
  },
}));
```

#### 3. Selectors and Performance

```typescript
// ✅ Good: Use selectors to prevent unnecessary re-renders
const useUserName = () => useUserStore((state) => state.user?.name);
const useIsLoggedIn = () => useUserStore((state) => !!state.user);

// ✅ Good: Memoized selectors for complex computations
const useExpensiveComputation = () =>
  useDataStore(
    useCallback((state) => state.items.filter((item) => item.active).length, [])
  );

// ❌ Avoid: Selecting entire state when you only need part of it
const BadComponent = () => {
  const entireState = useUserStore(); // Re-renders on any state change
  return <div>{entireState.user?.name}</div>;
};
```

#### 4. Middleware Usage

```typescript
// ✅ Good: Use middleware appropriately
const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Store implementation
      }),
      {
        name: "app-storage",
        // Only persist necessary data
        partialize: (state) => ({
          user: state.user,
          preferences: state.preferences,
        }),
      }
    ),
    {
      name: "app-store",
      // Enable only in development
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
```

### Integration Best Practices

#### 1. Type Safety

```typescript
// ✅ Good: Full type safety from API to UI
const ApiUserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
});

type ApiUser = z.infer<typeof ApiUserSchema>;

interface UserState {
  users: ApiUser[];
  addUser: (user: Omit<ApiUser, "id">) => Promise<void>;
}

const useUserStore = create<UserState>((set) => ({
  users: [],
  addUser: async (userData) => {
    // Validate input
    const validatedData = ApiUserSchema.omit({ id: true }).parse(userData);

    // API call with validated data
    const response = await fetch("/api/users", {
      method: "POST",
      body: JSON.stringify(validatedData),
    });

    // Validate response
    const newUser = ApiUserSchema.parse(await response.json());

    set((state) => ({ users: [...state.users, newUser] }));
  },
}));
```

#### 2. Error Boundaries

```typescript
// Error boundary for Zod validation errors
class ValidationErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    if (error instanceof z.ZodError) {
      return { hasError: true, error };
    }
    return null;
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Validation Error</h2>
          <pre>{JSON.stringify(this.state.error.issues, null, 2)}</pre>
        </div>
      );
    }

    return this.props.children;
  }
}
```

#### 3. Testing

```typescript
// ✅ Good: Test schemas and stores separately
describe("UserSchema", () => {
  it("should validate correct user data", () => {
    const validUser = {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
    };

    expect(() => UserSchema.parse(validUser)).not.toThrow();
  });

  it("should reject invalid email", () => {
    const invalidUser = {
      id: 1,
      name: "John Doe",
      email: "invalid-email",
    };

    expect(() => UserSchema.parse(invalidUser)).toThrow();
  });
});

describe("useUserStore", () => {
  beforeEach(() => {
    useUserStore.setState({ users: [], isLoading: false, error: null });
  });

  it("should add user successfully", async () => {
    const { addUser } = useUserStore.getState();

    await addUser({ name: "John", email: "<EMAIL>" });

    const { users } = useUserStore.getState();
    expect(users).toHaveLength(1);
    expect(users[0].name).toBe("John");
  });
});
```

## Summary

**Zod** provides:

- Runtime type validation
- TypeScript type inference
- Comprehensive error handling
- Data transformation capabilities

**Zustand** provides:

- Simple state management
- Excellent TypeScript support
- Minimal boilerplate
- Great performance

**Together they create**:

- Type-safe applications from API to UI
- Robust error handling
- Maintainable code architecture
- Excellent developer experience

Start with simple examples and gradually incorporate more advanced features as your application grows. Both libraries have excellent documentation and active communities for further learning.

## Installation Commands

```bash
# Install Zod
npm install zod

# Install Zustand
npm install zustand

# Install both together
npm install zod zustand

# For persistence and devtools (optional)
npm install zustand
```

Remember to enable strict mode in your TypeScript configuration for the best experience with both libraries!
