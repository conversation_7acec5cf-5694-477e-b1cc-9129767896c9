# Zod and Zustand: Complete Beginner's Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Zod - Schema Validation](#zod---schema-validation)
3. [Zustand - State Management](#zustand---state-management)
4. [Using Zod and Zustand Together](#using-zod-and-zustand-together)
5. [Real-World Examples](#real-world-examples)
6. [Best Practices](#best-practices)

## Introduction

This guide covers two essential libraries for modern React/TypeScript development:

- **Zod**: A TypeScript-first schema validation library
- **Zustand**: A lightweight state management solution

Both libraries are designed to be simple, type-safe, and developer-friendly.

## Zod - Schema Validation

### What is Zod?

Zod is a TypeScript-first schema validation library that helps you:

- Validate data at runtime
- Generate TypeScript types automatically
- Parse and transform data
- Handle errors gracefully

### Installation

```bash
npm install zod
# or
yarn add zod
# or
pnpm add zod
```

### Basic Zod Examples

#### 1. Simple Schema Definition

```typescript
import { z } from "zod";

// Define a schema
const UserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  age: z.number().min(0).max(120),
  isActive: z.boolean().default(true),
});

// Generate TypeScript type from schema
type User = z.infer<typeof UserSchema>;
// This creates: { id: number; name: string; email: string; age: number; isActive: boolean; }
```

#### 2. Data Validation

```typescript
// Valid data
const validUser = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
  isActive: true,
};

// Validate data
try {
  const user = UserSchema.parse(validUser);
  console.log("Valid user:", user);
} catch (error) {
  console.error("Validation failed:", error);
}

// Safe parsing (doesn't throw)
const result = UserSchema.safeParse(validUser);
if (result.success) {
  console.log("Valid user:", result.data);
} else {
  console.error("Validation errors:", result.error.issues);
}
```

#### 3. Advanced Schema Features

```typescript
// Optional and nullable fields
const ProfileSchema = z.object({
  username: z.string(),
  bio: z.string().optional(), // Can be undefined
  avatar: z.string().nullable(), // Can be null
  settings: z
    .object({
      theme: z.enum(["light", "dark"]),
      notifications: z.boolean().default(true),
    })
    .optional(),
});

// Arrays and refinements
const PostSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(10, "Content must be at least 10 characters"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  publishedAt: z.date().optional(),
  slug: z.string().regex(/^[a-z0-9-]+$/, "Invalid slug format"),
});

// Custom validation
const PasswordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain uppercase letter")
  .regex(/[a-z]/, "Password must contain lowercase letter")
  .regex(/[0-9]/, "Password must contain number");
```

#### 4. Form Validation Example

```typescript
// Form schema
const ContactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  phone: z.string().optional(),
});

type ContactForm = z.infer<typeof ContactFormSchema>;

// Usage in React component
function ContactForm() {
  const [formData, setFormData] = useState<Partial<ContactForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const result = ContactFormSchema.safeParse(formData);

    if (!result.success) {
      const newErrors: Record<string, string> = {};
      result.error.issues.forEach((issue) => {
        if (issue.path[0]) {
          newErrors[issue.path[0] as string] = issue.message;
        }
      });
      setErrors(newErrors);
      return;
    }

    // Form is valid, submit data
    console.log("Valid form data:", result.data);
    setErrors({});
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Name"
        value={formData.name || ""}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      />
      {errors.name && <span className="error">{errors.name}</span>}

      <input
        type="email"
        placeholder="Email"
        value={formData.email || ""}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      />
      {errors.email && <span className="error">{errors.email}</span>}

      <textarea
        placeholder="Message"
        value={formData.message || ""}
        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
      />
      {errors.message && <span className="error">{errors.message}</span>}

      <button type="submit">Submit</button>
    </form>
  );
}
```

## Zustand - State Management

### What is Zustand?

Zustand is a lightweight state management library that provides:

- Simple API with minimal boilerplate
- TypeScript support out of the box
- No providers or context needed
- Excellent performance
- DevTools integration

### Installation

```bash
npm install zustand
# or
yarn add zustand
# or
pnpm add zustand
```

### Basic Zustand Examples

#### 1. Simple Counter Store

```typescript
import { create } from "zustand";

// Define store interface
interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

// Create store
const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));

// Usage in React component
function Counter() {
  const { count, increment, decrement, reset } = useCounterStore();

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}
```

#### 2. Todo List Store

```typescript
interface Todo {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

interface TodoState {
  todos: Todo[];
  addTodo: (text: string) => void;
  toggleTodo: (id: string) => void;
  deleteTodo: (id: string) => void;
  clearCompleted: () => void;
}

const useTodoStore = create<TodoState>((set) => ({
  todos: [],

  addTodo: (text: string) =>
    set((state) => ({
      todos: [
        ...state.todos,
        {
          id: crypto.randomUUID(),
          text,
          completed: false,
          createdAt: new Date(),
        },
      ],
    })),

  toggleTodo: (id: string) =>
    set((state) => ({
      todos: state.todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      ),
    })),

  deleteTodo: (id: string) =>
    set((state) => ({
      todos: state.todos.filter((todo) => todo.id !== id),
    })),

  clearCompleted: () =>
    set((state) => ({
      todos: state.todos.filter((todo) => !todo.completed),
    })),
}));

// Usage in React component
function TodoList() {
  const { todos, addTodo, toggleTodo, deleteTodo, clearCompleted } =
    useTodoStore();
  const [inputText, setInputText] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim()) {
      addTodo(inputText.trim());
      setInputText("");
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Add a todo..."
        />
        <button type="submit">Add</button>
      </form>

      <ul>
        {todos.map((todo) => (
          <li key={todo.id}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span
              style={{
                textDecoration: todo.completed ? "line-through" : "none",
              }}
            >
              {todo.text}
            </span>
            <button onClick={() => deleteTodo(todo.id)}>Delete</button>
          </li>
        ))}
      </ul>

      <button onClick={clearCompleted}>Clear Completed</button>
    </div>
  );
}
```

#### 3. Advanced Zustand Features

```typescript
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Store with persistence and devtools
interface UserState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => void;
}

const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        isLoading: false,
        error: null,

        login: async (email: string, password: string) => {
          set({ isLoading: true, error: null });
          try {
            // Simulate API call
            const response = await fetch("/api/login", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ email, password }),
            });

            if (!response.ok) throw new Error("Login failed");

            const user = await response.json();
            set({ user, isLoading: false });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : "Login failed",
              isLoading: false,
            });
          }
        },

        logout: () => {
          set({ user: null, error: null });
        },

        updateProfile: (updates: Partial<User>) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, ...updates } });
          }
        },
      }),
      {
        name: "user-storage", // localStorage key
        partialize: (state) => ({ user: state.user }), // Only persist user data
      }
    ),
    {
      name: "user-store", // DevTools name
    }
  )
);
```

#### 4. Computed Values and Selectors

```typescript
// Store with computed values
interface ShoppingCartState {
  items: CartItem[];
  addItem: (product: Product, quantity: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
}

interface CartItem {
  product: Product;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
  price: number;
}

const useShoppingCartStore = create<ShoppingCartState>((set) => ({
  items: [],

  addItem: (product: Product, quantity: number) =>
    set((state) => {
      const existingItem = state.items.find(
        (item) => item.product.id === product.id
      );

      if (existingItem) {
        return {
          items: state.items.map((item) =>
            item.product.id === product.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          ),
        };
      }

      return {
        items: [...state.items, { product, quantity }],
      };
    }),

  removeItem: (productId: string) =>
    set((state) => ({
      items: state.items.filter((item) => item.product.id !== productId),
    })),

  updateQuantity: (productId: string, quantity: number) =>
    set((state) => ({
      items: state.items.map((item) =>
        item.product.id === productId ? { ...item, quantity } : item
      ),
    })),

  clearCart: () => set({ items: [] }),
}));

// Computed selectors (use outside the store)
const useCartTotal = () => {
  return useShoppingCartStore((state) =>
    state.items.reduce(
      (total, item) => total + item.product.price * item.quantity,
      0
    )
  );
};

const useCartItemCount = () => {
  return useShoppingCartStore((state) =>
    state.items.reduce((count, item) => count + item.quantity, 0)
  );
};

// Usage in component
function ShoppingCart() {
  const { items, addItem, removeItem, updateQuantity, clearCart } =
    useShoppingCartStore();
  const total = useCartTotal();
  const itemCount = useCartItemCount();

  return (
    <div>
      <h2>Shopping Cart ({itemCount} items)</h2>
      {items.map((item) => (
        <div key={item.product.id}>
          <span>{item.product.name}</span>
          <span>${item.product.price}</span>
          <input
            type="number"
            value={item.quantity}
            onChange={(e) =>
              updateQuantity(item.product.id, parseInt(e.target.value))
            }
          />
          <button onClick={() => removeItem(item.product.id)}>Remove</button>
        </div>
      ))}
      <div>Total: ${total.toFixed(2)}</div>
      <button onClick={clearCart}>Clear Cart</button>
    </div>
  );
}
```

## Using Zod and Zustand Together

### API Response Validation with Zustand

```typescript
import { z } from "zod";
import { create } from "zustand";

// Define API response schemas
const ApiUserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  avatar: z.string().url().optional(),
  createdAt: z.string().datetime(),
});

const ApiResponseSchema = z.object({
  data: z.array(ApiUserSchema),
  meta: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
  }),
});

type ApiUser = z.infer<typeof ApiUserSchema>;
type ApiResponse = z.infer<typeof ApiResponseSchema>;

// Store with validated API calls
interface UsersState {
  users: ApiUser[];
  isLoading: boolean;
  error: string | null;
  fetchUsers: () => Promise<void>;
  addUser: (userData: Omit<ApiUser, "id" | "createdAt">) => Promise<void>;
}

const useUsersStore = create<UsersState>((set, get) => ({
  users: [],
  isLoading: false,
  error: null,

  fetchUsers: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch("/api/users");
      const rawData = await response.json();

      // Validate API response with Zod
      const validatedData = ApiResponseSchema.parse(rawData);

      set({
        users: validatedData.data,
        isLoading: false,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("API response validation failed:", error.issues);
        set({
          error: "Invalid data received from server",
          isLoading: false,
        });
      } else {
        set({
          error:
            error instanceof Error ? error.message : "Failed to fetch users",
          isLoading: false,
        });
      }
    }
  },

  addUser: async (userData) => {
    try {
      // Validate input data
      const validatedInput = ApiUserSchema.omit({
        id: true,
        createdAt: true,
      }).parse(userData);

      const response = await fetch("/api/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(validatedInput),
      });

      const rawData = await response.json();
      const newUser = ApiUserSchema.parse(rawData);

      set((state) => ({
        users: [...state.users, newUser],
      }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("Validation failed:", error.issues);
        set({ error: "Invalid user data" });
      } else {
        set({ error: "Failed to add user" });
      }
    }
  },
}));
```

### Form State Management with Validation

```typescript
import { z } from "zod";
import { create } from "zustand";

// Form schema
const ProfileFormSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  age: z
    .number()
    .min(18, "Must be at least 18 years old")
    .max(100, "Must be less than 100"),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  interests: z.array(z.string()).min(1, "Select at least one interest"),
});

type ProfileFormData = z.infer<typeof ProfileFormSchema>;
type FormErrors = Partial<Record<keyof ProfileFormData, string>>;

interface ProfileFormState {
  formData: Partial<ProfileFormData>;
  errors: FormErrors;
  isSubmitting: boolean;
  isValid: boolean;
  updateField: <K extends keyof ProfileFormData>(
    field: K,
    value: ProfileFormData[K]
  ) => void;
  validateForm: () => boolean;
  submitForm: () => Promise<void>;
  resetForm: () => void;
}

const useProfileFormStore = create<ProfileFormState>((set, get) => ({
  formData: {},
  errors: {},
  isSubmitting: false,
  isValid: false,

  updateField: (field, value) => {
    set((state) => {
      const newFormData = { ...state.formData, [field]: value };

      // Clear field error when user starts typing
      const newErrors = { ...state.errors };
      delete newErrors[field];

      return {
        formData: newFormData,
        errors: newErrors,
      };
    });
  },

  validateForm: () => {
    const { formData } = get();
    const result = ProfileFormSchema.safeParse(formData);

    if (result.success) {
      set({ errors: {}, isValid: true });
      return true;
    } else {
      const newErrors: FormErrors = {};
      result.error.issues.forEach((issue) => {
        const field = issue.path[0] as keyof ProfileFormData;
        if (field) {
          newErrors[field] = issue.message;
        }
      });

      set({ errors: newErrors, isValid: false });
      return false;
    }
  },

  submitForm: async () => {
    const { formData, validateForm } = get();

    if (!validateForm()) {
      return;
    }

    set({ isSubmitting: true });

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Form submitted successfully:", formData);

      // Reset form after successful submission
      set({
        formData: {},
        errors: {},
        isSubmitting: false,
        isValid: false,
      });
    } catch (error) {
      set({
        errors: { email: "Failed to submit form. Please try again." },
        isSubmitting: false,
      });
    }
  },

  resetForm: () => {
    set({
      formData: {},
      errors: {},
      isSubmitting: false,
      isValid: false,
    });
  },
}));

// Usage in React component
function ProfileForm() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateForm,
    submitForm,
    resetForm,
  } = useProfileFormStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    submitForm();
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <input
          type="text"
          placeholder="First Name"
          value={formData.firstName || ""}
          onChange={(e) => updateField("firstName", e.target.value)}
          onBlur={validateForm}
        />
        {errors.firstName && <span className="error">{errors.firstName}</span>}
      </div>

      <div>
        <input
          type="text"
          placeholder="Last Name"
          value={formData.lastName || ""}
          onChange={(e) => updateField("lastName", e.target.value)}
          onBlur={validateForm}
        />
        {errors.lastName && <span className="error">{errors.lastName}</span>}
      </div>

      <div>
        <input
          type="email"
          placeholder="Email"
          value={formData.email || ""}
          onChange={(e) => updateField("email", e.target.value)}
          onBlur={validateForm}
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>

      <div>
        <input
          type="number"
          placeholder="Age"
          value={formData.age || ""}
          onChange={(e) => updateField("age", parseInt(e.target.value))}
          onBlur={validateForm}
        />
        {errors.age && <span className="error">{errors.age}</span>}
      </div>

      <div>
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
        <button type="button" onClick={resetForm}>
          Reset
        </button>
      </div>
    </form>
  );
}
```

## Real-World Examples

### E-commerce Product Management

```typescript
import { z } from "zod";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Product schemas
const ProductSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
  category: z.enum(["electronics", "clothing", "books", "home"]),
  inStock: z.boolean(),
  images: z.array(z.string().url()).min(1, "At least one image is required"),
  tags: z.array(z.string()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const ProductFiltersSchema = z.object({
  category: z.enum(["electronics", "clothing", "books", "home"]).optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  inStock: z.boolean().optional(),
  searchTerm: z.string().optional(),
});

type Product = z.infer<typeof ProductSchema>;
type ProductFilters = z.infer<typeof ProductFiltersSchema>;

interface ProductState {
  products: Product[];
  filteredProducts: Product[];
  filters: ProductFilters;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProducts: () => Promise<void>;
  addProduct: (
    productData: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => Promise<void>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  setFilters: (filters: ProductFilters) => void;
  clearFilters: () => void;
}

const useProductStore = create<ProductState>()(
  devtools(
    persist(
      (set, get) => ({
        products: [],
        filteredProducts: [],
        filters: {},
        isLoading: false,
        error: null,

        fetchProducts: async () => {
          set({ isLoading: true, error: null });

          try {
            const response = await fetch("/api/products");
            const rawData = await response.json();

            // Validate each product
            const validatedProducts = rawData.map((item: unknown) =>
              ProductSchema.parse(item)
            );

            set({
              products: validatedProducts,
              filteredProducts: validatedProducts,
              isLoading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to fetch products",
              isLoading: false,
            });
          }
        },

        addProduct: async (productData) => {
          try {
            // Validate input
            const validatedData = ProductSchema.omit({
              id: true,
              createdAt: true,
              updatedAt: true,
            }).parse(productData);

            const newProduct: Product = {
              ...validatedData,
              id: crypto.randomUUID(),
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            // Simulate API call
            const response = await fetch("/api/products", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(newProduct),
            });

            if (!response.ok) throw new Error("Failed to add product");

            set((state) => {
              const updatedProducts = [...state.products, newProduct];
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to add product",
            });
          }
        },

        updateProduct: async (id, updates) => {
          try {
            const currentProduct = get().products.find((p) => p.id === id);
            if (!currentProduct) throw new Error("Product not found");

            const updatedProduct = {
              ...currentProduct,
              ...updates,
              updatedAt: new Date(),
            };

            // Validate updated product
            ProductSchema.parse(updatedProduct);

            // Simulate API call
            const response = await fetch(`/api/products/${id}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updatedProduct),
            });

            if (!response.ok) throw new Error("Failed to update product");

            set((state) => {
              const updatedProducts = state.products.map((p) =>
                p.id === id ? updatedProduct : p
              );
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to update product",
            });
          }
        },

        deleteProduct: async (id) => {
          try {
            const response = await fetch(`/api/products/${id}`, {
              method: "DELETE",
            });

            if (!response.ok) throw new Error("Failed to delete product");

            set((state) => {
              const updatedProducts = state.products.filter((p) => p.id !== id);
              return {
                products: updatedProducts,
                filteredProducts: applyFilters(updatedProducts, state.filters),
              };
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to delete product",
            });
          }
        },

        setFilters: (filters) => {
          try {
            const validatedFilters = ProductFiltersSchema.parse(filters);

            set((state) => ({
              filters: validatedFilters,
              filteredProducts: applyFilters(state.products, validatedFilters),
            }));
          } catch (error) {
            console.error("Invalid filters:", error);
          }
        },

        clearFilters: () => {
          set((state) => ({
            filters: {},
            filteredProducts: state.products,
          }));
        },
      }),
      {
        name: "product-storage",
        partialize: (state) => ({
          products: state.products,
          filters: state.filters,
        }),
      }
    ),
    { name: "product-store" }
  )
);

// Helper function to apply filters
function applyFilters(products: Product[], filters: ProductFilters): Product[] {
  return products.filter((product) => {
    if (filters.category && product.category !== filters.category) return false;
    if (filters.minPrice && product.price < filters.minPrice) return false;
    if (filters.maxPrice && product.price > filters.maxPrice) return false;
    if (filters.inStock !== undefined && product.inStock !== filters.inStock)
      return false;
    if (
      filters.searchTerm &&
      !product.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
    )
      return false;

    return true;
  });
}

// Usage in React component
function ProductList() {
  const {
    filteredProducts,
    filters,
    isLoading,
    error,
    fetchProducts,
    setFilters,
    clearFilters,
  } = useProductStore();

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {
    setFilters({ ...filters, ...newFilters });
  };

  if (isLoading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <div className="filters">
        <select
          value={filters.category || ""}
          onChange={(e) =>
            handleFilterChange({ category: e.target.value as any })
          }
        >
          <option value="">All Categories</option>
          <option value="electronics">Electronics</option>
          <option value="clothing">Clothing</option>
          <option value="books">Books</option>
          <option value="home">Home</option>
        </select>

        <input
          type="number"
          placeholder="Min Price"
          value={filters.minPrice || ""}
          onChange={(e) =>
            handleFilterChange({ minPrice: parseFloat(e.target.value) })
          }
        />

        <input
          type="number"
          placeholder="Max Price"
          value={filters.maxPrice || ""}
          onChange={(e) =>
            handleFilterChange({ maxPrice: parseFloat(e.target.value) })
          }
        />

        <button onClick={clearFilters}>Clear Filters</button>
      </div>

      <div className="products">
        {filteredProducts.map((product) => (
          <div key={product.id} className="product-card">
            <h3>{product.name}</h3>
            <p>${product.price}</p>
            <p>Category: {product.category}</p>
            <p>In Stock: {product.inStock ? "Yes" : "No"}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Best Practices

### Zod Best Practices

#### 1. Schema Organization

```typescript
// ✅ Good: Organize schemas in separate files
// schemas/user.ts
export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
});

// schemas/product.ts
export const ProductSchema = z.object({
  id: z.string(),
  name: z.string(),
  price: z.number().positive(),
});

// schemas/index.ts
export * from "./user";
export * from "./product";
```

#### 2. Reusable Schema Patterns

```typescript
// Common validation patterns
const EmailSchema = z.string().email();
const PasswordSchema = z
  .string()
  .min(8)
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/);
const PhoneSchema = z.string().regex(/^\+?[\d\s-()]+$/);

// Base schemas for extension
const BaseEntitySchema = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const UserSchema = BaseEntitySchema.extend({
  name: z.string(),
  email: EmailSchema,
  password: PasswordSchema,
});
```

#### 3. Error Handling

```typescript
// ✅ Good: Comprehensive error handling
function validateUserData(data: unknown) {
  const result = UserSchema.safeParse(data);

  if (!result.success) {
    const errors = result.error.issues.map((issue) => ({
      field: issue.path.join("."),
      message: issue.message,
      code: issue.code,
    }));

    return { success: false, errors };
  }

  return { success: true, data: result.data };
}

// ✅ Good: Custom error messages
const UserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  age: z.number().min(18, "Must be 18 or older"),
  email: z.string().email("Please enter a valid email address"),
});
```

#### 4. Transform and Preprocess

```typescript
// Transform data during validation
const UserInputSchema = z.object({
  name: z.string().trim().toLowerCase(),
  age: z.string().transform((val) => parseInt(val, 10)),
  email: z.string().email().toLowerCase(),
  tags: z.string().transform((val) => val.split(",").map((tag) => tag.trim())),
});

// Preprocess data before validation
const DateSchema = z.preprocess((val) => {
  if (typeof val === "string") return new Date(val);
  return val;
}, z.date());
```

### Zustand Best Practices

#### 1. Store Organization

```typescript
// ✅ Good: Separate concerns into different stores
// stores/authStore.ts
export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  login: async (credentials) => {
    /* ... */
  },
  logout: () => {
    /* ... */
  },
}));

// stores/cartStore.ts
export const useCartStore = create<CartState>((set) => ({
  items: [],
  addItem: (item) => {
    /* ... */
  },
  removeItem: (id) => {
    /* ... */
  },
}));

// ❌ Avoid: One massive store for everything
```

#### 2. Action Patterns

```typescript
// ✅ Good: Async actions with proper error handling
const useDataStore = create<DataState>((set, get) => ({
  data: [],
  isLoading: false,
  error: null,

  fetchData: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await api.getData();
      set({ data: response, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  // ✅ Good: Optimistic updates
  updateItem: async (id, updates) => {
    // Optimistically update UI
    set((state) => ({
      data: state.data.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));

    try {
      await api.updateItem(id, updates);
    } catch (error) {
      // Revert on error
      get().fetchData();
      set({ error: "Failed to update item" });
    }
  },
}));
```

#### 3. Selectors and Performance

```typescript
// ✅ Good: Use selectors to prevent unnecessary re-renders
const useUserName = () => useUserStore((state) => state.user?.name);
const useIsLoggedIn = () => useUserStore((state) => !!state.user);

// ✅ Good: Memoized selectors for complex computations
const useExpensiveComputation = () =>
  useDataStore(
    useCallback((state) => state.items.filter((item) => item.active).length, [])
  );

// ❌ Avoid: Selecting entire state when you only need part of it
const BadComponent = () => {
  const entireState = useUserStore(); // Re-renders on any state change
  return <div>{entireState.user?.name}</div>;
};
```

#### 4. Middleware Usage

```typescript
// ✅ Good: Use middleware appropriately
const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Store implementation
      }),
      {
        name: "app-storage",
        // Only persist necessary data
        partialize: (state) => ({
          user: state.user,
          preferences: state.preferences,
        }),
      }
    ),
    {
      name: "app-store",
      // Enable only in development
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
```

### Integration Best Practices

#### 1. Type Safety

```typescript
// ✅ Good: Full type safety from API to UI
const ApiUserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
});

type ApiUser = z.infer<typeof ApiUserSchema>;

interface UserState {
  users: ApiUser[];
  addUser: (user: Omit<ApiUser, "id">) => Promise<void>;
}

const useUserStore = create<UserState>((set) => ({
  users: [],
  addUser: async (userData) => {
    // Validate input
    const validatedData = ApiUserSchema.omit({ id: true }).parse(userData);

    // API call with validated data
    const response = await fetch("/api/users", {
      method: "POST",
      body: JSON.stringify(validatedData),
    });

    // Validate response
    const newUser = ApiUserSchema.parse(await response.json());

    set((state) => ({ users: [...state.users, newUser] }));
  },
}));
```

#### 2. Error Boundaries

```typescript
// Error boundary for Zod validation errors
class ValidationErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    if (error instanceof z.ZodError) {
      return { hasError: true, error };
    }
    return null;
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Validation Error</h2>
          <pre>{JSON.stringify(this.state.error.issues, null, 2)}</pre>
        </div>
      );
    }

    return this.props.children;
  }
}
```

#### 3. Testing

```typescript
// ✅ Good: Test schemas and stores separately
describe("UserSchema", () => {
  it("should validate correct user data", () => {
    const validUser = {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
    };

    expect(() => UserSchema.parse(validUser)).not.toThrow();
  });

  it("should reject invalid email", () => {
    const invalidUser = {
      id: 1,
      name: "John Doe",
      email: "invalid-email",
    };

    expect(() => UserSchema.parse(invalidUser)).toThrow();
  });
});

describe("useUserStore", () => {
  beforeEach(() => {
    useUserStore.setState({ users: [], isLoading: false, error: null });
  });

  it("should add user successfully", async () => {
    const { addUser } = useUserStore.getState();

    await addUser({ name: "John", email: "<EMAIL>" });

    const { users } = useUserStore.getState();
    expect(users).toHaveLength(1);
    expect(users[0].name).toBe("John");
  });
});
```

## Summary

**Zod** provides:

- Runtime type validation
- TypeScript type inference
- Comprehensive error handling
- Data transformation capabilities

**Zustand** provides:

- Simple state management
- Excellent TypeScript support
- Minimal boilerplate
- Great performance

**Together they create**:

- Type-safe applications from API to UI
- Robust error handling
- Maintainable code architecture
- Excellent developer experience

Start with simple examples and gradually incorporate more advanced features as your application grows. Both libraries have excellent documentation and active communities for further learning.

## Installation Commands

```bash
# Install Zod
npm install zod

# Install Zustand
npm install zustand

# Install both together
npm install zod zustand

# For persistence and devtools (optional)
npm install zustand
```

Remember to enable strict mode in your TypeScript configuration for the best experience with both libraries!

---

## Applying Zustand to TripListView Component

### Current Implementation Analysis

Your current `TripListView` component uses local state and direct fetch calls:

```typescript
// Current implementation in TripListView.tsx
export default function TripListView() {
  const [data, setData] = useState<Trip[]>([]);

  useEffect(() => {
    fetch("/data/trips.json")
      .then((res) => res.json())
      .then(setData)
      .catch(console.error);
  }, []);

  return (
    <div>
      <DataTable<Trip>
        keyField="id"
        data={data}
        // ... other props
      />
    </div>
  );
}
```

### Problems with Current Approach

1. **State Duplication**: Each component instance fetches and stores its own data
2. **No Caching**: Data is refetched every time component mounts
3. **No Error Handling**: Basic error handling with console.error
4. **No Loading States**: No proper loading indicators
5. **No Data Synchronization**: Changes in one component don't reflect in others
6. **No Optimistic Updates**: No immediate UI feedback for actions

### Zustand Solution Architecture

#### 1. Create Trip Store with Zod Validation

```typescript
// stores/tripStore.ts
import { z } from "zod";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Define Zod schemas based on your existing Trip interface
const TripDriverSchema = z.object({
  driverId: z.string(),
  driverName: z.string(),
  driverPassportNumber: z.string(),
  driverNationality: z.string(),
  driverContactNo: z.string().optional(),
});

const TripVehicleSchema = z.object({
  vehicleId: z.string(),
  vehiclePlateNumber: z.string(),
  trackerNo: z.string(),
  model: z.string().optional(),
  color: z.string().optional(),
  type: z.string().optional(),
  plateCountry: z.string().optional(),
});

const TripRouteSchema = z.object({
  routeId: z.string(),
  routeName: z.string(),
  entryPort: z.string(),
  exitPort: z.string(),
});

const TripShipmentSchema = z.object({
  shipmentId: z.string(),
  shipmentDescription: z.string(),
  ownerDescription: z.string().optional(),
  shipmentDescriptionArabic: z.string().optional(),
});

const TripTrackingSchema = z.object({
  currentLocation: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
      timestamp: z.string(),
    })
    .optional(),
  lastKnownLocation: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
      timestamp: z.string(),
    })
    .optional(),
  batteryLevel: z.number().optional(),
  signalStrength: z.number().optional(),
});

const TripComplianceSchema = z.object({
  customsStatus: z.string().optional(),
  documentsStatus: z.string().optional(),
  securityStatus: z.string().optional(),
});

// Main Trip schema
const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),
  status: z.array(z.enum(["active", "charging", "offline"])),
  tripId: z.string(),
  tripStatus: z.enum(["pending", "activated", "ended", "cancelled"]),
  transitType: z.string(),
  creationDate: z.string(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),
  transitSeqNo: z.string().optional(),
  declarationDate: z.string().optional(),
  startingDate: z.string().optional(),
  expectedArrivalDate: z.string().optional(),
  endDate: z.string().optional(),
  driver_details: TripDriverSchema,
  vehicle_details: TripVehicleSchema,
  route: TripRouteSchema,
  shipment: TripShipmentSchema,
  tracking: TripTrackingSchema,
  compliance: TripComplianceSchema,
});

// API Response schema
const TripsApiResponseSchema = z.array(TripSchema);

type Trip = z.infer<typeof TripSchema>;
type TripStatus = Trip["tripStatus"];

// Store interface
interface TripState {
  // Data
  trips: Trip[];
  filteredTrips: Trip[];
  selectedTrip: Trip | null;

  // UI State
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number | null;

  // Filters
  filters: {
    status?: TripStatus;
    transitType?: string;
    searchTerm?: string;
    dateRange?: {
      start: string;
      end: string;
    };
  };

  // Pagination
  pagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
  };

  // Actions
  fetchTrips: () => Promise<void>;
  refreshTrips: () => Promise<void>;
  updateTripStatus: (tripId: string, status: TripStatus) => Promise<void>;
  selectTrip: (trip: Trip | null) => void;
  setFilters: (filters: Partial<TripState["filters"]>) => void;
  clearFilters: () => void;
  setPagination: (pagination: Partial<TripState["pagination"]>) => void;
  clearError: () => void;
}

// Create the store
export const useTripStore = create<TripState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        trips: [],
        filteredTrips: [],
        selectedTrip: null,
        isLoading: false,
        error: null,
        lastFetchTime: null,
        filters: {},
        pagination: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0,
        },

        // Actions
        fetchTrips: async () => {
          const state = get();

          // Avoid unnecessary fetches (cache for 5 minutes)
          const now = Date.now();
          if (
            state.lastFetchTime &&
            now - state.lastFetchTime < 5 * 60 * 1000
          ) {
            return;
          }

          set({ isLoading: true, error: null });

          try {
            const response = await fetch("/data/trips.json");

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const rawData = await response.json();

            // Validate data with Zod
            const validatedTrips = TripsApiResponseSchema.parse(rawData);

            set((state) => ({
              trips: validatedTrips,
              filteredTrips: applyFilters(validatedTrips, state.filters),
              isLoading: false,
              lastFetchTime: now,
              pagination: {
                ...state.pagination,
                totalItems: validatedTrips.length,
              },
            }));

            console.log(`Fetched ${validatedTrips.length} trips successfully`);
          } catch (error) {
            const errorMessage =
              error instanceof z.ZodError
                ? `Data validation failed: ${error.issues
                    .map((i) => i.message)
                    .join(", ")}`
                : error instanceof Error
                ? error.message
                : "Failed to fetch trips";

            set({
              error: errorMessage,
              isLoading: false,
            });

            console.error("Trip fetch error:", error);
          }
        },

        refreshTrips: async () => {
          set({ lastFetchTime: null });
          await get().fetchTrips();
        },

        updateTripStatus: async (tripId: string, status: TripStatus) => {
          set({ isLoading: true, error: null });

          try {
            // Optimistic update
            set((state) => ({
              trips: state.trips.map((trip) =>
                trip.tripId === tripId ? { ...trip, tripStatus: status } : trip
              ),
            }));

            // Simulate API call
            const response = await fetch(`/api/trips/${tripId}/status`, {
              method: "PATCH",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ status }),
            });

            if (!response.ok) {
              throw new Error("Failed to update trip status");
            }

            // Re-apply filters after update
            const state = get();
            set({
              filteredTrips: applyFilters(state.trips, state.filters),
              isLoading: false,
            });

            console.log(`Trip ${tripId} status updated to ${status}`);
          } catch (error) {
            // Revert optimistic update on error
            await get().fetchTrips();

            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to update trip",
              isLoading: false,
            });
          }
        },

        selectTrip: (trip: Trip | null) => {
          set({ selectedTrip: trip });
        },

        setFilters: (newFilters: Partial<TripState["filters"]>) => {
          set((state) => {
            const updatedFilters = { ...state.filters, ...newFilters };
            return {
              filters: updatedFilters,
              filteredTrips: applyFilters(state.trips, updatedFilters),
              pagination: {
                ...state.pagination,
                currentPage: 1, // Reset to first page when filtering
              },
            };
          });
        },

        clearFilters: () => {
          set((state) => ({
            filters: {},
            filteredTrips: state.trips,
            pagination: {
              ...state.pagination,
              currentPage: 1,
            },
          }));
        },

        setPagination: (newPagination: Partial<TripState["pagination"]>) => {
          set((state) => ({
            pagination: { ...state.pagination, ...newPagination },
          }));
        },

        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: "trip-storage",
        partialize: (state) => ({
          trips: state.trips,
          filters: state.filters,
          pagination: state.pagination,
          lastFetchTime: state.lastFetchTime,
        }),
      }
    ),
    {
      name: "trip-store",
    }
  )
);

// Helper function to apply filters
function applyFilters(trips: Trip[], filters: TripState["filters"]): Trip[] {
  return trips.filter((trip) => {
    // Status filter
    if (filters.status && trip.tripStatus !== filters.status) {
      return false;
    }

    // Transit type filter
    if (filters.transitType && trip.transitType !== filters.transitType) {
      return false;
    }

    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const searchableFields = [
        trip.description,
        trip.driver,
        trip.vehicle,
        trip.tripId,
        trip.transitNumber.toString(),
      ];

      const matches = searchableFields.some((field) =>
        field.toLowerCase().includes(searchLower)
      );

      if (!matches) return false;
    }

    // Date range filter
    if (filters.dateRange) {
      const tripDate = new Date(trip.creationDate);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);

      if (tripDate < startDate || tripDate > endDate) {
        return false;
      }
    }

    return true;
  });
}

// Selector hooks for performance optimization
export const useTripList = () => useTripStore((state) => state.filteredTrips);
export const useTripLoading = () => useTripStore((state) => state.isLoading);
export const useTripError = () => useTripStore((state) => state.error);
export const useTripFilters = () => useTripStore((state) => state.filters);
export const useTripPagination = () =>
  useTripStore((state) => state.pagination);
export const useSelectedTrip = () =>
  useTripStore((state) => state.selectedTrip);

// Action hooks
export const useTripActions = () =>
  useTripStore((state) => ({
    fetchTrips: state.fetchTrips,
    refreshTrips: state.refreshTrips,
    updateTripStatus: state.updateTripStatus,
    selectTrip: state.selectTrip,
    setFilters: state.setFilters,
    clearFilters: state.clearFilters,
    setPagination: state.setPagination,
    clearError: state.clearError,
  }));
```

#### 2. Updated TripListView Component

```typescript
// components/TripListView.tsx - Updated with Zustand
"use client";

import { DataTable } from "@/components/shared/DataTable";
import TripMasterDetailView from "@/components/TripMasterDetailView";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import { useEffect } from "react";
import { Trip } from "@/types/trip";
import {
  useTripList,
  useTripLoading,
  useTripError,
  useTripActions,
  useTripPagination,
} from "@/stores/tripStore";

const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
};

export default function TripListView() {
  // Use Zustand selectors for optimal performance
  const trips = useTripList();
  const isLoading = useTripLoading();
  const error = useTripError();
  const pagination = useTripPagination();

  // Use action hooks
  const { fetchTrips, refreshTrips, clearError, selectTrip } = useTripActions();

  // Fetch trips on component mount
  useEffect(() => {
    fetchTrips();
  }, [fetchTrips]);

  // Handle row selection for master-detail view
  const handleRowClick = (trip: Trip) => {
    selectTrip(trip);
  };

  // Error display component
  const ErrorDisplay = () => (
    <div className="flex items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
      <div className="text-center">
        <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-red-800 mb-2">
          Error Loading Trips
        </h3>
        <p className="text-red-600 mb-4">{error}</p>
        <div className="space-x-2">
          <button
            onClick={refreshTrips}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
          <button
            onClick={clearError}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  );

  // Loading display component
  const LoadingDisplay = () => (
    <div className="flex items-center justify-center p-8">
      <div className="text-center">
        <RefreshCw className="mx-auto h-8 w-8 text-blue-500 animate-spin mb-4" />
        <p className="text-gray-600">Loading trips...</p>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Header with refresh button */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Trip Management ({trips.length} trips)
        </h2>
        <button
          onClick={refreshTrips}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </button>
      </div>

      {/* Error state */}
      {error && <ErrorDisplay />}

      {/* Loading state for initial load */}
      {isLoading && trips.length === 0 && <LoadingDisplay />}

      {/* Data table */}
      {!error && (
        <DataTable<Trip>
          keyField="id"
          data={trips}
          stickyHeader={true}
          expandable={true}
          expandRowByClick={false}
          pageSize={pagination.pageSize}
          filterable={true}
          searchable={true}
          sortable={true}
          selectable={false}
          exportable={true}
          exportFormats={["xlsx"]}
          exportFileNameKey="trip-data"
          loading={isLoading}
          onRowClick={handleRowClick}
          columns={[
            {
              key: "transitNumber",
              title: "Transit Number",
              render: (row) => (
                <span className="text-blue-400 font-medium">
                  {row.transitNumber}
                </span>
              ),
              sortable: true,
            },
            {
              key: "description",
              title: "Shipment Description",
              sortable: true,
            },
            {
              key: "entry",
              title: "Entry-port - Exit-port",
              sortable: true,
            },
            {
              key: "lastSeen",
              title: "Last Seen",
              sortable: true,
            },
            {
              key: "tracker",
              title: "Tracker",
              sortable: true,
            },
            {
              key: "driver",
              title: "Driver Name",
              sortable: true,
            },
            {
              key: "vehicle",
              title: "Vehicle",
              sortable: true,
            },
            {
              key: "alerts",
              title: "Alerts",
              sortable: true,
            },
            {
              key: "tripStatus",
              title: "Trip Status",
              render: (row) => (
                <span
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    row.tripStatus === "activated"
                      ? "bg-green-100 text-green-800"
                      : row.tripStatus === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : row.tripStatus === "ended"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {row.tripStatus}
                </span>
              ),
              sortable: true,
            },
            {
              key: "status",
              title: "Device Status",
              render: (row) => (
                <div className="flex gap-1 items-center">
                  {row.status?.map((s, idx) => (
                    <span key={idx} title={s}>
                      {statusConfig[s]?.icon}
                    </span>
                  ))}
                </div>
              ),
            },
          ]}
          expandedRowRender={(row) => <TripMasterDetailView row={row} />}
        />
      )}

      {/* Empty state */}
      {!isLoading && !error && trips.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No trips found</p>
          <button
            onClick={refreshTrips}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh Data
          </button>
        </div>
      )}
    </div>
  );
}
```

#### 3. Enhanced Trip Filters Component

```typescript
// components/TripFilters.tsx - New component for filtering
"use client";

import { useState } from "react";
import { useTripFilters, useTripActions } from "@/stores/tripStore";
import { Search, Filter, X } from "lucide-react";

export default function TripFilters() {
  const filters = useTripFilters();
  const { setFilters, clearFilters } = useTripActions();

  const [searchTerm, setSearchTerm] = useState(filters.searchTerm || "");
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setFilters({ searchTerm: value || undefined });
  };

  const handleStatusFilter = (status: string) => {
    setFilters({
      status: status === "all" ? undefined : (status as any),
    });
  };

  const handleTransitTypeFilter = (transitType: string) => {
    setFilters({
      transitType: transitType === "all" ? undefined : transitType,
    });
  };

  const handleDateRangeFilter = (start: string, end: string) => {
    if (start && end) {
      setFilters({ dateRange: { start, end } });
    } else {
      setFilters({ dateRange: undefined });
    }
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className="bg-white p-4 rounded-lg border shadow-sm space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Search trips by description, driver, vehicle, or trip ID..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800"
        >
          <Filter className="h-4 w-4" />
          Advanced Filters
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="flex items-center gap-1 text-sm text-red-600 hover:text-red-800"
          >
            <X className="h-4 w-4" />
            Clear All
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trip Status
            </label>
            <select
              value={filters.status || "all"}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="activated">Activated</option>
              <option value="ended">Ended</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          {/* Transit Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transit Type
            </label>
            <select
              value={filters.transitType || "all"}
              onChange={(e) => handleTransitTypeFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="Import Transit">Import Transit</option>
              <option value="Export Transit">Export Transit</option>
              <option value="Domestic Transit">Domestic Transit</option>
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <div className="space-y-2">
              <input
                type="date"
                value={filters.dateRange?.start || ""}
                onChange={(e) =>
                  handleDateRangeFilter(
                    e.target.value,
                    filters.dateRange?.end || ""
                  )
                }
                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="date"
                value={filters.dateRange?.end || ""}
                onChange={(e) =>
                  handleDateRangeFilter(
                    filters.dateRange?.start || "",
                    e.target.value
                  )
                }
                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          {filters.searchTerm && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
              Search: {filters.searchTerm}
              <button onClick={() => handleSearchChange("")}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.status && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
              Status: {filters.status}
              <button onClick={() => handleStatusFilter("all")}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.transitType && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
              Type: {filters.transitType}
              <button onClick={() => handleTransitTypeFilter("all")}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.dateRange && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">
              Date: {filters.dateRange.start} to {filters.dateRange.end}
              <button onClick={() => handleDateRangeFilter("", "")}>
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
}
```

#### 4. Complete Page Implementation

```typescript
// app/trips/page.tsx - Complete page with filters and list
"use client";

import TripListView from "@/components/TripListView";
import TripFilters from "@/components/TripFilters";
import { Suspense } from "react";

export default function TripsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Trip Management</h1>
      </div>

      <Suspense fallback={<div>Loading filters...</div>}>
        <TripFilters />
      </Suspense>

      <Suspense fallback={<div>Loading trips...</div>}>
        <TripListView />
      </Suspense>
    </div>
  );
}
```

#### 5. Updated TripMasterDetailView Integration

```typescript
// components/TripMasterDetailView.tsx - Updated to use Zustand
import TripAlertMapViewer from "./TripAlertMapViewer";
import tripAlertsData from "../data/trip_alerts.json";
import { TripAlert } from "@/types/trip_alert";
import { TripDetailSummary } from "@/components/TripDetailSummary";
import { useSelectedTrip, useTripActions } from "@/stores/tripStore";
import { Trip } from "@/types/trip";
import { useEffect } from "react";

interface TripAlertsData {
  tripAlerts: TripAlert[];
}

interface Props {
  row: Trip;
}

export default function TripMasterDetailView({ row }: Props) {
  const selectedTrip = useSelectedTrip();
  const { selectTrip, updateTripStatus } = useTripActions();
  const tripAlerts = (tripAlertsData as unknown as TripAlertsData).tripAlerts;

  // Update selected trip when row changes
  useEffect(() => {
    if (row && (!selectedTrip || selectedTrip.tripId !== row.tripId)) {
      selectTrip(row);
    }
  }, [row, selectedTrip, selectTrip]);

  // Use selected trip data if available, fallback to row data
  const tripData = selectedTrip || row;

  const handleStatusUpdate = async (newStatus: Trip["tripStatus"]) => {
    try {
      await updateTripStatus(tripData.tripId, newStatus);
    } catch (error) {
      console.error("Failed to update trip status:", error);
    }
  };

  return (
    <div className="master-detail-view grid grid-cols-12 gap-4 p-2">
      {/* Left Sidebar / Detail Panel (3 cols) */}
      <div className="col-span-12 md:col-span-3">
        <div className="text-sm text-gray-600 space-y-1">
          <TripDetailSummary trip={tripData} />

          {/* Status Update Controls */}
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <h4 className="font-medium mb-2">Update Status</h4>
            <div className="space-y-2">
              {(["pending", "activated", "ended", "cancelled"] as const).map(
                (status) => (
                  <button
                    key={status}
                    onClick={() => handleStatusUpdate(status)}
                    disabled={tripData.tripStatus === status}
                    className={`w-full text-left px-2 py-1 rounded text-xs ${
                      tripData.tripStatus === status
                        ? "bg-blue-100 text-blue-800 cursor-not-allowed"
                        : "bg-white hover:bg-gray-100 border"
                    }`}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Map Viewer (9 cols) */}
      <div className="col-span-12 md:col-span-9">
        <TripAlertMapViewer
          tripAlerts={tripAlerts}
          selectedTripId={tripData.tripId}
          className="h-[600px]"
        />
      </div>
    </div>
  );
}
```

### Benefits of Zustand Implementation

#### 1. **Performance Improvements**

- **Selective Subscriptions**: Components only re-render when their specific data changes
- **Caching**: Data is cached for 5 minutes, reducing unnecessary API calls
- **Optimistic Updates**: Immediate UI feedback for better user experience
- **Efficient Filtering**: Filters are applied in the store, not in components

#### 2. **Better State Management**

- **Centralized State**: All trip data is managed in one place
- **Consistent Data**: All components see the same data state
- **Automatic Synchronization**: Changes in one component reflect everywhere
- **Persistent State**: Filters and data persist across page refreshes

#### 3. **Enhanced Error Handling**

- **Zod Validation**: Runtime validation ensures data integrity
- **Graceful Degradation**: Proper error states and recovery mechanisms
- **User-Friendly Messages**: Clear error messages for users
- **Retry Mechanisms**: Easy retry functionality for failed operations

#### 4. **Developer Experience**

- **TypeScript Integration**: Full type safety with Zod schema inference
- **DevTools Support**: Built-in Redux DevTools integration
- **Hot Reloading**: State persists during development
- **Debugging**: Clear action names and state changes in DevTools

#### 5. **Scalability**

- **Modular Architecture**: Easy to extend with new features
- **Reusable Hooks**: Selector hooks can be used across components
- **Middleware Support**: Easy to add logging, analytics, etc.
- **Testing**: Store logic is easily testable in isolation

### Migration Steps

#### Step 1: Install Dependencies

```bash
npm install zod zustand
```

#### Step 2: Create the Store

- Copy the `tripStore.ts` implementation
- Adjust schemas to match your exact data structure
- Test with your actual API endpoints

#### Step 3: Update Components

- Replace `useState` and `useEffect` with Zustand hooks
- Add error handling and loading states
- Implement optimistic updates where appropriate

#### Step 4: Add Filtering

- Implement the `TripFilters` component
- Connect filters to the store
- Test filter combinations

#### Step 5: Enhance UX

- Add loading indicators
- Implement error boundaries
- Add retry mechanisms
- Test edge cases

### Testing the Implementation

```typescript
// __tests__/tripStore.test.ts
import { renderHook, act } from "@testing-library/react";
import { useTripStore } from "@/stores/tripStore";

describe("Trip Store", () => {
  beforeEach(() => {
    // Reset store state
    useTripStore.getState().clearFilters();
  });

  it("should fetch trips successfully", async () => {
    const { result } = renderHook(() => useTripStore());

    await act(async () => {
      await result.current.fetchTrips();
    });

    expect(result.current.trips.length).toBeGreaterThan(0);
    expect(result.current.error).toBeNull();
  });

  it("should filter trips by status", () => {
    const { result } = renderHook(() => useTripStore());

    act(() => {
      result.current.setFilters({ status: "activated" });
    });

    const activatedTrips = result.current.filteredTrips.filter(
      (trip) => trip.tripStatus === "activated"
    );

    expect(result.current.filteredTrips).toEqual(activatedTrips);
  });

  it("should handle search filtering", () => {
    const { result } = renderHook(() => useTripStore());

    act(() => {
      result.current.setFilters({ searchTerm: "electronics" });
    });

    const searchResults = result.current.filteredTrips.filter((trip) =>
      trip.description.toLowerCase().includes("electronics")
    );

    expect(result.current.filteredTrips).toEqual(searchResults);
  });
});
```

This comprehensive Zustand implementation transforms your TripListView from a simple component with local state into a powerful, scalable, and maintainable solution that provides excellent user experience and developer productivity.
