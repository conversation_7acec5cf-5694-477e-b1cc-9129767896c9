# Trip Store Architecture Analysis & Implementation Guide

## Architecture Analysis Summary

Based on the documented architecture patterns and current project structure, this analysis provides recommendations for implementing a proper Zustand store for trip management that aligns with the established clean architecture principles.

## Current State Analysis

### Existing Implementation
- **Hook-based approach**: `useTripData` hook directly fetches data from JSON files
- **No centralized state management**: Each component manages its own trip data
- **No real-time updates**: Static data fetching without SignalR integration
- **Limited error handling**: Basic error states without retry mechanisms
- **No caching**: Data refetched on every component mount

### Architecture Gaps
1. **Missing Store Layer**: No Zustand store for trip management
2. **Missing Service Layer**: Direct API calls from hooks
3. **Missing Validation Layer**: No Zod schemas for trip data
4. **Missing Real-time Integration**: No SignalR for live trip updates
5. **Missing Filter Integration**: No filtering capabilities

## Required Layers Implementation

### 1. **Validation Layer** (`layers/validation/tripSchema.ts`)
```typescript
import { z } from 'zod';

// Trip validation schemas
export const TripDriverSchema = z.object({
  driverId: z.string().min(1, 'Driver ID is required'),
  driverName: z.string().min(1, 'Driver name is required'),
  driverPassportNumber: z.string().min(1, 'Passport number is required'),
  driverNationality: z.string().min(1, 'Nationality is required'),
  driverContactNo: z.string().optional()
});

export const TripVehicleSchema = z.object({
  vehicleId: z.string().min(1, 'Vehicle ID is required'),
  vehiclePlateNumber: z.string().min(1, 'Plate number is required'),
  trackerNo: z.string().min(1, 'Tracker number is required'),
  model: z.string().optional(),
  color: z.string().optional(),
  type: z.string().optional(),
  plateCountry: z.string().optional()
});

export const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),
  status: z.array(z.enum(['active', 'charging', 'offline'])),
  tripId: z.string().min(1, 'Trip ID is required'),
  tripStatus: z.enum(['pending', 'activated', 'ended', 'cancelled']),
  transitType: z.string(),
  creationDate: z.string(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),
  driver_details: TripDriverSchema,
  vehicle_details: TripVehicleSchema,
  // ... other nested schemas
});

export type Trip = z.infer<typeof TripSchema>;
export type TripDriver = z.infer<typeof TripDriverSchema>;
export type TripVehicle = z.infer<typeof TripVehicleSchema>;
```

### 2. **Service Layer** (`layers/services/tripService.ts`)
```typescript
import { Trip } from '../validation/tripSchema';
import { FilterCriteria } from '@/stores/filterStore';

export interface TripService {
  getTrips(filters?: FilterCriteria): Promise<Trip[]>;
  getTripById(tripId: string): Promise<Trip>;
  updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip>;
  createTrip(tripData: Partial<Trip>): Promise<Trip>;
}

class ApiTripService implements TripService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';

  async getTrips(filters?: FilterCriteria): Promise<Trip[]> {
    try {
      const queryParams = filters ? new URLSearchParams(filters as any).toString() : '';
      const response = await fetch(`${this.baseUrl}/trips?${queryParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch trips:', error);
      throw error;
    }
  }

  async getTripById(tripId: string): Promise<Trip> {
    try {
      const response = await fetch(`${this.baseUrl}/trips/${tripId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Failed to fetch trip ${tripId}:`, error);
      throw error;
    }
  }

  async updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip> {
    try {
      const response = await fetch(`${this.baseUrl}/trips/${tripId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Failed to update trip ${tripId} status:`, error);
      throw error;
    }
  }

  async createTrip(tripData: Partial<Trip>): Promise<Trip> {
    try {
      const response = await fetch(`${this.baseUrl}/trips`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tripData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to create trip:', error);
      throw error;
    }
  }
}

// Fallback service for development/testing
class MockTripService implements TripService {
  async getTrips(): Promise<Trip[]> {
    const response = await fetch('/data/trips.json');
    return await response.json();
  }

  async getTripById(tripId: string): Promise<Trip> {
    const trips = await this.getTrips();
    const trip = trips.find(t => t.tripId === tripId);
    if (!trip) {
      throw new Error(`Trip ${tripId} not found`);
    }
    return trip;
  }

  async updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip> {
    // Mock implementation
    const trip = await this.getTripById(tripId);
    return { ...trip, tripStatus: status };
  }

  async createTrip(tripData: Partial<Trip>): Promise<Trip> {
    // Mock implementation
    return tripData as Trip;
  }
}

// Service factory
export const createTripService = (): TripService => {
  const useApi = process.env.NEXT_PUBLIC_USE_API === 'true';
  return useApi ? new ApiTripService() : new MockTripService();
};

export const tripService = createTripService();
```

### 3. **Store Layer** (`stores/tripStore.ts`)
```typescript
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Trip, TripSchema } from '@/layers/validation/tripSchema';
import { tripService } from '@/layers/services/tripService';
import { useFilterStore, FilterCriteria } from './filterStore';
import { logger } from '@/layers/logging/logger';

export interface TripStoreState {
  // State
  trips: Trip[];
  selectedTrip: Trip | null;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  connectionStatus: 'disconnected' | 'connected' | 'error';
  lastUpdate: Date | null;
  
  // Form states
  createTripFormError: Record<string, string> | null;
  updateTripFormError: Record<string, string> | null;
}

export interface TripStoreActions {
  // Core actions
  initializeStore: () => Promise<void>;
  fetchTrips: (filters?: FilterCriteria) => Promise<void>;
  fetchTripById: (tripId: string) => Promise<void>;
  selectTrip: (tripId: string | null) => void;
  
  // CRUD operations
  createTrip: (tripData: Partial<Trip>) => Promise<void>;
  updateTripStatus: (tripId: string, status: Trip['tripStatus']) => Promise<void>;
  
  // Real-time updates
  handleTripUpdate: (update: Partial<Trip> & { tripId: string }) => void;
  
  // Filtering
  applyClientSideFiltering: (trips: Trip[], criteria: FilterCriteria) => Trip[];
  
  // Cleanup
  cleanupStore: () => void;
  
  // Error handling
  clearErrors: () => void;
}

export type TripStoreType = TripStoreState & TripStoreActions;

// Factory function for creating trip store logic
const createTripStoreLogic = (instanceId: string) => (set: any, get: any): TripStoreType => ({
  // Initial state
  trips: [],
  selectedTrip: null,
  isLoading: false,
  isInitialized: false,
  error: null,
  connectionStatus: 'disconnected',
  lastUpdate: null,
  createTripFormError: null,
  updateTripFormError: null,

  // Initialize store
  initializeStore: async () => {
    if (get().isInitialized) return;
    
    try {
      set({ isLoading: true, error: null });
      logger.info(`TripStore[${instanceId}]: Initializing store`);
      
      // Subscribe to SignalR for real-time updates
      // signalrService.subscribe('TRIP_UPDATE', get().handleTripUpdate);
      
      // Load initial data
      await get().fetchTrips();
      
      set({ 
        isInitialized: true, 
        isLoading: false, 
        connectionStatus: 'connected',
        lastUpdate: new Date() 
      });
      
      logger.info(`TripStore[${instanceId}]: Store initialized successfully`);
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to initialize`, error as Error);
      set({ 
        error: 'Failed to initialize trip store', 
        isLoading: false,
        connectionStatus: 'error'
      });
    }
  },

  // Fetch trips with optional filtering
  fetchTrips: async (filters?: FilterCriteria) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get filter criteria from filter store if not provided
      const filterCriteria = filters || useFilterStore.getState().getFilterCriteria();
      
      try {
        // Try API first
        const trips = await tripService.getTrips(filterCriteria || undefined);
        
        // Validate trips
        const validatedTrips = trips.map(trip => {
          const result = TripSchema.safeParse(trip);
          if (!result.success) {
            logger.warn(`TripStore[${instanceId}]: Invalid trip data`, { tripId: trip.tripId, errors: result.error });
            return null;
          }
          return result.data;
        }).filter(Boolean) as Trip[];
        
        set({ 
          trips: validatedTrips, 
          isLoading: false, 
          lastUpdate: new Date(),
          connectionStatus: 'connected'
        });
        
        logger.info(`TripStore[${instanceId}]: Fetched ${validatedTrips.length} trips`);
      } catch (apiError) {
        // Fallback to mock data
        logger.warn(`TripStore[${instanceId}]: API unavailable, using fallback data`);
        const fallbackTrips = await tripService.getTrips();
        
        const filteredTrips = filterCriteria 
          ? get().applyClientSideFiltering(fallbackTrips, filterCriteria)
          : fallbackTrips;
        
        set({ 
          trips: filteredTrips, 
          isLoading: false,
          connectionStatus: 'error'
        });
      }
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to fetch trips`, error as Error);
      set({ error: 'Failed to load trips', isLoading: false });
    }
  },

  // Fetch single trip by ID
  fetchTripById: async (tripId: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const trip = await tripService.getTripById(tripId);
      
      // Validate trip
      const result = TripSchema.safeParse(trip);
      if (!result.success) {
        throw new Error(`Invalid trip data for ${tripId}`);
      }
      
      // Update trips array if trip exists, otherwise add it
      const currentTrips = get().trips;
      const tripIndex = currentTrips.findIndex(t => t.tripId === tripId);
      
      let updatedTrips;
      if (tripIndex >= 0) {
        updatedTrips = [...currentTrips];
        updatedTrips[tripIndex] = result.data;
      } else {
        updatedTrips = [...currentTrips, result.data];
      }
      
      set({ 
        trips: updatedTrips,
        selectedTrip: result.data,
        isLoading: false,
        lastUpdate: new Date()
      });
      
      logger.info(`TripStore[${instanceId}]: Fetched trip ${tripId}`);
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to fetch trip ${tripId}`, error as Error);
      set({ error: `Failed to load trip ${tripId}`, isLoading: false });
    }
  },

  // Select trip
  selectTrip: (tripId: string | null) => {
    const trip = tripId ? get().trips.find(t => t.tripId === tripId) || null : null;
    set({ selectedTrip: trip });
    logger.debug(`TripStore[${instanceId}]: Trip selected`, { tripId });
  },

  // Create new trip
  createTrip: async (tripData: Partial<Trip>) => {
    try {
      set({ createTripFormError: null });
      
      // Validate trip data
      const result = TripSchema.safeParse(tripData);
      if (!result.success) {
        const fieldErrors: Record<string, string> = {};
        result.error.errors.forEach(err => {
          const field = err.path.join('.');
          fieldErrors[field] = err.message;
        });
        set({ createTripFormError: fieldErrors });
        return;
      }
      
      const newTrip = await tripService.createTrip(result.data);
      
      // Add to trips array
      const currentTrips = get().trips;
      set({
        trips: [...currentTrips, newTrip],
        lastUpdate: new Date(),
        createTripFormError: null
      });
      
      logger.info(`TripStore[${instanceId}]: Trip created`, { tripId: newTrip.tripId });
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to create trip`, error as Error);
      set({ createTripFormError: { root: 'Failed to create trip' } });
    }
  },

  // Update trip status
  updateTripStatus: async (tripId: string, status: Trip['tripStatus']) => {
    try {
      set({ updateTripFormError: null });
      
      const updatedTrip = await tripService.updateTripStatus(tripId, status);
      
      // Update trips array
      const currentTrips = get().trips;
      const tripIndex = currentTrips.findIndex(t => t.tripId === tripId);
      
      if (tripIndex >= 0) {
        const updatedTrips = [...currentTrips];
        updatedTrips[tripIndex] = updatedTrip;
        
        set({
          trips: updatedTrips,
          selectedTrip: get().selectedTrip?.tripId === tripId ? updatedTrip : get().selectedTrip,
          lastUpdate: new Date()
        });
      }
      
      logger.info(`TripStore[${instanceId}]: Trip status updated`, { tripId, status });
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to update trip status`, error as Error);
      set({ updateTripFormError: { root: 'Failed to update trip status' } });
    }
  },

  // Handle real-time trip updates
  handleTripUpdate: (update: Partial<Trip> & { tripId: string }) => {
    const currentTrips = get().trips;
    const tripIndex = currentTrips.findIndex(t => t.tripId === update.tripId);
    
    if (tripIndex >= 0) {
      const updatedTrips = [...currentTrips];
      updatedTrips[tripIndex] = { ...updatedTrips[tripIndex], ...update };
      
      set({
        trips: updatedTrips,
        selectedTrip: get().selectedTrip?.tripId === update.tripId 
          ? { ...get().selectedTrip!, ...update }
          : get().selectedTrip,
        lastUpdate: new Date()
      });
      
      logger.debug(`TripStore[${instanceId}]: Trip updated via SignalR`, { tripId: update.tripId });
    }
  },

  // Apply client-side filtering
  applyClientSideFiltering: (trips: Trip[], criteria: FilterCriteria): Trip[] => {
    logger.debug(`TripStore[${instanceId}]: Applying client-side filtering`, {
      total: trips.length,
      criteria: Object.keys(criteria).filter(key => criteria[key as keyof FilterCriteria])
    });
    
    return trips.filter(trip => {
      // Apply filtering logic based on criteria
      if (criteria.tripStatus && !criteria.tripStatus.includes(trip.tripStatus)) {
        return false;
      }
      
      if (criteria.transitType && !criteria.transitType.includes(trip.transitType)) {
        return false;
      }
      
      if (criteria.searchText) {
        const searchLower = criteria.searchText.toLowerCase();
        const searchableFields = [
          trip.tripId,
          trip.description,
          trip.driver,
          trip.vehicle,
          trip.driver_details.driverName
        ];
        
        if (!searchableFields.some(field => 
          field?.toLowerCase().includes(searchLower)
        )) {
          return false;
        }
      }
      
      return true;
    });
  },

  // Cleanup store
  cleanupStore: () => {
    logger.info(`TripStore[${instanceId}]: Cleaning up store`);
    // signalrService.disconnect();
    set({ 
      isInitialized: false, 
      connectionStatus: 'disconnected',
      trips: [],
      selectedTrip: null
    });
  },

  // Clear errors
  clearErrors: () => {
    set({ 
      error: null, 
      createTripFormError: null, 
      updateTripFormError: null 
    });
  }
});

// Global trip store
export const useTripStore = create<TripStoreType>()()
  devtools(
    createTripStoreLogic('main'),
    { name: 'trip-store-main' }
  )
);

// Store instance cache for isolated stores
const storeInstanceCache = new Map<string, any>();

// Hook for creating isolated trip store instances
export const useTripStoreInstance = (newInstance: boolean = false) => {
  const instanceStore = React.useMemo(() => {
    if (!newInstance) {
      return null;
    }
    
    const cacheKey = 'isolated-trips';
    
    if (storeInstanceCache.has(cacheKey)) {
      return storeInstanceCache.get(cacheKey);
    }
    
    const instanceId = `instance-${Math.random().toString(36).substr(2, 9)}`;
    const newStore = create<TripStoreType>()()
      devtools(
        createTripStoreLogic(instanceId),
        { name: `trip-store-${instanceId}` }
      )
    );
    
    storeInstanceCache.set(cacheKey, newStore);
    return newStore;
  }, [newInstance]);
  
  if (!newInstance) {
    return useTripStore;
  }
  
  return instanceStore!;
};
```

### 4. **Logging Layer** (`layers/logging/logger.ts`)
```typescript
interface Logger {
  info: (message: string, meta?: any) => void;
  warn: (message: string, meta?: any) => void;
  error: (message: string, error?: Error, meta?: any) => void;
  debug: (message: string, meta?: any) => void;
}

class ConsoleLogger implements Logger {
  info(message: string, meta?: any) {
    console.log(`[INFO] ${message}`, meta || '');
  }
  
  warn(message: string, meta?: any) {
    console.warn(`[WARN] ${message}`, meta || '');
  }
  
  error(message: string, error?: Error, meta?: any) {
    console.error(`[ERROR] ${message}`, error || '', meta || '');
  }
  
  debug(message: string, meta?: any) {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, meta || '');
    }
  }
}

export const logger: Logger = new ConsoleLogger();
```

### 5. **Configuration Layer** (`config/appSettings.ts`)
```typescript
export const appSettings = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || '/api',
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '5000'),
    retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3')
  },
  signalr: {
    hubUrl: process.env.NEXT_PUBLIC_SIGNALR_HUB_URL || '/hubs/trips',
    reconnectInterval: parseInt(process.env.NEXT_PUBLIC_SIGNALR_RECONNECT_INTERVAL || '5000')
  },
  features: {
    useRealTimeUpdates: process.env.NEXT_PUBLIC_USE_REALTIME === 'true',
    useApiService: process.env.NEXT_PUBLIC_USE_API === 'true'
  }
};
```

## Component Usage Examples

### Using Global Trip Store
```typescript
// components/TripDashboard.tsx
import { useTripStore } from '@/stores/tripStore';
import { useEffect } from 'react';

export const TripDashboard: React.FC = () => {
  const {
    trips,
    selectedTrip,
    isLoading,
    error,
    initializeStore,
    selectTrip,
    fetchTrips
  } = useTripStore();

  useEffect(() => {
    initializeStore();
  }, []);

  if (isLoading) return <div>Loading trips...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Trip Dashboard ({trips.length} trips)</h1>
      {trips.map(trip => (
        <TripCard 
          key={trip.tripId}
          trip={trip}
          isSelected={selectedTrip?.tripId === trip.tripId}
          onClick={() => selectTrip(trip.tripId)}
        />
      ))}
    </div>
  );
};
```

### Using Isolated Trip Store
```typescript
// components/IsolatedTripWorkspace.tsx
import { useTripStoreInstance } from '@/stores/tripStore';
import { useEffect } from 'react';

export const IsolatedTripWorkspace: React.FC = () => {
  const isolatedStore = useTripStoreInstance(true);
  
  const trips = isolatedStore(state => state.trips);
  const initializeStore = isolatedStore(state => state.initializeStore);
  const createTrip = isolatedStore(state => state.createTrip);

  useEffect(() => {
    initializeStore();
  }, []);

  return (
    <div>
      <h1>Isolated Trip Workspace</h1>
      <p>Independent from main dashboard</p>
      {/* Trip management UI */}
    </div>
  );
};
```

## Migration Strategy

### Phase 1: Foundation
1. Create validation schemas (`layers/validation/tripSchema.ts`)
2. Implement service layer (`layers/services/tripService.ts`)
3. Set up logging and configuration layers

### Phase 2: Store Implementation
1. Create trip store (`stores/tripStore.ts`)
2. Implement global store pattern
3. Add error handling and validation

### Phase 3: Component Migration
1. Replace `useTripData` hook with store usage
2. Update existing components to use trip store
3. Add real-time update capabilities

### Phase 4: Advanced Features
1. Implement isolated store pattern
2. Add filtering integration
3. Implement SignalR real-time updates
4. Add performance optimizations

## Benefits of This Architecture

1. **Separation of Concerns**: Clear boundaries between layers
2. **Type Safety**: Zod schemas ensure runtime type validation
3. **Testability**: Service layer can be easily mocked
4. **Scalability**: Factory pattern allows multiple store instances
5. **Real-time Ready**: Built-in SignalR integration support
6. **Error Handling**: Comprehensive error management
7. **Performance**: Optimized state updates and caching
8. **Maintainability**: Clean, documented, and consistent patterns

This architecture follows the documented patterns while providing a practical, production-ready implementation for trip management in your tracking application.