# Trip Store Implementation Guide

## Architecture Analysis

Based on the analysis of the project's architecture documents (`ARCHITECTURE.md` and `STORE_PATTERNS.md`) and current codebase, this guide provides a comprehensive implementation plan for a Zustand-based trip management system that follows the established clean architecture principles.

## Current State vs Required Architecture

### Current Implementation Gaps
- **No centralized state management**: Using `useTripData` hook with direct JSON fetching
- **Missing service layer**: Direct API calls from hooks
- **No validation layer**: No Zod schemas for trip data validation
- **No real-time updates**: Static data without SignalR integration
- **Limited error handling**: Basic error states without retry mechanisms

### Required Layers Summary

According to the architecture, we need to implement these layers:

1. **Validation Layer** - Zod schemas for type safety and runtime validation
2. **Service Layer** - Abstract transport details (REST, SignalR, mock)
3. **Store Layer** - Zustand store with business logic and state management
4. **Config Layer** - Environment configuration management
5. **Logging Layer** - Cross-cutting concern for monitoring and debugging

## Layer Implementation

### 1. Validation Layer

**File**: `layers/validation/tripSchema.ts`

```typescript
import { z } from 'zod';

// Driver validation schema
export const TripDriverSchema = z.object({
  driverId: z.string().min(1, 'Driver ID is required'),
  driverName: z.string().min(1, 'Driver name is required'),
  driverPassportNumber: z.string().min(1, 'Passport number is required'),
  driverNationality: z.string().min(1, 'Nationality is required'),
  driverContactNo: z.string().optional()
});

// Vehicle validation schema
export const TripVehicleSchema = z.object({
  vehicleId: z.string().min(1, 'Vehicle ID is required'),
  vehiclePlateNumber: z.string().min(1, 'Plate number is required'),
  trackerNo: z.string().min(1, 'Tracker number is required'),
  model: z.string().optional(),
  color: z.string().optional(),
  type: z.string().optional(),
  plateCountry: z.string().optional()
});

// Route validation schema
export const TripRouteSchema = z.object({
  routeId: z.string().min(1, 'Route ID is required'),
  routeName: z.string().min(1, 'Route name is required'),
  entryPort: z.string().min(1, 'Entry port is required'),
  exitPort: z.string().min(1, 'Exit port is required')
});

// Shipment validation schema
export const TripShipmentSchema = z.object({
  shipmentId: z.string().min(1, 'Shipment ID is required'),
  shipmentDescription: z.string().min(1, 'Shipment description is required'),
  ownerDescription: z.string().optional(),
  shipmentDescriptionArabic: z.string().optional()
});

// Tracking validation schema
export const TripTrackingSchema = z.object({
  currentLocation: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
    timestamp: z.string()
  }),
  completeDistance: z.number().min(0),
  remainingDistance: z.number().min(0),
  estimatedArrival: z.string().nullable(),
  elocks: z.string()
});

// Compliance validation schema
export const TripComplianceSchema = z.object({
  securityNotes: z.string(),
  customsStatus: z.string(),
  documentStatus: z.string()
});

// Main trip validation schema
export const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),
  status: z.array(z.enum(['active', 'charging', 'offline'])),
  tripId: z.string().min(1, 'Trip ID is required'),
  tripStatus: z.enum(['pending', 'activated', 'ended', 'cancelled']),
  transitType: z.string(),
  creationDate: z.string(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),
  transitSeqNo: z.string().optional(),
  declarationDate: z.string().optional(),
  startingDate: z.string().optional(),
  expectedArrivalDate: z.string().optional(),
  endDate: z.string().optional(),
  driver_details: TripDriverSchema,
  vehicle_details: TripVehicleSchema,
  route: TripRouteSchema,
  shipment: TripShipmentSchema,
  tracking: TripTrackingSchema,
  compliance: TripComplianceSchema
});

// Filter criteria schema
export const TripFilterCriteriaSchema = z.object({
  tripStatus: z.array(z.string()).optional(),
  transitType: z.array(z.string()).optional(),
  driverName: z.string().optional(),
  vehiclePlateNumber: z.string().optional(),
  routeName: z.string().optional(),
  dateRange: z.object({
    start: z.string(),
    end: z.string()
  }).optional()
});

// Export inferred types
export type Trip = z.infer<typeof TripSchema>;
export type TripDriver = z.infer<typeof TripDriverSchema>;
export type TripVehicle = z.infer<typeof TripVehicleSchema>;
export type TripRoute = z.infer<typeof TripRouteSchema>;
export type TripShipment = z.infer<typeof TripShipmentSchema>;
export type TripTracking = z.infer<typeof TripTrackingSchema>;
export type TripCompliance = z.infer<typeof TripComplianceSchema>;
export type TripFilterCriteria = z.infer<typeof TripFilterCriteriaSchema>;

// Validation helper functions
export const validateTrip = (data: unknown) => {
  return TripSchema.safeParse(data);
};

export const validateTripArray = (data: unknown) => {
  return z.array(TripSchema).safeParse(data);
};

export const validateTripFilterCriteria = (data: unknown) => {
  return TripFilterCriteriaSchema.safeParse(data);
};
```

### 2. Service Layer

**File**: `layers/services/tripService.ts`

```typescript
import { Trip, TripFilterCriteria, validateTrip, validateTripArray } from '../validation/tripSchema';
import { logger } from '../logging/logger';

export interface TripUpdate {
  tripId: string;
  status: Trip['tripStatus'];
  tracking: Partial<Trip['tracking']>;
  timestamp: string;
}

export interface TripService {
  getTrips(filters?: TripFilterCriteria): Promise<Trip[]>;
  getTripById(tripId: string): Promise<Trip>;
  updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip>;
  createTrip(tripData: Partial<Trip>): Promise<Trip>;
  subscribeToTripUpdates(callback: (update: TripUpdate) => void): () => void;
}

// API-based service implementation
class ApiTripService implements TripService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';

  async getTrips(filters?: TripFilterCriteria): Promise<Trip[]> {
    try {
      logger.info('TripService: Fetching trips', { filters });
      
      const queryParams = filters ? new URLSearchParams(filters as any).toString() : '';
      const response = await fetch(`${this.baseUrl}/trips?${queryParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const validation = validateTripArray(data);
      
      if (!validation.success) {
        logger.error('TripService: Invalid trip data received', validation.error);
        throw new Error('Invalid trip data format');
      }
      
      logger.info('TripService: Successfully fetched trips', { count: validation.data.length });
      return validation.data;
    } catch (error) {
      logger.error('TripService: Failed to fetch trips', error as Error);
      throw error;
    }
  }

  async getTripById(tripId: string): Promise<Trip> {
    try {
      logger.info('TripService: Fetching trip by ID', { tripId });
      
      const response = await fetch(`${this.baseUrl}/trips/${tripId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const validation = validateTrip(data);
      
      if (!validation.success) {
        logger.error('TripService: Invalid trip data received', validation.error);
        throw new Error('Invalid trip data format');
      }
      
      logger.info('TripService: Successfully fetched trip', { tripId });
      return validation.data;
    } catch (error) {
      logger.error(`TripService: Failed to fetch trip ${tripId}`, error as Error);
      throw error;
    }
  }

  async updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip> {
    try {
      logger.info('TripService: Updating trip status', { tripId, status });
      
      const response = await fetch(`${this.baseUrl}/trips/${tripId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const validation = validateTrip(data);
      
      if (!validation.success) {
        logger.error('TripService: Invalid trip data received', validation.error);
        throw new Error('Invalid trip data format');
      }
      
      logger.info('TripService: Successfully updated trip status', { tripId, status });
      return validation.data;
    } catch (error) {
      logger.error(`TripService: Failed to update trip ${tripId} status`, error as Error);
      throw error;
    }
  }

  async createTrip(tripData: Partial<Trip>): Promise<Trip> {
    try {
      logger.info('TripService: Creating new trip', { tripData });
      
      const response = await fetch(`${this.baseUrl}/trips`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tripData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const validation = validateTrip(data);
      
      if (!validation.success) {
        logger.error('TripService: Invalid trip data received', validation.error);
        throw new Error('Invalid trip data format');
      }
      
      logger.info('TripService: Successfully created trip', { tripId: validation.data.tripId });
      return validation.data;
    } catch (error) {
      logger.error('TripService: Failed to create trip', error as Error);
      throw error;
    }
  }

  subscribeToTripUpdates(callback: (update: TripUpdate) => void): () => void {
    // SignalR implementation would go here
    logger.info('TripService: Subscribing to trip updates');
    
    // Mock implementation for now
    const interval = setInterval(() => {
      // Simulate real-time updates
    }, 30000);
    
    return () => {
      logger.info('TripService: Unsubscribing from trip updates');
      clearInterval(interval);
    };
  }
}

// Mock service for development/testing
class MockTripService implements TripService {
  async getTrips(filters?: TripFilterCriteria): Promise<Trip[]> {
    try {
      logger.info('MockTripService: Fetching trips from JSON', { filters });
      
      const response = await fetch('/data/trips.json');
      if (!response.ok) {
        throw new Error('Failed to fetch trips.json');
      }
      
      const data = await response.json();
      const validation = validateTripArray(data);
      
      if (!validation.success) {
        logger.error('MockTripService: Invalid trip data in JSON', validation.error);
        throw new Error('Invalid trip data format in JSON file');
      }
      
      let trips = validation.data;
      
      // Apply client-side filtering
      if (filters) {
        trips = this.applyFilters(trips, filters);
      }
      
      logger.info('MockTripService: Successfully fetched trips', { count: trips.length });
      return trips;
    } catch (error) {
      logger.error('MockTripService: Failed to fetch trips', error as Error);
      throw error;
    }
  }

  async getTripById(tripId: string): Promise<Trip> {
    const trips = await this.getTrips();
    const trip = trips.find(t => t.tripId === tripId);
    
    if (!trip) {
      logger.error('MockTripService: Trip not found', { tripId });
      throw new Error(`Trip ${tripId} not found`);
    }
    
    logger.info('MockTripService: Successfully fetched trip', { tripId });
    return trip;
  }

  async updateTripStatus(tripId: string, status: Trip['tripStatus']): Promise<Trip> {
    logger.info('MockTripService: Mock updating trip status', { tripId, status });
    const trip = await this.getTripById(tripId);
    return { ...trip, tripStatus: status };
  }

  async createTrip(tripData: Partial<Trip>): Promise<Trip> {
    logger.info('MockTripService: Mock creating trip', { tripData });
    // Mock implementation - in real scenario, this would persist data
    return tripData as Trip;
  }

  subscribeToTripUpdates(callback: (update: TripUpdate) => void): () => void {
    logger.info('MockTripService: Mock subscribing to trip updates');
    // Mock implementation - no real-time updates
    return () => {
      logger.info('MockTripService: Mock unsubscribing from trip updates');
    };
  }

  private applyFilters(trips: Trip[], filters: TripFilterCriteria): Trip[] {
    return trips.filter(trip => {
      if (filters.tripStatus && !filters.tripStatus.includes(trip.tripStatus)) {
        return false;
      }
      
      if (filters.transitType && !filters.transitType.includes(trip.transitType)) {
        return false;
      }
      
      if (filters.driverName && !trip.driver_details.driverName.toLowerCase().includes(filters.driverName.toLowerCase())) {
        return false;
      }
      
      if (filters.vehiclePlateNumber && !trip.vehicle_details.vehiclePlateNumber.toLowerCase().includes(filters.vehiclePlateNumber.toLowerCase())) {
        return false;
      }
      
      if (filters.routeName && !trip.route.routeName.toLowerCase().includes(filters.routeName.toLowerCase())) {
        return false;
      }
      
      if (filters.dateRange) {
        const tripDate = new Date(trip.creationDate);
        const startDate = new Date(filters.dateRange.start);
        const endDate = new Date(filters.dateRange.end);
        
        if (tripDate < startDate || tripDate > endDate) {
          return false;
        }
      }
      
      return true;
    });
  }
}

// Service factory
export const createTripService = (): TripService => {
  const useApi = process.env.NEXT_PUBLIC_USE_API === 'true';
  return useApi ? new ApiTripService() : new MockTripService();
};

export const tripService = createTripService();
```

### 3. Logging Layer

**File**: `layers/logging/logger.ts`

```typescript
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: string;
  source?: string;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  private log(level: LogLevel, message: string, data?: any, source?: string) {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      source
    };

    // Console logging for development
    if (this.isDevelopment) {
      const consoleMethod = level === 'error' ? 'error' : level === 'warn' ? 'warn' : 'log';
      console[consoleMethod](`[${level.toUpperCase()}] ${message}`, data || '');
    }

    // In production, you might send logs to a service
    if (!this.isDevelopment && (level === 'error' || level === 'warn')) {
      // Send to logging service (e.g., Sentry, LogRocket, etc.)
      this.sendToLoggingService(entry);
    }
  }

  debug(message: string, data?: any, source?: string) {
    this.log('debug', message, data, source);
  }

  info(message: string, data?: any, source?: string) {
    this.log('info', message, data, source);
  }

  warn(message: string, data?: any, source?: string) {
    this.log('warn', message, data, source);
  }

  error(message: string, error?: Error | any, source?: string) {
    this.log('error', message, error, source);
  }

  private sendToLoggingService(entry: LogEntry) {
    // Implementation for external logging service
    // This could be Sentry, LogRocket, or custom logging endpoint
  }
}

export const logger = new Logger();
```

### 4. Config Layer

**File**: `config/appSettings.ts`

```typescript
interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  signalr: {
    hubUrl: string;
    reconnectInterval: number;
  };
  trips: {
    defaultPageSize: number;
    refreshInterval: number;
    cacheTimeout: number;
  };
  features: {
    useRealTimeUpdates: boolean;
    enableFiltering: boolean;
    enableCaching: boolean;
  };
}

const loadConfig = (): AppConfig => {
  return {
    api: {
      baseUrl: process.env.NEXT_PUBLIC_API_URL || '/api',
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3')
    },
    signalr: {
      hubUrl: process.env.NEXT_PUBLIC_SIGNALR_HUB_URL || '/hubs/trips',
      reconnectInterval: parseInt(process.env.NEXT_PUBLIC_SIGNALR_RECONNECT_INTERVAL || '5000')
    },
    trips: {
      defaultPageSize: parseInt(process.env.NEXT_PUBLIC_TRIPS_PAGE_SIZE || '20'),
      refreshInterval: parseInt(process.env.NEXT_PUBLIC_TRIPS_REFRESH_INTERVAL || '30000'),
      cacheTimeout: parseInt(process.env.NEXT_PUBLIC_TRIPS_CACHE_TIMEOUT || '300000')
    },
    features: {
      useRealTimeUpdates: process.env.NEXT_PUBLIC_ENABLE_REALTIME === 'true',
      enableFiltering: process.env.NEXT_PUBLIC_ENABLE_FILTERING !== 'false',
      enableCaching: process.env.NEXT_PUBLIC_ENABLE_CACHING !== 'false'
    }
  };
};

export const appConfig = loadConfig();
```

### 5. Store Layer (Zustand)

**File**: `stores/tripStore.ts`

```typescript
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Trip, TripFilterCriteria } from '../layers/validation/tripSchema';
import { tripService, TripUpdate } from '../layers/services/tripService';
import { logger } from '../layers/logging/logger';
import { appConfig } from '../config/appSettings';

interface TripStoreState {
  // State
  trips: Trip[];
  selectedTrip: Trip | null;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastUpdate: Date | null;
  filters: TripFilterCriteria | null;
  
  // Actions
  initializeTripStore: () => Promise<void>;
  fetchTrips: (filters?: TripFilterCriteria) => Promise<void>;
  getTripById: (tripId: string) => Promise<void>;
  selectTrip: (tripId: string | null) => void;
  updateTripStatus: (tripId: string, status: Trip['tripStatus']) => Promise<void>;
  setFilters: (filters: TripFilterCriteria | null) => void;
  applyFilters: () => Promise<void>;
  refreshTrips: () => Promise<void>;
  cleanupTripStore: () => void;
  
  // Real-time subscription management
  subscribeToUpdates: () => void;
  unsubscribeFromUpdates: () => void;
}

// Factory function for creating trip store logic
const createTripStoreLogic = (instanceId: string) => (set: any, get: any): TripStoreState => ({
  // Initial state
  trips: [],
  selectedTrip: null,
  isLoading: false,
  isInitialized: false,
  error: null,
  connectionStatus: 'disconnected',
  lastUpdate: null,
  filters: null,

  // Initialize store
  initializeTripStore: async () => {
    if (get().isInitialized) {
      logger.info(`TripStore[${instanceId}]: Already initialized`);
      return;
    }

    try {
      set({ isLoading: true, error: null });
      logger.info(`TripStore[${instanceId}]: Initializing trip store`);

      // Subscribe to real-time updates if enabled
      if (appConfig.features.useRealTimeUpdates) {
        get().subscribeToUpdates();
      }

      // Load initial data
      await get().fetchTrips();

      set({
        isInitialized: true,
        isLoading: false,
        connectionStatus: 'connected',
        lastUpdate: new Date()
      });

      logger.info(`TripStore[${instanceId}]: Trip store initialized successfully`);
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to initialize`, error as Error);
      set({
        error: 'Failed to initialize trip store',
        isLoading: false,
        connectionStatus: 'error'
      });
    }
  },

  // Fetch trips with optional filtering
  fetchTrips: async (filters?: TripFilterCriteria) => {
    try {
      set({ isLoading: true, error: null });
      logger.info(`TripStore[${instanceId}]: Fetching trips`, { filters });

      const currentFilters = filters || get().filters;
      const trips = await tripService.getTrips(currentFilters || undefined);

      set({
        trips,
        isLoading: false,
        lastUpdate: new Date(),
        filters: currentFilters
      });

      logger.info(`TripStore[${instanceId}]: Successfully fetched ${trips.length} trips`);
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to fetch trips`, error as Error);
      set({
        error: 'Failed to load trips',
        isLoading: false
      });
    }
  },

  // Get specific trip by ID
  getTripById: async (tripId: string) => {
    try {
      set({ isLoading: true, error: null });
      logger.info(`TripStore[${instanceId}]: Fetching trip by ID`, { tripId });

      const trip = await tripService.getTripById(tripId);
      
      // Update the trip in the trips array if it exists
      const currentTrips = get().trips;
      const tripIndex = currentTrips.findIndex(t => t.tripId === tripId);
      
      if (tripIndex >= 0) {
        const updatedTrips = [...currentTrips];
        updatedTrips[tripIndex] = trip;
        set({ trips: updatedTrips });
      } else {
        // Add to trips array if not exists
        set({ trips: [...currentTrips, trip] });
      }

      set({
        selectedTrip: trip,
        isLoading: false,
        lastUpdate: new Date()
      });

      logger.info(`TripStore[${instanceId}]: Successfully fetched trip`, { tripId });
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to fetch trip ${tripId}`, error as Error);
      set({
        error: `Failed to load trip ${tripId}`,
        isLoading: false
      });
    }
  },

  // Select trip (for UI state)
  selectTrip: (tripId: string | null) => {
    const trip = tripId ? get().trips.find(t => t.tripId === tripId) || null : null;
    set({ selectedTrip: trip });
    logger.debug(`TripStore[${instanceId}]: Trip selected`, { tripId });
  },

  // Update trip status
  updateTripStatus: async (tripId: string, status: Trip['tripStatus']) => {
    try {
      set({ isLoading: true, error: null });
      logger.info(`TripStore[${instanceId}]: Updating trip status`, { tripId, status });

      const updatedTrip = await tripService.updateTripStatus(tripId, status);
      
      // Update the trip in the store
      const currentTrips = get().trips;
      const tripIndex = currentTrips.findIndex(t => t.tripId === tripId);
      
      if (tripIndex >= 0) {
        const updatedTrips = [...currentTrips];
        updatedTrips[tripIndex] = updatedTrip;
        
        set({
          trips: updatedTrips,
          selectedTrip: get().selectedTrip?.tripId === tripId ? updatedTrip : get().selectedTrip,
          isLoading: false,
          lastUpdate: new Date()
        });
      }

      logger.info(`TripStore[${instanceId}]: Successfully updated trip status`, { tripId, status });
    } catch (error) {
      logger.error(`TripStore[${instanceId}]: Failed to update trip status`, error as Error);
      set({
        error: `Failed to update trip ${tripId}`,
        isLoading: false
      });
    }
  },

  // Set filters
  setFilters: (filters: TripFilterCriteria | null) => {
    set({ filters });
    logger.debug(`TripStore[${instanceId}]: Filters updated`, { filters });
  },

  // Apply current filters
  applyFilters: async () => {
    const filters = get().filters;
    await get().fetchTrips(filters || undefined);
  },

  // Refresh trips (re-fetch with current filters)
  refreshTrips: async () => {
    logger.info(`TripStore[${instanceId}]: Refreshing trips`);
    await get().fetchTrips();
  },

  // Subscribe to real-time updates
  subscribeToUpdates: () => {
    if (get().connectionStatus === 'connected') {
      logger.warn(`TripStore[${instanceId}]: Already subscribed to updates`);
      return;
    }

    set({ connectionStatus: 'connecting' });
    logger.info(`TripStore[${instanceId}]: Subscribing to real-time updates`);

    const unsubscribe = tripService.subscribeToTripUpdates((update: TripUpdate) => {
      logger.debug(`TripStore[${instanceId}]: Received trip update`, update);
      
      const currentTrips = get().trips;
      const tripIndex = currentTrips.findIndex(t => t.tripId === update.tripId);
      
      if (tripIndex >= 0) {
        const updatedTrips = [...currentTrips];
        updatedTrips[tripIndex] = {
          ...updatedTrips[tripIndex],
          tripStatus: update.status,
          tracking: {
            ...updatedTrips[tripIndex].tracking,
            ...update.tracking
          },
          lastSeen: update.timestamp
        };
        
        set({
          trips: updatedTrips,
          lastUpdate: new Date()
        });
      }
    });

    // Store unsubscribe function for cleanup
    (get() as any)._unsubscribeFromUpdates = unsubscribe;
    set({ connectionStatus: 'connected' });
  },

  // Unsubscribe from real-time updates
  unsubscribeFromUpdates: () => {
    const unsubscribe = (get() as any)._unsubscribeFromUpdates;
    if (unsubscribe) {
      logger.info(`TripStore[${instanceId}]: Unsubscribing from real-time updates`);
      unsubscribe();
      (get() as any)._unsubscribeFromUpdates = null;
    }
    set({ connectionStatus: 'disconnected' });
  },

  // Cleanup store
  cleanupTripStore: () => {
    logger.info(`TripStore[${instanceId}]: Cleaning up trip store`);
    get().unsubscribeFromUpdates();
    set({
      isInitialized: false,
      connectionStatus: 'disconnected',
      selectedTrip: null,
      error: null
    });
  }
});

// Global trip store instance
export const useTripStore = create<TripStoreState>()(n  devtools(
    createTripStoreLogic('main'),
    {
      name: 'trip-store-main'
    }
  )
);

// Store instance cache for isolated stores
const storeInstanceCache = new Map<string, any>();

// Hook for creating isolated store instances
export const useTripStoreInstance = (newInstance: boolean = false) => {
  const instanceStore = React.useMemo(() => {
    if (!newInstance) {
      return null; // Use global store
    }
    
    const cacheKey = 'isolated-trips';
    
    if (storeInstanceCache.has(cacheKey)) {
      return storeInstanceCache.get(cacheKey);
    }
    
    const instanceId = `instance-${Math.random().toString(36).substr(2, 9)}`;
    const newStore = create<TripStoreState>()(n      devtools(
        createTripStoreLogic(instanceId),
        {
          name: `trip-store-${instanceId}`
        }
      )
    );
    
    storeInstanceCache.set(cacheKey, newStore);
    return newStore;
  }, [newInstance]);
  
  if (!newInstance) {
    return useTripStore;
  }
  
  return instanceStore!;
};
```

## Component Usage Examples

### Basic Trip List Component

```typescript
// components/TripList.tsx
import React, { useEffect } from 'react';
import { useTripStore } from '../stores/tripStore';
import { logger } from '../layers/logging/logger';

export const TripList: React.FC = () => {
  const {
    trips,
    isLoading,
    error,
    selectedTrip,
    selectTrip,
    initializeTripStore,
    refreshTrips
  } = useTripStore();

  useEffect(() => {
    initializeTripStore();
  }, [initializeTripStore]);

  const handleTripSelect = (tripId: string) => {
    selectTrip(tripId);
    logger.info('TripList: Trip selected', { tripId });
  };

  const handleRefresh = () => {
    refreshTrips();
  };

  if (isLoading) {
    return <div>Loading trips...</div>;
  }

  if (error) {
    return (
      <div>
        <p>Error: {error}</p>
        <button onClick={handleRefresh}>Retry</button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2>Trips ({trips.length})</h2>
        <button onClick={handleRefresh}>Refresh</button>
      </div>
      
      <div className="space-y-2">
        {trips.map(trip => (
          <div
            key={trip.tripId}
            className={`p-4 border rounded cursor-pointer ${
              selectedTrip?.tripId === trip.tripId ? 'bg-blue-100' : 'hover:bg-gray-50'
            }`}
            onClick={() => handleTripSelect(trip.tripId)}
          >
            <div className="flex justify-between">
              <span className="font-medium">{trip.description}</span>
              <span className={`px-2 py-1 rounded text-sm ${
                trip.tripStatus === 'active' ? 'bg-green-100 text-green-800' :
                trip.tripStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {trip.tripStatus}
              </span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              Driver: {trip.driver_details.driverName} | 
              Vehicle: {trip.vehicle_details.vehiclePlateNumber}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Trip Filter Component

```typescript
// components/TripFilter.tsx
import React, { useState } from 'react';
import { useTripStore } from '../stores/tripStore';
import { TripFilterCriteria } from '../layers/validation/tripSchema';

export const TripFilter: React.FC = () => {
  const { filters, setFilters, applyFilters } = useTripStore();
  const [localFilters, setLocalFilters] = useState<TripFilterCriteria>(filters || {});

  const handleApplyFilters = () => {
    setFilters(localFilters);
    applyFilters();
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    setFilters(null);
    applyFilters();
  };

  return (
    <div className="p-4 border rounded">
      <h3 className="font-medium mb-4">Filter Trips</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Trip Status</label>
          <select
            multiple
            value={localFilters.tripStatus || []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value);
              setLocalFilters({ ...localFilters, tripStatus: values });
            }}
            className="w-full border rounded px-3 py-2"
          >
            <option value="pending">Pending</option>
            <option value="activated">Activated</option>
            <option value="ended">Ended</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Driver Name</label>
          <input
            type="text"
            value={localFilters.driverName || ''}
            onChange={(e) => setLocalFilters({ ...localFilters, driverName: e.target.value })}
            className="w-full border rounded px-3 py-2"
            placeholder="Search by driver name"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Vehicle Plate</label>
          <input
            type="text"
            value={localFilters.vehiclePlateNumber || ''}
            onChange={(e) => setLocalFilters({ ...localFilters, vehiclePlateNumber: e.target.value })}
            className="w-full border rounded px-3 py-2"
            placeholder="Search by plate number"
          />
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Apply Filters
          </button>
          <button
            onClick={handleClearFilters}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );
};
```

## Migration Strategy

### Phase 1: Setup Infrastructure
1. Create the layer directories: `layers/validation`, `layers/services`, `layers/logging`
2. Implement validation schemas
3. Create basic service layer with mock implementation
4. Set up logging and configuration

### Phase 2: Implement Store
1. Create the Zustand trip store
2. Implement basic CRUD operations
3. Add filtering capabilities
4. Test with existing components

### Phase 3: Migrate Components
1. Update existing components to use the store instead of `useTripData`
2. Add error handling and loading states
3. Implement real-time updates

### Phase 4: Enhance Features
1. Add caching mechanisms
2. Implement optimistic updates
3. Add retry logic and resilience
4. Performance optimizations

## Environment Configuration

Add these variables to your `.env.local`:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_API_RETRY_ATTEMPTS=3
NEXT_PUBLIC_USE_API=false

# SignalR Configuration
NEXT_PUBLIC_SIGNALR_HUB_URL=/hubs/trips
NEXT_PUBLIC_SIGNALR_RECONNECT_INTERVAL=5000

# Trip Configuration
NEXT_PUBLIC_TRIPS_PAGE_SIZE=20
NEXT_PUBLIC_TRIPS_REFRESH_INTERVAL=30000
NEXT_PUBLIC_TRIPS_CACHE_TIMEOUT=300000

# Feature Flags
NEXT_PUBLIC_ENABLE_REALTIME=false
NEXT_PUBLIC_ENABLE_FILTERING=true
NEXT_PUBLIC_ENABLE_CACHING=true
```

## Benefits of This Architecture

1. **Clean Separation of Concerns**: Each layer has a single responsibility
2. **Type Safety**: Zod schemas ensure runtime validation and TypeScript types
3. **Testability**: Each layer can be tested independently
4. **Scalability**: Easy to add new features and modify existing ones
5. **Maintainability**: Clear structure makes code easy to understand and modify
6. **Real-time Ready**: Built-in support for SignalR integration
7. **Error Handling**: Comprehensive error handling and logging
8. **Performance**: Optimized with caching and efficient state management

This implementation follows the established architecture patterns in your project and provides a solid foundation for trip management that can scale with your application's needs.