# 🚦 Tracking Dashboard Architecture & Developer Guidelines

---

## 1. **Overview**

This project is a real-time vehicle tracking dashboard built with React, Zustand, SignalR, and a .NET backend. It follows clean architecture principles to ensure maintainability, scalability, and developer productivity.

---

## 2. **High-Level Architecture Diagram**

```mermaid
graph TD
  A["UI Components"] -->|"useStore"| B["Zustand Store"]
  B -->|"calls"| C["Service Layer"]
  C -->|"REST API"| D["Backend API"]
  C -->|"SignalR"| E["SignalR Hub"]
  B -->|"subscribes"| E
  F["Validation Layer"] -->|"Zod Schemas"| A
  G["Config (.env+ appSettings)"] -->|"loads"| C
  H["Monitoring/Performance/Resilience"] -->|"cross-cutting"| B
```

---

## 3. **Layered Structure**

### **A. UI Layer (Components)**
- **Dumb**: No business logic, no direct API calls.
- **Interacts only with Zustand store** via hooks.
- **Form validation** uses Zod schemas, but logic is in the store.

### **B. State Management (Zustand Store)**
- **Single source of truth** for all app state.
- **Handles all business logic**: loading, updating, syncing, error handling.
- **Orchestrates**:
  - Initial data load (REST API)
  - Real-time updates (SignalR)
  - User actions (add/update/remove)
- **Exposes actions** for UI to call.

### **C. Service Layer**
- **Abstracts transport details** (REST, SignalR, etc.).
- **No state or business logic**.
- **Adapters**: If you ever need to swap implementations (e.g., mock vs. real), do it here.

### **D. Validation Layer**
- **Zod schemas** for all domain models.
- **Centralized**: One file per domain (e.g., tracker, auth).
- **Used in both store and UI** for type safety and runtime validation.

### **E. Config Layer**
- **.env** for environment-specific values (number of trackers, API URLs, etc.).
- **appSettings.ts** loads and exposes config to services.

### **F. Monitoring, Performance, Resilience**
- **Cross-cutting concerns**: logging, metrics, error handling, retry, circuit breaker, etc.
- **Used by store and services**.

---

## 4. **Data Flow Diagram**

```mermaid
sequenceDiagram
  participant User
  participant UI as UI Component
  participant Store as Zustand Store
  participant Service as Service Layer
  participant API as Backend API
  participant SignalR as SignalR Hub

  User->>UI: Interacts (e.g., Add Tracker)
  UI->>Store: Calls action (addTracker)
  Store->>Service: Calls API (if needed)
  Service->>API: REST call (POST /trackers)
  API-->>Service: Response
  Service-->>Store: Data
  Store-->>UI: State update

  Note over Store,SignalR: On app start, store subscribes to SignalR
  SignalR-->>Store: Real-time tracker updates
  Store-->>UI: State update
```

---

## 5. **Directory Structure**

```
src/
  components/         # UI components (dumb, stateless)
    Dashboard/
      Dashboard.tsx
      AddTrackerForm.tsx
      UpdateTrackerForm.tsx
  stores/             # Zustand stores (business logic, state)
    dashboardStore.ts
    authStore.ts
  layers/             # Service, validation, logging, etc.
    auth/
      authService.ts
    fetchy/
      index.ts
    signalr/
      signalrSimulator.ts
    validation/
      trackerSchema.ts
      authSchema.ts
      ...
    logging/
      logger.ts
  config/
    appSettings.ts    # Loads from .env
  types/              # Shared TypeScript types
    index.ts
```

---

## 6. **Guidelines for Developers**

### **A. UI Components**
- Never call APIs or SignalR directly.
- Never contain business logic.
- Use Zustand store hooks for all state and actions.
- Use Zod schemas for form validation, but keep validation logic in the store.

### **B. Zustand Store**
- All business logic and side effects live here.
- Orchestrate data from both REST and SignalR.
- Expose clear, typed actions for the UI.
- Use Zod schemas for validating data before updating state.
- Handle optimistic updates, error states, and loading indicators.

### **C. Service Layer**
- Only responsible for data transport (fetch, SignalR, etc.).
- No state, no business logic.
- Use adapters if you need to swap implementations (e.g., mock for tests).

### **D. Validation**
- One Zod schema file per domain (e.g., trackerSchema.ts).
- Export both the schema and the inferred TypeScript type.
- Use schemas in both store and UI for type safety.

### **E. Configuration**
- All environment/config values in `.env`.
- `appSettings.ts` loads and exposes config.
- Never hardcode config values in code.

### **F. Monitoring, Performance, Resilience**
- Use logger for all important actions and errors.
- Use retry/circuit breaker for network calls.
- Collect and report metrics for key actions.

---

## 7. **Example: Adding a Tracker**

```mermaid
flowchart TD
  A["AddTrackerForm"] -->|"calls"| B["dashboardStore.addTracker()"]
  B -->|"validates with"| C["trackerSchema"]
  B -->|"calls"| D["trackerService.addTracker()"]
  D -->|"POST"| E["Backend API"]
  E -->|"response"| D
  D -->|"returns"| B
  B -->|"updates state"| A
```

---

## 8. **Best Practices**

- **Keep components dumb**: UI only, no logic.
- **Centralize logic in stores**: All business rules, side effects, and state.
- **Abstract transport in services**: No state, no logic, just data movement.
- **Validate everywhere**: Use Zod schemas for all data in/out.
- **Use config for everything**: No hardcoded values.
- **Monitor and log**: Use logger and metrics for all important flows.
- **Document your code**: JSDoc, comments, and update this architecture doc as needed.

---

## 9. **Debugging & Monitoring Zustand Store**

### 1. **Enable Zustand Devtools**

Zustand supports integration with Redux DevTools, which allows you to inspect state changes, actions, and time-travel through state history.

**How to enable:**
- Install the devtools middleware:
  ```bash
  npm install zustand-middleware
  ```
- Wrap your store with `devtools`:
  ```typescript
  import { create } from 'zustand';
  import { devtools } from 'zustand/middleware';

  const useDashboardStore = create(
    devtools((set, get) => ({
      // ...store definition
    }), { name: 'DashboardStore' })
  );
  ```
- Open your browser’s Redux DevTools extension to inspect state and actions.

---

### 2. **Logging State Changes**

- Use Zustand’s `subscribe` method to log or monitor state changes for debugging or analytics.
- Example:
  ```typescript
  const unsub = useDashboardStore.subscribe(
    (state, prevState) => {
      console.log('Zustand Store Changed:', { state, prevState });
      // Optionally send metrics here
    }
  );
  // Remember to unsubscribe if needed
  ```

---

### 3. **Custom Middleware for Monitoring**

- You can create custom middleware to log, monitor, or send metrics on every action or state change.
- Example:
  ```typescript
  const monitor = (config) => (set, get, api) =>
    config(
      (...args) => {
        // Custom logic before/after state change
        console.debug('Zustand Action:', args);
        set(...args);
      },
      get,
      api
    );

  const useStore = create(monitor((set, get) => ({
    // ...store
  })));
  ```

---

### 4. **Best Practices for Zustand Debugging**

- **Name your stores** in devtools for clarity (e.g., `{ name: 'AuthStore' }`).
- **Keep actions pure**: Avoid side effects inside state updaters; use async actions for effects.
- **Log errors and important events** using your logger (e.g., login failures, tracker updates).
- **Test with mock states**: Zustand stores are easy to mock for unit/integration tests.
- **Use selectors** to avoid unnecessary re-renders and to debug specific slices of state.

---

### 5. **Production Monitoring**

- **Metrics**: Use the subscribe or middleware pattern to send anonymized usage or error metrics to your monitoring backend.
- **Error Reporting**: Catch and log errors in store actions, and report them using your logger or monitoring service.
- **Performance**: Monitor for unnecessary re-renders by profiling components and using selectors.

---

### 6. **Example: Monitoring State Changes**

```typescript
import { useEffect } from 'react';
import { useDashboardStore } from '../stores/dashboardStore';

export function useStoreMonitor() {
  useEffect(() => {
    const unsub = useDashboardStore.subscribe(
      (state, prevState) => {
        // Send to logger or monitoring service
        logger.debug('DashboardStore state changed', { state, prevState });
      }
    );
    return () => unsub();
  }, []);
}
```
*You can use this hook in your App component to monitor store changes globally.*

---

### 7. **References**

- [Zustand Devtools Docs](https://docs.pmnd.rs/zustand/integrations/devtools)
- [Zustand Middleware Docs](https://docs.pmnd.rs/zustand/guides/middleware)
- [Redux DevTools Extension](https://github.com/zalmoxisus/redux-devtools-extension)

---

## **Summary Table: Zustand Store Debugging & Monitoring**

| Tool/Technique         | Use Case                        | How to Use                                 |
|------------------------|---------------------------------|--------------------------------------------|
| Zustand Devtools       | Inspect state/actions           | Enable middleware, use Redux DevTools      |
| subscribe()            | Log/monitor state changes       | `store.subscribe((state, prev) => ...)`    |
| Custom Middleware      | Advanced logging/metrics        | Wrap store with custom middleware          |
| Logger Integration     | Error/event reporting           | Log in actions and error handlers          |
| Selectors              | Debug specific state slices     | Use selectors in components/hooks          |

---

**By following these practices, your team will have full visibility into Zustand store state, actions, and side effects—making debugging and monitoring much easier and more robust.**

---

## 10. **Extending the Architecture**

- To add new features, create new store actions and service methods.
- For new domains, add new schema files and store files.
- For new real-time sources, subscribe in the store and merge updates into state.

---

## 11. **FAQ**

**Q: Can I call APIs directly in components?**  
A: No. Always go through the store.

**Q: Where do I put form validation?**  
A: Use Zod schemas, but validation logic should be in the store.

**Q: How do I add a new config value?**  
A: Add it to `.env`, then expose it in `appSettings.ts`.

**Q: How do I handle real-time updates?**  
A: The store subscribes to SignalR and updates state.

---

## 12. **References**

- [Zustand Docs](https://docs.pmnd.rs/zustand/getting-started/introduction)
- [Zod Docs](https://zod.dev/)
- [SignalR Docs](https://learn.microsoft.com/en-us/aspnet/core/signalr/introduction)
- [React Query (TanStack)](https://tanstack.com/query/latest)
- [Clean Architecture](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html)

---

## 13. **Contact**

For questions or suggestions, contact the architecture owner or open an issue in the repository. 