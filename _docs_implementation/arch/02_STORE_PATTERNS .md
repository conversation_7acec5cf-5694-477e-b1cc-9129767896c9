# Store Patterns & Architecture Guidelines

This document provides comprehensive guidelines for implementing store
patterns, filtering, and validation in the Tracking application.

## Table of Contents

1.  [Global Store Pattern](#X821044b654eec10cc9010ff48636692af6d556a)
2.  [Isolated Store Pattern](#X346fc93452bd002c6430cc36cb636af350d22e8)
3.  [Filtering System](#X116bbc346b3a21ca55e1592391198db05f5e8c9)
4.  [Validation System](#Xba58468eaf8fe05dd6321eb7a0a29e28ceebb84)

## 1. Global Store Pattern

### Overview

Global stores are singleton instances shared across all components in
the application. They maintain a single source of truth and ensure data
consistency across different views.

### Implementation

#### Store Factory Function

The core of both global and isolated stores is the
`createDashboardStoreLogic` factory function. This function contains all
the business logic and can be instantiated multiple times with different
`instanceId` values.

    // src/stores/dashboardStore.ts

    // Factory function that creates store logic - the heart of the architecture
    const createDashboardStoreLogic = (instanceId: string) => (set, get) => ({
      // State
      trackers: [],
      selectedTracker: null,
      isLoading: false,
      isInitialized: false,
      error: null,
      connectionStatus: 'disconnected',
      viewCenter: { lat: 40.7128, lng: -74.0060, timestamp: new Date() },
      zoom: 13,
      addTrackerFormError: null,

      // Actions with instance-specific behavior
      initializeDashboard: async () => {
        if (get().isInitialized) return;
        
        try {
          set({ isLoading: true, error: null });
          logger.info(`DashboardStore[${instanceId}]: Initializing dashboard`);
          
          // Each instance gets its own SignalR connection
          signalrSimulator.connect();
          
          // Subscribe to SignalR with instance-specific callback
          signalrSimulator.subscribe('TRACKER_UPDATE', (update: TrackerUpdate) => {
            // Instance-specific filtering and processing
            const filterCriteria = useFilterStore.getState().getFilterCriteria();
            
            if (filterCriteria) {
              // Apply filtering before processing
              const tempTracker: Tracker = {
                id: update.trackerId,
                name: `Tracker ${update.trackerId}`,
                type: 'car',
                location: update.location,
                speed: update.speed,
                battery: update.battery,
                status: update.status,
                lastUpdate: new Date(),
                lastMaintenance: new Date(),
                isActive: true,
                color: '#3B82F6'
              };
              
              if (!get().applyClientSideFiltering([tempTracker], filterCriteria).length) {
                return; // Discard this message for this instance
              }
            }
            
            // Update this instance's trackers
            const currentTrackers = get().trackers;
            const trackerIndex = currentTrackers.findIndex(t => t.id === update.trackerId);
            
            if (trackerIndex >= 0) {
              const updatedTrackers = [...currentTrackers];
              updatedTrackers[trackerIndex] = {
                ...updatedTrackers[trackerIndex],
                location: update.location,
                speed: update.speed,
                battery: update.battery,
                status: update.status,
                lastUpdate: new Date()
              };
              set({ trackers: updatedTrackers, lastUpdate: new Date() });
            }
          });
          
          // Load initial data with filtering
          await get().fetchActiveTrackers();
          
          set({ 
            isInitialized: true, 
            isLoading: false, 
            connectionStatus: 'connected',
            lastUpdate: new Date() 
          });
          
          logger.info(`DashboardStore[${instanceId}]: Dashboard initialized successfully`);
        } catch (error) {
          logger.error(`DashboardStore[${instanceId}]: Failed to initialize`, error as Error);
          set({ 
            error: 'Failed to initialize dashboard', 
            isLoading: false,
            connectionStatus: 'error'
          });
        }
      },

      addTracker: (formData: any) => {
        try {
          // Generate instance-specific ID
          const id = `${instanceId}-tracker-${Date.now()}`;
          const now = new Date();
          
          // Validation and tracker creation logic
          const tracker = {
            id, // Instance-specific ID (e.g., "main-tracker-123" or "instance-abc123-tracker-456")
            name: formData.name,
            type: formData.type,
            location: { lat: 40.7128, lng: -74.0060, timestamp: now },
            speed: formData.speed,
            status: formData.status,
            battery: formData.battery,
            lastUpdate: now,
            lastMaintenance: new Date(formData.lastMaintenance),
            isActive: formData.isActive,
            color: VEHICLE_COLORS[formData.type] || '#4ECDC4',
          };
          
          // Validate tracker
          const result = validator.validateTracker(tracker);
          if (!result.success) {
            const fieldErrors: Record<string, string> = {};
            (result.errors || []).forEach(e => { fieldErrors[e.field] = e.message; });
            set({ addTrackerFormError: fieldErrors });
            return;
          }
          
          // Add to this instance only
          const currentTrackers = get().trackers;
          set({
            trackers: [...currentTrackers, result.data],
            lastUpdate: new Date(),
            addTrackerFormError: null
          });
          
          logger.info(`DashboardStore[${instanceId}]: Tracker added`, { trackerId: id });
        } catch (error) {
          logger.error(`DashboardStore[${instanceId}]: Failed to add tracker`, error as Error);
          set({ addTrackerFormError: { root: 'Failed to add tracker' } });
        }
      },

      fetchActiveTrackers: async () => {
        try {
          set({ isLoading: true, error: null });
          
          // Get filter criteria from filter store
          const filterCriteria = useFilterStore.getState().getFilterCriteria();
          
          try {
            // Try API first with filtering
            const trackers = await trackerService.getActiveTrackers(
              filterCriteria || undefined
            );
            set({ trackers, isLoading: false, lastUpdate: new Date() });
            logger.info(`DashboardStore[${instanceId}]: Fetched ${trackers.length} trackers from API`);
          } catch (apiError) {
            // Fallback to SignalR data with client-side filtering
            logger.warn(`DashboardStore[${instanceId}]: API unavailable, using SignalR data`);
            const signalrTrackers = signalrSimulator.getInitialTrackers();
            const filteredTrackers = filterCriteria 
              ? get().applyClientSideFiltering(signalrTrackers, filterCriteria)
              : signalrTrackers;
            set({ trackers: filteredTrackers, isLoading: false });
          }
        } catch (error) {
          logger.error(`DashboardStore[${instanceId}]: Failed to fetch trackers`, error as Error);
          set({ error: 'Failed to load trackers', isLoading: false });
        }
      },

      cleanupDashboard: () => {
        logger.info(`DashboardStore[${instanceId}]: Cleaning up dashboard`);
        signalrSimulator.disconnect();
        set({ isInitialized: false, connectionStatus: 'disconnected' });
      },

      // Other actions (selectTracker, removeTracker, etc.) with instance logging
      selectTracker: (trackerId: string | null) => {
        set({ selectedTracker: trackerId });
        logger.debug(`DashboardStore[${instanceId}]: Tracker selected`, { trackerId });
      },

      // Instance-specific filtering method
      applyClientSideFiltering: (trackers: Tracker[], criteria: FilterCriteria): Tracker[] => {
        logger.debug(`DashboardStore[${instanceId}]: Applying client-side filtering`, {
          total: trackers.length,
          criteria: Object.keys(criteria).filter(key => criteria[key as keyof FilterCriteria])
        });
        
        return trackers.filter(tracker => {
          // Same filtering logic as TrackerService but instance-aware
          return trackerService.shouldIncludeTracker(tracker, criteria);
        });
      }
    });

#### Store Definition

    // src/stores/dashboardStore.ts
    import { create } from 'zustand';
    import { devtools } from 'zustand/middleware';

    // Main global store - singleton instance using the factory
    export const useDashboardStore = create<DashboardStoreType>()(
      devtools(
        createDashboardStoreLogic('main'), // 'main' is the global instance ID
        {
          name: 'dashboard-store-main'
        }
      )
    );

#### Component Usage

    // src/components/Dashboard/Dashboard.tsx
    import { useDashboardStore } from '../../stores/dashboardStore';

    export const Dashboard: React.FC = () => {
      // Using global store - all components share the same data
      const {
        trackers,
        selectedTracker,
        selectTracker,
        addTracker,
        initializeDashboard
      } = useDashboardStore();

      useEffect(() => {
        // Initialize global store once
        initializeDashboard();
      }, []);

      return (
        <div>
          <h1>Trackers ({trackers.length})</h1>
          {trackers.map(tracker => (
            <TrackerCard 
              key={tracker.id}
              tracker={tracker}
              isSelected={selectedTracker === tracker.id}
              onClick={() => selectTracker(tracker.id)}
            />
          ))}
        </div>
      );
    };

#### Another Component Using Same Global Store

    // src/components/Dashboard/GlobalTrackersForm.tsx
    import { useDashboardStore } from '../../stores/dashboardStore';

    export const GlobalTrackersForm: React.FC = () => {
      // Same global store instance - shares data with Dashboard.tsx
      const {
        trackers,        // Same trackers as Dashboard.tsx
        selectedTracker, // Same selection as Dashboard.tsx
        selectTracker,
        addTracker
      } = useDashboardStore();

      // Changes here affect Dashboard.tsx and all other global store users
      const handleAddTracker = (formData: any) => {
        addTracker(formData); // Updates global state
      };

      return (
        <div>
          <h1>Global Trackers (Shared Store)</h1>
          <p>Changes here affect Dashboard and other global components</p>
          {/* Same trackers as Dashboard.tsx */}
          {trackers.map(tracker => (
            <TrackerCard key={tracker.id} tracker={tracker} />
          ))}
        </div>
      );
    };

### Key Characteristics

-   ✅ **Singleton**: One instance per application
-   ✅ **Shared State**: All components see the same data
-   ✅ **Synchronized**: Changes in one component affect all others
-   ✅ **Persistent**: Data survives navigation between components
-   ✅ **Performance**: Single SignalR connection, single API state

### When to Use Global Store

-   Dashboard main view
-   Navigation between related components
-   Shared state across multiple views
-   Single source of truth requirements

## 2. Isolated Store Pattern

### Overview

Isolated stores create completely separate instances of the same store
logic. Each instance has its own state, SignalR connection, and data
lifecycle, completely independent from other instances.

### Implementation

#### How the Factory Enables Isolation

The isolated store pattern works because the same
`createDashboardStoreLogic` factory function is used to create
completely separate store instances. Each instance has its own:

    // src/stores/dashboardStore.ts

    // The factory function is the key - it creates independent instances
    const createDashboardStoreLogic = (instanceId: string) => (set, get) => ({
      // Each instance has its own state
      trackers: [],              // Independent tracker list
      selectedTracker: null,     // Independent selection
      isLoading: false,          // Independent loading state
      error: null,               // Independent error state
      connectionStatus: 'disconnected', // Independent connection
      
      // Each instance generates unique IDs
      addTracker: (formData: any) => {
        const id = `${instanceId}-tracker-${Date.now()}`; // Unique per instance
        // e.g., "main-tracker-123" vs "instance-abc123-tracker-456"
      },
      
      // Each instance has its own SignalR subscription
      initializeDashboard: async () => {
        signalrSimulator.subscribe('TRACKER_UPDATE', (update) => {
          // This callback is specific to THIS instance
          const currentTrackers = get().trackers; // THIS instance's trackers
          // ... update logic affects only THIS instance
        });
      }
    });

#### Store Factory & Instance Management

    // src/stores/dashboardStore.ts

    // Store instance cache to persist isolated stores across navigation
    const storeInstanceCache = new Map<string, any>();

    export const useDashboardStoreInstance = (newInstance: boolean = false) => {
      const instanceStore = React.useMemo(() => {
        if (!newInstance) {
          return null; // Return null to use global store
        }
        
        // Use consistent key for isolated trackers to persist across navigation
        const cacheKey = 'isolated-trackers';
        
        // Check if we already have this instance cached
        if (storeInstanceCache.has(cacheKey)) {
          return storeInstanceCache.get(cacheKey);
        }
        
        // Create new instance with unique ID
        const instanceId = `instance-${Math.random().toString(36).substr(2, 9)}`;
        const newStore = create<DashboardStoreType>()(
          devtools(
            createDashboardStoreLogic(instanceId), // Same logic, unique instance
            {
              name: `dashboard-store-${instanceId}`
            }
          )
        );
        
        // Cache the instance for persistence across navigation
        storeInstanceCache.set(cacheKey, newStore);
        return newStore;
      }, [newInstance]);
      
      if (!newInstance) {
        return useDashboardStore; // Return global store
      }
      
      return instanceStore!;
    };

#### Component Usage

    // src/components/Dashboard/IsolatedTrackersForm.tsx
    import { useDashboardStoreInstance } from '../../stores/dashboardStore';

    export const IsolatedTrackersForm: React.FC = () => {
      // Create ISOLATED store instance (separate from global)
      const isolatedStore = useDashboardStoreInstance(true);
      
      // Extract state using selectors
      const trackers = isolatedStore((state: any) => state.trackers);
      const selectedTracker = isolatedStore((state: any) => state.selectedTracker);
      const selectTracker = isolatedStore((state: any) => state.selectTracker);
      const addTracker = isolatedStore((state: any) => state.addTracker);

      // Initialize isolated store (only if not already initialized)
      useEffect(() => {
        const initializeIsolatedStore = async () => {
          const currentState = isolatedStore.getState();
          
          // Only initialize if store is empty
          if (currentState.trackers.length === 0 && !currentState.isInitialized) {
            await currentState.initializeDashboard();
            logger.info('IsolatedTrackersForm: Store initialized independently');
          } else {
            logger.info('IsolatedTrackersForm: Preserving existing data', {
              trackerCount: currentState.trackers.length
            });
          }
        };

        initializeIsolatedStore();
      }, [isolatedStore]);

      // Changes here do NOT affect global store or other instances
      const handleAddTracker = (formData: any) => {
        addTracker(formData); // Only updates THIS isolated instance
      };

      return (
        <div>
          <h1>Isolated Trackers (Separate Store)</h1>
          <p>Completely independent from Dashboard and Global Trackers</p>
          {/* Different trackers than global store */}
          {trackers.map(tracker => (
            <TrackerCard key={tracker.id} tracker={tracker} />
          ))}
        </div>
      );
    };

#### Form Components with Store Selection

    // src/components/Dashboard/AddTrackerForm.tsx
    interface AddTrackerFormProps {
      useNewInstance: boolean; // Flag to choose store type
      onClose: () => void;
    }

    export const AddTrackerForm: React.FC<AddTrackerFormProps> = ({ 
      useNewInstance, 
      onClose 
    }) => {
      // Conditionally use global or isolated store
      const globalStore = useDashboardStore();
      const isolatedStore = useDashboardStoreInstance(useNewInstance);
      
      // Select the appropriate store
      const store = useNewInstance ? isolatedStore : globalStore;
      const addTracker = store((state: any) => state.addTracker);
      const formError = store((state: any) => state.addTrackerFormError);

      const handleSubmit = (formData: any) => {
        addTracker(formData); // Adds to selected store (global or isolated)
        if (!formError) {
          onClose();
        }
      };

      return (
        <form onSubmit={handleSubmit}>
          <h3>
            Add Tracker to {useNewInstance ? 'Isolated' : 'Global'} Store
          </h3>
          {/* Form fields */}
        </form>
      );
    };

    // Usage in Global component
    <AddTrackerForm useNewInstance={false} onClose={handleClose} />

    // Usage in Isolated component  
    <AddTrackerForm useNewInstance={true} onClose={handleClose} />

### Factory Pattern Benefits

The `createDashboardStoreLogic` factory pattern provides several
architectural advantages:

#### **🏗️ Code Reusability**

    // Same logic, different instances
    const globalStore = createDashboardStoreLogic('main');        // Global instance
    const isolatedStore = createDashboardStoreLogic('instance-xyz'); // Isolated instance
    const testStore = createDashboardStoreLogic('test-env');      // Test instance

#### **🔍 Instance Identification**

    // Every action includes instance context for debugging
    logger.info(`DashboardStore[${instanceId}]: Tracker added`, { trackerId });
    // Output: "DashboardStore[main]: Tracker added" vs "DashboardStore[instance-abc123]: Tracker added"

#### **⚡ Independent Operations**

    // Each instance operates independently
    globalStore.addTracker(data);    // Affects only global store
    isolatedStore.addTracker(data);  // Affects only isolated store
    // No cross-instance interference

#### **🎯 Consistent Behavior**

    // Same validation, filtering, and business logic across all instances
    const result = validator.validateTracker(tracker); // Same validation
    const filtered = applyClientSideFiltering(trackers, criteria); // Same filtering
    // Ensures consistency regardless of instance type

### Key Characteristics

-   ✅ **Independent**: Completely separate from global store
-   ✅ **Isolated State**: Own trackers, selection, errors
-   ✅ **Separate SignalR**: Own connection and data stream
-   ✅ **Persistent**: Cached across navigation
-   ✅ **Same Logic**: Uses same store logic as global store via factory
-   ✅ **Debuggable**: Instance-specific logging and identification
-   ✅ **Scalable**: Easy to create new instances for different purposes

### When to Use Isolated Store

-   Testing scenarios
-   Independent data sets
-   Proof of concepts
-   User-specific workspaces
-   Temporary data manipulation

## 3. Filtering System

### Overview

The filtering system provides client-side filtering for both API
responses and SignalR streams. It uses a dedicated filter store with
localStorage persistence and applies filters consistently across all
data sources.

### Architecture

    // src/stores/filterStore.ts

    export interface FilterCriteria {
      vehicleTypes: string[];
      statuses: string[];
      batteryRange: { min: number; max: number };
      speedRange: { min: number; max: number };
      batteryLevels: string[];
      nameSearch: string;
      regionBounds: {
        north: number;
        south: number;
        east: number;
        west: number;
      } | null;
      lastActiveAfter: Date | null;
    }

    // Filter store with localStorage persistence
    export const useFilterStore = create<FilterStoreType>()(
      devtools(
        persist(
          (set, get) => ({
            criteria: DEFAULT_CRITERIA,
            hasActiveFilters: false,
            
            setCriteria: (newCriteria: Partial<FilterCriteria>) => {
              const updatedCriteria = { ...get().criteria, ...newCriteria };
              const hasActive = calculateHasActiveFilters(updatedCriteria);
              set({ 
                criteria: updatedCriteria, 
                hasActiveFilters: hasActive 
              });
            },
            
            getFilterCriteria: () => {
              const state = get();
              return state.hasActiveFilters ? state.criteria : null;
            }
          }),
          {
            name: 'tracker-filters', // localStorage key
            partialize: (state) => ({ criteria: state.criteria })
          }
        )
      )
    );

### Service Layer Filtering

    // src/services/trackerService.ts

    export class TrackerService {
      async getActiveTrackers(filterCriteria?: FilterCriteria): Promise<Tracker[]> {
        try {
          // Get raw data from API
          const rawData = await fetchy.get<Tracker[]>('/api/trackers/active');
          
          // Apply client-side filtering if criteria provided
          if (filterCriteria) {
            return this.applyFilters(rawData, filterCriteria);
          }
          
          return rawData;
        } catch (error) {
          logger.error('TrackerService: Failed to fetch trackers', error as Error);
          throw error;
        }
      }

      private applyFilters(trackers: Tracker[], criteria: FilterCriteria): Tracker[] {
        return trackers.filter(tracker => 
          this.shouldIncludeTracker(tracker, criteria)
        );
      }

      private shouldIncludeTracker(tracker: Tracker, criteria: FilterCriteria): boolean {
        // Vehicle type filter
        if (criteria.vehicleTypes.length > 0 && 
            !criteria.vehicleTypes.includes(tracker.type)) {
          return false;
        }

        // Status filter
        if (criteria.statuses.length > 0 && 
            !criteria.statuses.includes(tracker.status)) {
          return false;
        }

        // Battery range filter
        if (tracker.battery < criteria.batteryRange.min || 
            tracker.battery > criteria.batteryRange.max) {
          return false;
        }

        // Speed range filter
        if (tracker.speed < criteria.speedRange.min || 
            tracker.speed > criteria.speedRange.max) {
          return false;
        }

        // Name search filter
        if (criteria.nameSearch && 
            !tracker.name.toLowerCase().includes(criteria.nameSearch.toLowerCase())) {
          return false;
        }

        // Geographic bounds filter
        if (criteria.regionBounds) {
          const { lat, lng } = tracker.location;
          if (lat < criteria.regionBounds.south || 
              lat > criteria.regionBounds.north ||
              lng < criteria.regionBounds.west || 
              lng > criteria.regionBounds.east) {
            return false;
          }
        }

        // Last active filter
        if (criteria.lastActiveAfter && 
            tracker.lastUpdate < criteria.lastActiveAfter) {
          return false;
        }

        return true;
      }
    }

### Store Integration

    // src/stores/dashboardStore.ts

    const createDashboardStoreLogic = (instanceId: string) => (set, get) => ({
      // Fetch trackers with filtering
      fetchActiveTrackers: async () => {
        try {
          set({ isLoading: true, error: null });
          
          // Get filter criteria from filter store
          const filterCriteria = useFilterStore.getState().getFilterCriteria();
          
          try {
            // Try API first with filtering
            const trackers = await trackerService.getActiveTrackers(
              filterCriteria || undefined
            );
            set({ trackers, isLoading: false, lastUpdate: new Date() });
          } catch (apiError) {
            // Fallback to SignalR data with client-side filtering
            logger.warn('API unavailable, using SignalR data with filtering');
            const signalrTrackers = signalrSimulator.getInitialTrackers();
            const filteredTrackers = filterCriteria 
              ? get().applyClientSideFiltering(signalrTrackers, filterCriteria)
              : signalrTrackers;
            set({ trackers: filteredTrackers, isLoading: false });
          }
        } catch (error) {
          logger.error(`DashboardStore[${instanceId}]: Failed to fetch trackers`, error);
          set({ error: 'Failed to load trackers', isLoading: false });
        }
      },

      // SignalR message filtering
      initializeDashboard: async () => {
        // ... SignalR setup ...
        
        signalrSimulator.subscribe('TRACKER_UPDATE', (update: TrackerUpdate) => {
          const filterCriteria = useFilterStore.getState().getFilterCriteria();
          
          if (filterCriteria) {
            // Create temporary tracker object for filtering
            const tempTracker: Tracker = {
              id: update.trackerId,
              name: `Tracker ${update.trackerId}`,
              type: 'car', // Default type for filtering
              location: update.location,
              speed: update.speed,
              battery: update.battery,
              status: update.status,
              lastUpdate: new Date(),
              lastMaintenance: new Date(),
              isActive: true,
              color: '#3B82F6'
            };
            
            // Apply filtering - discard if doesn't match
            if (!get().applyClientSideFiltering([tempTracker], filterCriteria).length) {
              return; // Discard this SignalR message
            }
          }
          
          // Process the update normally
          const currentTrackers = get().trackers;
          const trackerIndex = currentTrackers.findIndex(t => t.id === update.trackerId);
          // ... update logic ...
        });
      },

      // Client-side filtering method
      applyClientSideFiltering: (trackers: Tracker[], criteria: FilterCriteria): Tracker[] => {
        return trackers.filter(tracker => {
          // Same filtering logic as TrackerService.shouldIncludeTracker
          return trackerService.shouldIncludeTracker(tracker, criteria);
        });
      }
    });

### Filter Integration Hook

    // src/stores/dashboardStore.ts

    export const useFilterIntegration = (storeInstance: any) => {
      React.useEffect(() => {
        // Subscribe to filter store changes
        const unsubscribe = useFilterStore.subscribe((state) => {
          logger.info('FilterIntegration: Filter criteria changed, re-applying filters');
          
          const store = storeInstance.getState();
          
          // Re-apply filters to current SignalR data
          try {
            const signalrTrackers = signalrSimulator.getInitialTrackers();
            const filterCriteria = useFilterStore.getState().getFilterCriteria();
            const filteredTrackers = filterCriteria 
              ? store.applyClientSideFiltering(signalrTrackers, filterCriteria)
              : signalrTrackers;
            
            storeInstance.setState({ 
              trackers: filteredTrackers,
              lastUpdate: new Date()
            });
            
            logger.info('FilterIntegration: Filters re-applied', { 
              total: signalrTrackers.length,
              filtered: filteredTrackers.length
            });
          } catch (error) {
            logger.error('FilterIntegration: Failed to re-apply filters', error);
          }
        });

        return unsubscribe;
      }, [storeInstance]);
    };

### Component Usage

    // src/components/Dashboard/Dashboard.tsx

    export const Dashboard: React.FC = () => {
      const { trackers, fetchActiveTrackers } = useDashboardStore();
      
      // Initialize filter integration for global store
      useFilterIntegration(useDashboardStore);

      return (
        <div>
          <FilterForm /> {/* Filter controls */}
          <div>Trackers ({trackers.length})</div>
          {/* Filtered trackers displayed */}
        </div>
      );
    };
    // src/components/Common/FilterForm.tsx

    export const FilterForm: React.FC = () => {
      const { criteria, setCriteria, hasActiveFilters, clearFilters } = useFilterStore();

      const handleVehicleTypeChange = (types: string[]) => {
        setCriteria({ vehicleTypes: types });
      };

      const handleBatteryRangeChange = (min: number, max: number) => {
        setCriteria({ batteryRange: { min, max } });
      };

      return (
        <div className="filter-form">
          <h3>Filters {hasActiveFilters && <span>(Active)</span>}</h3>
          
          {/* Vehicle Type Filter */}
          <div>
            <label>Vehicle Types:</label>
            <select multiple value={criteria.vehicleTypes} onChange={handleVehicleTypeChange}>
              <option value="car">Car</option>
              <option value="truck">Truck</option>
              <option value="bus">Bus</option>
            </select>
          </div>

          {/* Battery Range Filter */}
          <div>
            <label>Battery Range:</label>
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={criteria.batteryRange.min}
              onChange={(e) => handleBatteryRangeChange(+e.target.value, criteria.batteryRange.max)}
            />
          </div>

          <button onClick={clearFilters} disabled={!hasActiveFilters}>
            Clear All Filters
          </button>
        </div>
      );
    };

### Key Features

-   ✅ **Client-Side Only**: No API parameters sent
-   ✅ **SignalR Filtering**: Messages filtered before processing
-   ✅ **Persistent**: Filters saved to localStorage
-   ✅ **Performance**: `hasActiveFilters` flag avoids unnecessary
    processing
-   ✅ **Consistent**: Same filtering logic for API and SignalR
-   ✅ **Real-Time**: Filter changes immediately applied

## 4. Validation System

### Overview

The validation system uses Zod schemas for runtime type checking and
validation. It provides both compile-time TypeScript types and runtime
validation with detailed error reporting.

### Schema Definition

    // src/layers/validation/schemas.ts

    import { z } from 'zod';

    // Location schema with coordinate validation
    export const trackerLocationSchema = z.object({
      lat: z.number()
        .min(-90, 'Latitude must be between -90 and 90')
        .max(90, 'Latitude must be between -90 and 90'),
      lng: z.number()
        .min(-180, 'Longitude must be between -180 and 180')
        .max(180, 'Longitude must be between -180 and 180'),
      timestamp: z.date()
    });

    // Main tracker schema
    export const trackerSchema = z.object({
      id: z.string()
        .min(1, 'Tracker ID is required')
        .regex(
          /^(tracker-\d+|[a-zA-Z0-9-]+-tracker-\d+|[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i, 
          'Invalid tracker ID format'
        ),
      name: z.string()
        .min(3, 'Tracker name must be at least 3 characters')
        .max(100, 'Tracker name must not exceed 100 characters')
        .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Tracker name contains invalid characters'),
      type: z.enum(['car', 'truck', 'bus', 'van', 'motorcycle', 'police', 'ambulance']),
      location: trackerLocationSchema,
      speed: z.number()
        .min(0, 'Speed cannot be negative')
        .max(500, 'Speed seems unrealistic (max 500 km/h)'),
      status: z.enum(['active', 'inactive', 'maintenance'], {
        errorMap: () => ({ message: 'Status must be active, inactive, or maintenance' })
      }),
      battery: z.number()
        .min(0, 'Battery level cannot be negative')
        .max(100, 'Battery level cannot exceed 100%'),
      lastUpdate: z.date(),
      lastMaintenance: z.date(),
      isActive: z.boolean(),
      color: z.string()
        .regex(/^#[0-9A-F]{6}$/i, 'Color must be a valid hex color code')
    });

    // SignalR update schema (subset of tracker)
    export const trackerUpdateSchema = z.object({
      trackerId: z.string().min(1),
      location: trackerLocationSchema,
      speed: z.number().min(0).max(500),
      battery: z.number().min(0).max(100),
      status: z.enum(['active', 'inactive', 'maintenance'])
    });

    // Authentication schemas
    export const loginCredentialsSchema = z.object({
      username: z.string()
        .min(3, 'Username must be at least 3 characters')
        .max(50, 'Username must not exceed 50 characters')
        .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
      password: z.string()
        .min(6, 'Password must be at least 6 characters')
        .max(100, 'Password must not exceed 100 characters')
    });

    // Export TypeScript types from schemas
    export type Tracker = z.infer<typeof trackerSchema>;
    export type TrackerUpdate = z.infer<typeof trackerUpdateSchema>;
    export type TrackerLocation = z.infer<typeof trackerLocationSchema>;
    export type LoginCredentials = z.infer<typeof loginCredentialsSchema>;

### Validator Service

    // src/layers/validation/validator.ts

    import { logger } from '../logging/logger';
    import { 
      trackerSchema, 
      trackerUpdateSchema, 
      loginCredentialsSchema,
      type Tracker,
      type TrackerUpdate,
      type LoginCredentials
    } from './schemas';

    export interface ValidationResult<T> {
      success: boolean;
      data?: T;
      errors?: ValidationError[];
    }

    export interface ValidationError {
      field: string;
      message: string;
      code: string;
    }

    export class Validator {
      validateTracker(data: unknown): ValidationResult<Tracker> {
        try {
          const result = trackerSchema.safeParse(data);
          
          if (result.success) {
            return {
              success: true,
              data: result.data
            };
          }
          
          const errors = result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }));
          
          logger.warn('Validation failed for Tracker', {
            errors,
            originalData: data
          });
          
          return {
            success: false,
            errors
          };
        } catch (error) {
          logger.error('Validator: Unexpected error during tracker validation', error as Error);
          return {
            success: false,
            errors: [{ field: 'root', message: 'Validation failed', code: 'unknown' }]
          };
        }
      }

      validateTrackerUpdate(data: unknown): ValidationResult<TrackerUpdate> {
        try {
          const result = trackerUpdateSchema.safeParse(data);
          
          if (result.success) {
            return {
              success: true,
              data: result.data
            };
          }
          
          const errors = result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }));
          
          logger.warn('Validation failed for TrackerUpdate', {
            errors,
            originalData: data
          });
          
          return {
            success: false,
            errors
          };
        } catch (error) {
          logger.error('Validator: Unexpected error during tracker update validation', error as Error);
          return {
            success: false,
            errors: [{ field: 'root', message: 'Validation failed', code: 'unknown' }]
          };
        }
      }

      validateLoginCredentials(data: unknown): ValidationResult<LoginCredentials> {
        try {
          const result = loginCredentialsSchema.safeParse(data);
          
          if (result.success) {
            return {
              success: true,
              data: result.data
            };
          }
          
          const errors = result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }));
          
          logger.warn('Validation failed for LoginCredentials', {
            errors,
            originalData: data
          });
          
          return {
            success: false,
            errors
          };
        } catch (error) {
          logger.error('Validator: Unexpected error during login validation', error as Error);
          return {
            success: false,
            errors: [{ field: 'root', message: 'Validation failed', code: 'unknown' }]
          };
        }
      }
    }

    export const validator = new Validator();

### Store Integration

    // src/stores/dashboardStore.ts

    import { validator } from '../layers/validation/validator';
    import { logger } from '../layers/logging/logger';

    const createDashboardStoreLogic = (instanceId: string) => (set, get) => ({
      addTracker: (formData: any) => {
        try {
          // Generate tracker data
          const id = `${instanceId}-tracker-${Date.now()}`;
          const now = new Date();
          
          // Handle date validation
          let lastMaintDate: Date;
          try {
            lastMaintDate = formData.lastMaintenance ? new Date(formData.lastMaintenance) : now;
            if (isNaN(lastMaintDate.getTime())) throw new Error('Invalid date');
          } catch {
            set({ addTrackerFormError: { lastMaintenance: 'Last maintenance date is invalid' } });
            return;
          }
          
          // Construct tracker object
          const tracker = {
            id,
            name: formData.name,
            type: formData.type,
            location: { lat: 40.7128, lng: -74.0060, timestamp: now },
            speed: formData.speed,
            status: formData.status,
            battery: formData.battery,
            lastUpdate: now,
            lastMaintenance: lastMaintDate,
            isActive: formData.isActive,
            color: VEHICLE_COLORS[formData.type] || '#4ECDC4',
          };
          
          // Validate using Zod schema
          const result = validator.validateTracker(tracker);
          if (!result.success) {
            // Convert validation errors to form errors
            const fieldErrors: Record<string, string> = {};
            (result.errors || []).forEach(e => {
              fieldErrors[e.field] = e.message;
            });
            
            set({ addTrackerFormError: fieldErrors });
            return;
          }
          
          // Add validated tracker
          const currentTrackers = get().trackers;
          set({
            trackers: [...currentTrackers, result.data], // result.data is validated Tracker
            lastUpdate: new Date(),
            addTrackerFormError: null
          });
          
          logger.info(`DashboardStore[${instanceId}]: Tracker added`, { trackerId: id });
        } catch (error) {
          logger.error(`DashboardStore[${instanceId}]: Failed to add tracker`, error as Error);
          set({ addTrackerFormError: { root: 'Failed to add tracker' } });
        }
      }
    });

### Component Usage

    // src/components/Dashboard/AddTrackerForm.tsx

    export const AddTrackerForm: React.FC<AddTrackerFormProps> = ({ useNewInstance, onClose }) => {
      const [formData, setFormData] = useState({
        name: '',
        type: 'car',
        speed: 0,
        battery: 100,
        status: 'active',
        isActive: true,
        lastMaintenance: new Date().toISOString().split('T')[0]
      });

      const store = useNewInstance ? useDashboardStoreInstance(true) : useDashboardStore();
      const addTracker = store((state: any) => state.addTracker);
      const formError = store((state: any) => state.addTrackerFormError);
      const clearFormError = store((state: any) => state.clearAddTrackerFormError);

      const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        clearFormError();
        addTracker(formData); // Validation happens in store
      };

      const getFieldError = (field: string): string | null => {
        if (!formError) return null;
        if (typeof formError === 'string') return formError;
        return formError[field] || null;
      };

      return (
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('name') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
              placeholder="Enter tracker name"
            />
            {getFieldError('name') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('name')}</p>
            )}
          </div>

          {/* Type Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Type</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('type') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
            >
              <option value="car">Car</option>
              <option value="truck">Truck</option>
              <option value="bus">Bus</option>
              <option value="van">Van</option>
              <option value="motorcycle">Motorcycle</option>
              <option value="police">Police</option>
              <option value="ambulance">Ambulance</option>
            </select>
            {getFieldError('type') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('type')}</p>
            )}
          </div>

          {/* Speed Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Speed (km/h)</label>
            <input
              type="number"
              min="0"
              max="500"
              value={formData.speed}
              onChange={(e) => setFormData({ ...formData, speed: parseInt(e.target.value) || 0 })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('speed') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
            />
            {getFieldError('speed') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('speed')}</p>
            )}
          </div>

          {/* Battery Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Battery (%)</label>
            <input
              type="number"
              min="0"
              max="100"
              value={formData.battery}
              onChange={(e) => setFormData({ ...formData, battery: parseInt(e.target.value) || 0 })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('battery') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
            />
            {getFieldError('battery') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('battery')}</p>
            )}
          </div>

          {/* Status Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('status') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="maintenance">Maintenance</option>
            </select>
            {getFieldError('status') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('status')}</p>
            )}
          </div>

          {/* Last Maintenance Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Last Maintenance</label>
            <input
              type="date"
              value={formData.lastMaintenance}
              onChange={(e) => setFormData({ ...formData, lastMaintenance: e.target.value })}
              className={`mt-1 block w-full rounded-md border ${
                getFieldError('lastMaintenance') ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2`}
            />
            {getFieldError('lastMaintenance') && (
              <p className="mt-1 text-sm text-red-600">{getFieldError('lastMaintenance')}</p>
            )}
          </div>

          {/* Is Active Checkbox */}
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700">Active Tracker</label>
            {getFieldError('isActive') && (
              <p className="ml-2 text-sm text-red-600">{getFieldError('isActive')}</p>
            )}
          </div>

          {/* Root/General Error */}
          {getFieldError('root') && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{getFieldError('root')}</p>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700"
            >
              Add Tracker
            </button>
          </div>
        </form>
      );
    };

### SignalR Validation

    // src/layers/signalr/signalrSimulator.ts

    import { validator } from '../validation/validator';
    import { logger } from '../logging/logger';

    export class SignalRSimulator {
      private handleTrackerUpdate(rawUpdate: unknown) {
        // Validate SignalR message
        const result = validator.validateTrackerUpdate(rawUpdate);
        
        if (!result.success) {
          logger.warn('SignalR: Invalid tracker update received', {
            errors: result.errors,
            rawData: rawUpdate
          });
          return; // Discard invalid message
        }
        
        // Process validated update
        const update = result.data; // Typed as TrackerUpdate
        this.callbacks.forEach(callback => {
          try {
            callback('TRACKER_UPDATE', update);
          } catch (error) {
            logger.error('SignalR: Callback error', error as Error);
          }
        });
      }
    }

### Key Features

-   ✅ **Runtime Validation**: Zod provides runtime type checking
-   ✅ **Compile-Time Types**: TypeScript types generated from schemas
-   ✅ **Detailed Errors**: Field-specific error messages
-   ✅ **Input Sanitization**: Regex patterns and range constraints
-   ✅ **Error Handling**: Graceful degradation for invalid data
-   ✅ **Consistent**: Same validation logic across forms, APIs, and
    SignalR

## Best Practices Summary

### Factory Pattern

-   Create a single factory function with all business logic
-   Use `instanceId` parameter for instance identification and unique
    IDs
-   Include instance context in all logging for debugging
-   Keep the factory pure - no external dependencies in the function
    itself
-   Use the same validation and filtering logic across all instances

### Global Store

-   Use for shared application state
-   Single SignalR connection for efficiency
-   Initialize once in main component
-   Use `createDashboardStoreLogic('main')` with global identifier

### Isolated Store

-   Use for independent features or testing
-   Cache instances to persist across navigation
-   Guard initialization to preserve data
-   Use `createDashboardStoreLogic(uniqueId)` with random identifier
-   Clean up resources appropriately (but preserve data for navigation)

### Filtering

-   Apply filters in service layer for consistency
-   Use client-side filtering for performance
-   Discard non-matching SignalR messages early
-   Persist filter criteria to localStorage

### Validation

-   Define schemas once, use everywhere
-   Validate at system boundaries (forms, APIs, SignalR)
-   Provide field-specific error feedback
-   Log validation failures for debugging

These patterns ensure a scalable, maintainable, and performant
architecture for complex state management and data validation
requirements.
