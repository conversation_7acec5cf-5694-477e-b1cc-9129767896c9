# Simple Trip Store Implementation Steps

## Implementation Summary

This guide provides a simplified, step-by-step approach to implement:
1. **Trip data fetching** from server API (JSON mockup)
2. **Integration with TripListView component**

## Step-by-Step Implementation Plan

### Phase 1: Basic Setup (20 minutes)
1. Install required dependencies
2. Create basic folder structure
3. Set up environment configuration

### Phase 2: Trip Data Service (30 minutes)
1. Create trip types and validation
2. Implement API service for trip fetching
3. Create basic Zustand store for trips

### Phase 3: Component Integration (40 minutes)
1. Update existing TripListView component
2. Connect store to component
3. Test the complete flow

**Total Estimated Time: 1.5 hours**

---

## Detailed Implementation

### Phase 1: Basic Setup

#### 1.1 Install Dependencies

```bash
npm install zustand zod
```

#### 1.2 Create Folder Structure

```
src/
├── stores/
│   └── tripStore.ts
├── services/
│   └── tripService.ts
├── types/
│   └── trip.ts
└── components/
    └── TripListView.tsx (existing)
```

#### 1.3 Environment Configuration

Add to `.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_USE_MOCK_DATA=true
```

### Phase 2: Trip Data Service

#### 2.1 Trip Types (`types/trip.ts`)

```typescript
export interface Trip {
  id: number;
  transitNumber: number;
  description: string;
  entry: string;
  lastSeen: string;
  tracker: string;
  driver: string;
  vehicle: string;
  alerts: string;
  status: string[];
  tripId: string;
  tripStatus: 'pending' | 'activated' | 'ended' | 'cancelled';
  transitType: string;
  creationDate: string;
  activationDate: string | null;
  completionDate: string | null;
  driver_details: {
    driverId: string;
    driverName: string;
    driverPassportNumber: string;
    driverNationality: string;
    driverContactNo?: string;
  };
  vehicle_details: {
    vehicleId: string;
    vehiclePlateNumber: string;
    trackerNo: string;
    model?: string;
    color?: string;
    type?: string;
    plateCountry?: string;
  };
  route: {
    routeId: string;
    routeName: string;
    entryPort: string;
    exitPort: string;
  };
  tracking: {
    currentLocation: {
      latitude: number;
      longitude: number;
      timestamp: string;
    };
    completeDistance: number;
    remainingDistance: number;
    estimatedArrival: string | null;
    elocks: string;
  };
}

// Validation schemas using Zod
import { z } from 'zod';

export const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),
  status: z.array(z.string()),
  tripId: z.string(),
  tripStatus: z.enum(['pending', 'activated', 'ended', 'cancelled']),
  transitType: z.string(),
  creationDate: z.string(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),
  driver_details: z.object({
    driverId: z.string(),
    driverName: z.string(),
    driverPassportNumber: z.string(),
    driverNationality: z.string(),
    driverContactNo: z.string().optional()
  }),
  vehicle_details: z.object({
    vehicleId: z.string(),
    vehiclePlateNumber: z.string(),
    trackerNo: z.string(),
    model: z.string().optional(),
    color: z.string().optional(),
    type: z.string().optional(),
    plateCountry: z.string().optional()
  }),
  route: z.object({
    routeId: z.string(),
    routeName: z.string(),
    entryPort: z.string(),
    exitPort: z.string()
  }),
  tracking: z.object({
    currentLocation: z.object({
      latitude: z.number(),
      longitude: z.number(),
      timestamp: z.string()
    }),
    completeDistance: z.number(),
    remainingDistance: z.number(),
    estimatedArrival: z.string().nullable(),
    elocks: z.string()
  })
});
```

#### 2.2 Trip Service (`services/tripService.ts`)

```typescript
import { Trip, TripSchema } from '../types/trip';
import { z } from 'zod';

class TripService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  private useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';

  async getTrips(): Promise<Trip[]> {
    try {
      const url = this.useMockData ? '/data/trips.json' : `${this.baseUrl}/trips`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Validate data with Zod
      const tripsSchema = z.array(TripSchema);
      const validatedTrips = tripsSchema.parse(data);
      
      return validatedTrips;
    } catch (error) {
      console.error('Failed to fetch trips:', error);
      // Return mock data as fallback
      return this.getMockTrips();
    }
  }

  async getTripById(tripId: string): Promise<Trip | null> {
    try {
      const url = this.useMockData 
        ? '/data/trips.json' 
        : `${this.baseUrl}/trips/${tripId}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (this.useMockData) {
        // Find specific trip in mock data
        const trips = z.array(TripSchema).parse(data);
        const trip = trips.find((t: Trip) => t.tripId === tripId);
        return trip || null;
      }
      
      // Validate single trip
      const validatedTrip = TripSchema.parse(data);
      return validatedTrip;
    } catch (error) {
      console.error(`Failed to fetch trip ${tripId}:`, error);
      return null;
    }
  }

  private getMockTrips(): Trip[] {
    return [
      {
        id: 1,
        transitNumber: 12345,
        description: 'Delivery to Downtown',
        entry: 'Port A',
        lastSeen: '2024-01-15 10:30:00',
        tracker: 'TRK001',
        driver: 'John Doe',
        vehicle: 'ABC-123',
        alerts: 'None',
        status: ['Active'],
        tripId: 'trip-001',
        tripStatus: 'activated',
        transitType: 'Commercial',
        creationDate: '2024-01-15 08:00:00',
        activationDate: '2024-01-15 09:00:00',
        completionDate: null,
        driver_details: {
          driverId: 'DRV001',
          driverName: 'John Doe',
          driverPassportNumber: 'P123456789',
          driverNationality: 'US',
          driverContactNo: '+1234567890'
        },
        vehicle_details: {
          vehicleId: 'VEH001',
          vehiclePlateNumber: 'ABC-123',
          trackerNo: 'TRK001',
          model: 'Ford Transit',
          color: 'White',
          type: 'Van',
          plateCountry: 'US'
        },
        route: {
          routeId: 'RT001',
          routeName: 'Downtown Route',
          entryPort: 'Port A',
          exitPort: 'Port B'
        },
        tracking: {
          currentLocation: {
            latitude: 40.7128,
            longitude: -74.0060,
            timestamp: new Date().toISOString()
          },
          completeDistance: 150.5,
          remainingDistance: 45.2,
          estimatedArrival: '2024-01-15 14:30:00',
          elocks: 'Secured'
        }
      },
      {
        id: 2,
        transitNumber: 12346,
        description: 'Pickup from Warehouse',
        entry: 'Port C',
        lastSeen: '2024-01-15 11:15:00',
        tracker: 'TRK002',
        driver: 'Jane Smith',
        vehicle: 'XYZ-789',
        alerts: 'Delay',
        status: ['Pending'],
        tripId: 'trip-002',
        tripStatus: 'pending',
        transitType: 'Personal',
        creationDate: '2024-01-15 07:30:00',
        activationDate: null,
        completionDate: null,
        driver_details: {
          driverId: 'DRV002',
          driverName: 'Jane Smith',
          driverPassportNumber: 'P987654321',
          driverNationality: 'CA',
          driverContactNo: '+1987654321'
        },
        vehicle_details: {
          vehicleId: 'VEH002',
          vehiclePlateNumber: 'XYZ-789',
          trackerNo: 'TRK002',
          model: 'Toyota Camry',
          color: 'Blue',
          type: 'Sedan',
          plateCountry: 'CA'
        },
        route: {
          routeId: 'RT002',
          routeName: 'Warehouse Route',
          entryPort: 'Port C',
          exitPort: 'Port D'
        },
        tracking: {
          currentLocation: {
            latitude: 40.7589,
            longitude: -73.9851,
            timestamp: new Date().toISOString()
          },
          completeDistance: 200.0,
          remainingDistance: 200.0,
          estimatedArrival: null,
          elocks: 'Not Applied'
        }
      }
    ];
  }
}

export const tripService = new TripService();
```

#### 2.3 Trip Store (`stores/tripStore.ts`)

```typescript
import { create } from 'zustand';
import { Trip } from '../types/trip';
import { tripService } from '../services/tripService';

interface TripStoreState {
  // State
  trips: Trip[];
  selectedTrip: Trip | null;
  isLoading: boolean;
  error: string | null;
  lastFetch: Date | null;
  
  // Actions
  fetchTrips: () => Promise<void>;
  selectTrip: (tripId: string) => void;
  clearSelection: () => void;
  refreshTrips: () => Promise<void>;
  clearError: () => void;
}

export const useTripStore = create<TripStoreState>((set, get) => ({
  // Initial state
  trips: [],
  selectedTrip: null,
  isLoading: false,
  error: null,
  lastFetch: null,

  // Fetch trips from API
  fetchTrips: async () => {
    try {
      set({ isLoading: true, error: null });
      const trips = await tripService.getTrips();
      set({ 
        trips, 
        isLoading: false, 
        lastFetch: new Date() 
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch trips',
        isLoading: false 
      });
    }
  },

  // Select a trip
  selectTrip: (tripId: string) => {
    const trip = get().trips.find(t => t.tripId === tripId);
    set({ selectedTrip: trip || null });
  },

  // Clear selection
  clearSelection: () => {
    set({ selectedTrip: null });
  },

  // Refresh trips (force refetch)
  refreshTrips: async () => {
    await get().fetchTrips();
  },

  // Clear error state
  clearError: () => {
    set({ error: null });
  }
}));
```

### Phase 3: Component Integration

#### 3.1 Update TripListView Component

Update your existing `components/TripListView.tsx`:

```typescript
import React, { useEffect } from 'react';
import { useTripStore } from '../stores/tripStore';
import { Trip } from '../types/trip';

export const TripListView: React.FC = () => {
  const {
    trips,
    selectedTrip,
    isLoading,
    error,
    lastFetch,
    fetchTrips,
    selectTrip,
    clearSelection,
    refreshTrips,
    clearError
  } = useTripStore();

  // Fetch trips on component mount
  useEffect(() => {
    fetchTrips();
  }, [fetchTrips]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'activated': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'ended': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleTripSelect = (trip: Trip) => {
    selectTrip(trip.tripId);
  };

  const handleRefresh = () => {
    refreshTrips();
  };

  if (isLoading && trips.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading trips...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Trip Management</h1>
        <div className="flex items-center space-x-4">
          {lastFetch && (
            <span className="text-sm text-gray-500">
              Last updated: {formatDate(lastFetch.toISOString())}
            </span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex justify-between items-center">
            <span className="text-red-800">Error: {error}</span>
            <button
              onClick={clearError}
              className="text-red-600 hover:text-red-800"
            >
              ×
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trip List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Trips ({trips.length})</h2>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {trips.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No trips available
              </div>
            ) : (
              trips.map((trip) => (
                <div
                  key={trip.tripId}
                  onClick={() => handleTripSelect(trip)}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                    selectedTrip?.tripId === trip.tripId ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-gray-900">
                          #{trip.transitNumber}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          getStatusBadgeColor(trip.tripStatus)
                        }`}>
                          {trip.tripStatus}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {trip.description}
                      </p>
                      <div className="text-xs text-gray-500 mt-2">
                        <div>Driver: {trip.driver}</div>
                        <div>Vehicle: {trip.vehicle}</div>
                        <div>Last seen: {trip.lastSeen}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Trip Details */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold">Trip Details</h2>
              {selectedTrip && (
                <button
                  onClick={clearSelection}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              )}
            </div>
          </div>
          <div className="p-4">
            {selectedTrip ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">
                    Trip #{selectedTrip.transitNumber}
                  </h3>
                  <p className="text-gray-600">{selectedTrip.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-700">Status</h4>
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      getStatusBadgeColor(selectedTrip.tripStatus)
                    }`}>
                      {selectedTrip.tripStatus}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-700">Type</h4>
                    <p className="text-gray-600">{selectedTrip.transitType}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Driver Details</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>Name:</strong> {selectedTrip.driver_details.driverName}</p>
                    <p><strong>ID:</strong> {selectedTrip.driver_details.driverId}</p>
                    <p><strong>Nationality:</strong> {selectedTrip.driver_details.driverNationality}</p>
                    {selectedTrip.driver_details.driverContactNo && (
                      <p><strong>Contact:</strong> {selectedTrip.driver_details.driverContactNo}</p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Vehicle Details</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>Plate:</strong> {selectedTrip.vehicle_details.vehiclePlateNumber}</p>
                    <p><strong>Tracker:</strong> {selectedTrip.vehicle_details.trackerNo}</p>
                    {selectedTrip.vehicle_details.model && (
                      <p><strong>Model:</strong> {selectedTrip.vehicle_details.model}</p>
                    )}
                    {selectedTrip.vehicle_details.color && (
                      <p><strong>Color:</strong> {selectedTrip.vehicle_details.color}</p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Route</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>Route:</strong> {selectedTrip.route.routeName}</p>
                    <p><strong>From:</strong> {selectedTrip.route.entryPort}</p>
                    <p><strong>To:</strong> {selectedTrip.route.exitPort}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Tracking Info</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>Distance:</strong> {selectedTrip.tracking.completeDistance} km</p>
                    <p><strong>Remaining:</strong> {selectedTrip.tracking.remainingDistance} km</p>
                    <p><strong>E-locks:</strong> {selectedTrip.tracking.elocks}</p>
                    {selectedTrip.tracking.estimatedArrival && (
                      <p><strong>ETA:</strong> {formatDate(selectedTrip.tracking.estimatedArrival)}</p>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                Select a trip to view details
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### 3.2 Usage in Pages

Use the component in your pages (e.g., `app/trips/page.tsx`):

```typescript
import { TripListView } from '@/components/TripListView';

export default function TripsPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <TripListView />
    </div>
  );
}
```

### Phase 4: Testing

#### 4.1 Create Mock Data File

Create `public/data/trips.json` with sample data:

```json
[
  {
    "id": 1,
    "transitNumber": 12345,
    "description": "Delivery to Downtown",
    "entry": "Port A",
    "lastSeen": "2024-01-15 10:30:00",
    "tracker": "TRK001",
    "driver": "John Doe",
    "vehicle": "ABC-123",
    "alerts": "None",
    "status": ["Active"],
    "tripId": "trip-001",
    "tripStatus": "activated",
    "transitType": "Commercial",
    "creationDate": "2024-01-15 08:00:00",
    "activationDate": "2024-01-15 09:00:00",
    "completionDate": null,
    "driver_details": {
      "driverId": "DRV001",
      "driverName": "John Doe",
      "driverPassportNumber": "P123456789",
      "driverNationality": "US",
      "driverContactNo": "+1234567890"
    },
    "vehicle_details": {
      "vehicleId": "VEH001",
      "vehiclePlateNumber": "ABC-123",
      "trackerNo": "TRK001",
      "model": "Ford Transit",
      "color": "White",
      "type": "Van",
      "plateCountry": "US"
    },
    "route": {
      "routeId": "RT001",
      "routeName": "Downtown Route",
      "entryPort": "Port A",
      "exitPort": "Port B"
    },
    "tracking": {
      "currentLocation": {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "timestamp": "2024-01-15T10:30:00Z"
      },
      "completeDistance": 150.5,
      "remainingDistance": 45.2,
      "estimatedArrival": "2024-01-15 14:30:00",
      "elocks": "Secured"
    }
  }
]
```

#### 4.2 Test the Implementation

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Navigate to your trips page**

3. **Verify functionality:**
   - Trips load from mock data
   - Trip selection works
   - Trip details display correctly
   - Refresh button works
   - Error handling works (try invalid JSON)

## Key Features

✅ **Simple Setup** - Minimal dependencies and configuration
✅ **Type Safety** - Full TypeScript support with Zod validation
✅ **Error Handling** - Graceful error handling with fallbacks
✅ **Loading States** - Proper loading indicators
✅ **Responsive Design** - Works on desktop and mobile
✅ **Mock Data Support** - Easy testing with JSON files
✅ **Zustand Store** - Centralized state management
✅ **Component Integration** - Ready-to-use TripListView component

## Next Steps

1. **Add filtering and search functionality**
2. **Implement trip status updates**
3. **Add pagination for large datasets**
4. **Integrate with real API endpoints**
5. **Add trip creation/editing capabilities**

This implementation provides a solid foundation for trip management without the complexity of real-time features, making it perfect for getting started quickly.