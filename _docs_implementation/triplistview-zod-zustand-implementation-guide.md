# TripListView: Zod & Zustand Implementation Guide

## Overview

This guide shows how to transform your TripListView component from local state management to a robust Zod + Zustand architecture with proper separation of concerns.


## 🏗️ Architecture Overview
The guide follows your exact file structure:

 infrastructure/validation/tripSchema.ts - Zod validation schemas
 infrastructure/api/tripService.ts - API service layer with caching
 stores/filterStore.ts - Dedicated filter state management
 stores/tripStore.ts - Main trip state management
Updated  components/TripListView.tsx - Enhanced component

🔧 Key Features Implemented
1. Zod Validation ( tripSchema.ts)
Complete schemas matching your Trip interface
Type-safe validation with runtime checks
Helper functions for safe parsing
Automatic TypeScript type generation

2. API Service Layer ( tripService.ts)
5-minute caching mechanism
Error handling with Zod validation
Cache management utilities
Clean separation from UI logic

3. Filter Store ( filterStore.ts)
Dedicated filter state management
Search, status, transit type, and date range filters
Persistent filter state
Efficient filter application logic

4. Trip Store ( tripStore.ts)
Centralized trip state management
Optimistic updates for better UX
Integration with filter store
Performance-optimized selector hooks

5. Enhanced TripListView Component
Proper loading and error states
Refresh functionality
Integration with both stores
Clean, maintainable code structure
✅ Implementation Benefits
Performance
Components only re-render when specific data changes
API response caching reduces unnecessary calls
Optimistic updates for immediate feedback
Store-level filtering for efficiency
Type Safety
Runtime validation ensures data integrity
Compile-time error checking
Auto-generated TypeScript types
Validation error handling
Developer Experience
Redux DevTools integration
Hot reloading with state persistence
Easy testing with isolated logic
Clear error messages and recovery
Architecture
Clear separation of concerns
Reusable service layer
Modular filter system
Scalable state management
📝 Step-by-Step Implementation
The guide provides:

Prerequisites checklist
File creation order
Complete code for each file
Usage examples
Testing strategies
Migration guide from current implementation
Troubleshooting section
## File Structure

```
stores/
├── tripStore.ts              # Main trip state management
├── filterStore.ts            # Filter state management
infrastructure/
├── validation/
│   └── tripSchema.ts         # Zod validation schemas
├── api/
│   └── tripService.ts        # API service layer
components/
└── TripListView.tsx          # Updated component
```

## Implementation Steps

### Step 1: Create Zod Validation Schemas

**File: `infrastructure/validation/tripSchema.ts`**

```typescript
import { z } from "zod";

// Base schemas for nested objects
export const TripDriverSchema = z.object({
  driverId: z.string(),
  driverName: z.string(),
  driverPassportNumber: z.string(),
  driverNationality: z.string(),
  driverContactNo: z.string().optional(),
});

export const TripVehicleSchema = z.object({
  vehicleId: z.string(),
  vehiclePlateNumber: z.string(),
  trackerNo: z.string(),
  model: z.string().optional(),
  color: z.string().optional(),
  type: z.string().optional(),
  plateCountry: z.string().optional(),
});

export const TripRouteSchema = z.object({
  routeId: z.string(),
  routeName: z.string(),
  entryPort: z.string(),
  exitPort: z.string(),
});

export const TripShipmentSchema = z.object({
  shipmentId: z.string(),
  shipmentDescription: z.string(),
  ownerDescription: z.string().optional(),
  shipmentDescriptionArabic: z.string().optional(),
});

export const TripTrackingSchema = z.object({
  currentLocation: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
      timestamp: z.string(),
    })
    .optional(),
  lastKnownLocation: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
      timestamp: z.string(),
    })
    .optional(),
  batteryLevel: z.number().optional(),
  signalStrength: z.number().optional(),
});

export const TripComplianceSchema = z.object({
  customsStatus: z.string().optional(),
  documentsStatus: z.string().optional(),
  securityStatus: z.string().optional(),
});

// Main Trip schema
export const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),
  status: z.array(z.enum(["active", "charging", "offline"])),
  tripId: z.string(),
  tripStatus: z.enum(["pending", "activated", "ended", "cancelled"]),
  transitType: z.string(),
  creationDate: z.string(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),
  transitSeqNo: z.string().optional(),
  declarationDate: z.string().optional(),
  startingDate: z.string().optional(),
  expectedArrivalDate: z.string().optional(),
  endDate: z.string().optional(),
  driver_details: TripDriverSchema,
  vehicle_details: TripVehicleSchema,
  route: TripRouteSchema,
  shipment: TripShipmentSchema,
  tracking: TripTrackingSchema,
  compliance: TripComplianceSchema,
});

// API Response schemas
export const TripsApiResponseSchema = z.array(TripSchema);

// Export types
export type Trip = z.infer<typeof TripSchema>;
export type TripStatus = Trip["tripStatus"];
export type TripDriver = z.infer<typeof TripDriverSchema>;
export type TripVehicle = z.infer<typeof TripVehicleSchema>;
export type TripRoute = z.infer<typeof TripRouteSchema>;
export type TripShipment = z.infer<typeof TripShipmentSchema>;
export type TripTracking = z.infer<typeof TripTrackingSchema>;
export type TripCompliance = z.infer<typeof TripComplianceSchema>;

// Validation helpers
export const validateTrip = (data: unknown): Trip => {
  return TripSchema.parse(data);
};

export const validateTrips = (data: unknown): Trip[] => {
  return TripsApiResponseSchema.parse(data);
};

export const safeValidateTrip = (data: unknown) => {
  return TripSchema.safeParse(data);
};

export const safeValidateTrips = (data: unknown) => {
  return TripsApiResponseSchema.safeParse(data);
};
```

### Step 2: Create API Service Layer

**File: `infrastructure/api/tripService.ts`**

```typescript
import {
  validateTrips,
  validateTrip,
  Trip,
  TripStatus,
} from "../validation/tripSchema";

export class TripService {
  private static readonly BASE_URL = "/data";
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private static cache: { data: Trip[]; timestamp: number } | null = null;

  /**
   * Fetch all trips with caching
   */
  static async fetchTrips(): Promise<Trip[]> {
    // Check cache first
    if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {
      return this.cache.data;
    }

    try {
      const response = await fetch(`${this.BASE_URL}/trips.json`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const rawData = await response.json();

      // Validate with Zod
      const validatedTrips = validateTrips(rawData);

      // Update cache
      this.cache = {
        data: validatedTrips,
        timestamp: Date.now(),
      };

      return validatedTrips;
    } catch (error) {
      console.error("Failed to fetch trips:", error);
      throw error;
    }
  }

  /**
   * Update trip status
   */
  static async updateTripStatus(
    tripId: string,
    status: TripStatus
  ): Promise<Trip> {
    try {
      // In a real app, this would be an API call
      const response = await fetch(`/api/trips/${tripId}/status`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update trip status");
      }

      const rawData = await response.json();
      return validateTrip(rawData);
    } catch (error) {
      console.error("Failed to update trip status:", error);
      throw error;
    }
  }

  /**
   * Clear cache (useful for force refresh)
   */
  static clearCache(): void {
    this.cache = null;
  }

  /**
   * Get cached data if available
   */
  static getCachedTrips(): Trip[] | null {
    if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {
      return this.cache.data;
    }
    return null;
  }
}
```

### Step 3: Create Filter Store

**File: `stores/filterStore.ts`**

```typescript
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { Trip, TripStatus } from "../infrastructure/validation/tripSchema";

export interface TripFilters {
  status?: TripStatus;
  transitType?: string;
  searchTerm?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

interface FilterState {
  filters: TripFilters;
  setFilters: (filters: Partial<TripFilters>) => void;
  clearFilters: () => void;
  applyFilters: (trips: Trip[]) => Trip[];
}

export const useFilterStore = create<FilterState>()(
  devtools(
    persist(
      (set, get) => ({
        filters: {},

        setFilters: (newFilters: Partial<TripFilters>) => {
          set((state) => ({
            filters: { ...state.filters, ...newFilters },
          }));
        },

        clearFilters: () => {
          set({ filters: {} });
        },

        applyFilters: (trips: Trip[]): Trip[] => {
          const { filters } = get();

          return trips.filter((trip) => {
            // Status filter
            if (filters.status && trip.tripStatus !== filters.status) {
              return false;
            }

            // Transit type filter
            if (
              filters.transitType &&
              trip.transitType !== filters.transitType
            ) {
              return false;
            }

            // Search term filter
            if (filters.searchTerm) {
              const searchLower = filters.searchTerm.toLowerCase();
              const searchableFields = [
                trip.description,
                trip.driver,
                trip.vehicle,
                trip.tripId,
                trip.transitNumber.toString(),
              ];

              const matches = searchableFields.some((field) =>
                field.toLowerCase().includes(searchLower)
              );

              if (!matches) return false;
            }

            // Date range filter
            if (filters.dateRange) {
              const tripDate = new Date(trip.creationDate);
              const startDate = new Date(filters.dateRange.start);
              const endDate = new Date(filters.dateRange.end);

              if (tripDate < startDate || tripDate > endDate) {
                return false;
              }
            }

            return true;
          });
        },
      }),
      {
        name: "trip-filters",
        partialize: (state) => ({ filters: state.filters }),
      }
    ),
    { name: "filter-store" }
  )
);

// Selector hooks
export const useTripFilters = () => useFilterStore((state) => state.filters);
export const useFilterActions = () =>
  useFilterStore((state) => ({
    setFilters: state.setFilters,
    clearFilters: state.clearFilters,
    applyFilters: state.applyFilters,
  }));
```

### Step 4: Create Main Trip Store

**File: `stores/tripStore.ts`**

```typescript
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { Trip, TripStatus } from "../infrastructure/validation/tripSchema";
import { TripService } from "../infrastructure/api/tripService";
import { useFilterStore } from "./filterStore";

interface TripState {
  // Data
  trips: Trip[];
  selectedTrip: Trip | null;

  // UI State
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number | null;

  // Pagination
  pagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
  };

  // Actions
  fetchTrips: () => Promise<void>;
  refreshTrips: () => Promise<void>;
  updateTripStatus: (tripId: string, status: TripStatus) => Promise<void>;
  selectTrip: (trip: Trip | null) => void;
  setPagination: (pagination: Partial<TripState["pagination"]>) => void;
  clearError: () => void;

  // Computed
  getFilteredTrips: () => Trip[];
}

export const useTripStore = create<TripState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        trips: [],
        selectedTrip: null,
        isLoading: false,
        error: null,
        lastFetchTime: null,
        pagination: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0,
        },

        // Actions
        fetchTrips: async () => {
          set({ isLoading: true, error: null });

          try {
            const trips = await TripService.fetchTrips();

            set({
              trips,
              isLoading: false,
              lastFetchTime: Date.now(),
              pagination: (state) => ({
                ...state.pagination,
                totalItems: trips.length,
              }),
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to fetch trips",
              isLoading: false,
            });
          }
        },

        refreshTrips: async () => {
          TripService.clearCache();
          await get().fetchTrips();
        },

        updateTripStatus: async (tripId: string, status: TripStatus) => {
          set({ isLoading: true, error: null });

          try {
            // Optimistic update
            set((state) => ({
              trips: state.trips.map((trip) =>
                trip.tripId === tripId ? { ...trip, tripStatus: status } : trip
              ),
            }));

            // API call
            await TripService.updateTripStatus(tripId, status);

            set({ isLoading: false });
          } catch (error) {
            // Revert optimistic update
            await get().fetchTrips();

            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to update trip",
              isLoading: false,
            });
          }
        },

        selectTrip: (trip: Trip | null) => {
          set({ selectedTrip: trip });
        },

        setPagination: (newPagination) => {
          set((state) => ({
            pagination: { ...state.pagination, ...newPagination },
          }));
        },

        clearError: () => {
          set({ error: null });
        },

        // Computed
        getFilteredTrips: () => {
          const { trips } = get();
          const { applyFilters } = useFilterStore.getState();
          return applyFilters(trips);
        },
      }),
      {
        name: "trip-storage",
        partialize: (state) => ({
          trips: state.trips,
          pagination: state.pagination,
          lastFetchTime: state.lastFetchTime,
        }),
      }
    ),
    { name: "trip-store" }
  )
);

// Selector hooks for performance
export const useTrips = () => useTripStore((state) => state.trips);
export const useFilteredTrips = () =>
  useTripStore((state) => state.getFilteredTrips());
export const useTripLoading = () => useTripStore((state) => state.isLoading);
export const useTripError = () => useTripStore((state) => state.error);
export const useSelectedTrip = () =>
  useTripStore((state) => state.selectedTrip);
export const useTripPagination = () =>
  useTripStore((state) => state.pagination);

// Action hooks
export const useTripActions = () =>
  useTripStore((state) => ({
    fetchTrips: state.fetchTrips,
    refreshTrips: state.refreshTrips,
    updateTripStatus: state.updateTripStatus,
    selectTrip: state.selectTrip,
    setPagination: state.setPagination,
    clearError: state.clearError,
  }));
```

### Step 5: Update TripListView Component

**File: `components/TripListView.tsx`**

```typescript
"use client";

import { DataTable } from "@/components/shared/DataTable";
import TripMasterDetailView from "@/components/TripMasterDetailView";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import { useEffect } from "react";
import { Trip } from "@/infrastructure/validation/tripSchema";
import {
  useFilteredTrips,
  useTripLoading,
  useTripError,
  useTripActions,
  useTripPagination,
} from "@/stores/tripStore";

const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
};

export default function TripListView() {
  // Zustand selectors
  const trips = useFilteredTrips();
  const isLoading = useTripLoading();
  const error = useTripError();
  const pagination = useTripPagination();

  // Actions
  const { fetchTrips, refreshTrips, clearError, selectTrip } = useTripActions();

  // Initialize data
  useEffect(() => {
    fetchTrips();
  }, [fetchTrips]);

  // Error component
  const ErrorDisplay = () => (
    <div className="flex items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
      <div className="text-center">
        <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-red-800 mb-2">
          Error Loading Trips
        </h3>
        <p className="text-red-600 mb-4">{error}</p>
        <div className="space-x-2">
          <button
            onClick={refreshTrips}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
          <button
            onClick={clearError}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Trip Management ({trips.length} trips)
        </h2>
        <button
          onClick={refreshTrips}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </button>
      </div>

      {/* Error state */}
      {error && <ErrorDisplay />}

      {/* Loading state */}
      {isLoading && trips.length === 0 && (
        <div className="flex items-center justify-center p-8">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mr-4" />
          <p className="text-gray-600">Loading trips...</p>
        </div>
      )}

      {/* Data table */}
      {!error && (
        <DataTable<Trip>
          keyField="id"
          data={trips}
          stickyHeader={true}
          expandable={true}
          expandRowByClick={false}
          pageSize={pagination.pageSize}
          filterable={false}
          searchable={false}
          sortable={true}
          selectable={false}
          exportable={true}
          exportFormats={["xlsx"]}
          exportFileNameKey="trip-data"
          loading={isLoading}
          onRowClick={selectTrip}
          columns={[
            {
              key: "transitNumber",
              title: "Transit Number",
              render: (row) => (
                <span className="text-blue-400 font-medium">
                  {row.transitNumber}
                </span>
              ),
            },
            { key: "description", title: "Shipment Description" },
            { key: "entry", title: "Entry-port - Exit-port" },
            { key: "lastSeen", title: "Last Seen" },
            { key: "tracker", title: "Tracker" },
            { key: "driver", title: "Driver Name" },
            { key: "vehicle", title: "Vehicle" },
            { key: "alerts", title: "Alerts" },
            {
              key: "tripStatus",
              title: "Trip Status",
              render: (row) => (
                <span
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    row.tripStatus === "activated"
                      ? "bg-green-100 text-green-800"
                      : row.tripStatus === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : row.tripStatus === "ended"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {row.tripStatus}
                </span>
              ),
            },
            {
              key: "status",
              title: "Device Status",
              render: (row) => (
                <div className="flex gap-1 items-center">
                  {row.status?.map((s, idx) => (
                    <span key={idx}>{statusConfig[s]?.icon}</span>
                  ))}
                </div>
              ),
            },
          ]}
          expandedRowRender={(row) => <TripMasterDetailView row={row} />}
        />
      )}

      {/* Empty state */}
      {!isLoading && !error && trips.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No trips found</p>
          <button
            onClick={refreshTrips}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh Data
          </button>
        </div>
      )}
    </div>
  );
}
```

## Quick Implementation Checklist

### ✅ Prerequisites

- [ ] Install dependencies: `npm install zod zustand`
- [ ] Ensure TypeScript strict mode is enabled

### ✅ File Creation Order

1. [ ] Create `infrastructure/validation/tripSchema.ts` - Zod schemas
2. [ ] Create `infrastructure/api/tripService.ts` - API service layer
3. [ ] Create `stores/filterStore.ts` - Filter state management
4. [ ] Create `stores/tripStore.ts` - Main trip state management
5. [ ] Update `components/TripListView.tsx` - Component implementation

### ✅ Key Benefits Achieved

#### **🔒 Type Safety**

- Runtime validation with Zod ensures data integrity
- TypeScript types automatically generated from schemas
- Compile-time error checking for all trip operations

#### **⚡ Performance**

- Selective component re-renders with Zustand selectors
- 5-minute API response caching
- Optimistic updates for immediate UI feedback
- Efficient filtering applied at store level

#### **🏗️ Architecture**

- Clear separation of concerns (validation, API, state, UI)
- Centralized state management
- Reusable service layer
- Modular filter system

#### **🛠️ Developer Experience**

- Redux DevTools integration for debugging
- Hot reloading with persistent state
- Easy testing with isolated store logic
- Clear error handling and recovery

### ✅ Usage Examples

#### **Basic Usage**

```typescript
// In any component
import { useFilteredTrips, useTripActions } from "@/stores/tripStore";

function MyComponent() {
  const trips = useFilteredTrips();
  const { fetchTrips, updateTripStatus } = useTripActions();

  // Component logic...
}
```

#### **Filter Usage**

```typescript
// In filter components
import { useTripFilters, useFilterActions } from "@/stores/filterStore";

function FilterComponent() {
  const filters = useTripFilters();
  const { setFilters, clearFilters } = useFilterActions();

  const handleStatusFilter = (status: TripStatus) => {
    setFilters({ status });
  };
}
```

#### **API Service Usage**

```typescript
// Direct service usage (if needed)
import { TripService } from "@/infrastructure/api/tripService";

// Get cached trips
const cachedTrips = TripService.getCachedTrips();

// Force refresh
TripService.clearCache();
const freshTrips = await TripService.fetchTrips();
```

### ✅ Testing Strategy

#### **Schema Testing**

```typescript
import {
  validateTrip,
  safeValidateTrip,
} from "@/infrastructure/validation/tripSchema";

test("should validate valid trip data", () => {
  const validTrip = {
    /* valid trip data */
  };
  expect(() => validateTrip(validTrip)).not.toThrow();
});

test("should handle invalid trip data", () => {
  const invalidTrip = {
    /* invalid trip data */
  };
  const result = safeValidateTrip(invalidTrip);
  expect(result.success).toBe(false);
});
```

#### **Store Testing**

```typescript
import { renderHook, act } from "@testing-library/react";
import { useTripStore } from "@/stores/tripStore";

test("should fetch trips successfully", async () => {
  const { result } = renderHook(() => useTripStore());

  await act(async () => {
    await result.current.fetchTrips();
  });

  expect(result.current.trips.length).toBeGreaterThan(0);
});
```

### ✅ Migration from Current Implementation

#### **Before (Current)**

```typescript
// Local state management
const [data, setData] = useState<Trip[]>([]);

useEffect(() => {
  fetch("/data/trips.json")
    .then((res) => res.json())
    .then(setData)
    .catch(console.error);
}, []);
```

#### **After (Zustand + Zod)**

```typescript
// Centralized state with validation
const trips = useFilteredTrips();
const { fetchTrips } = useTripActions();

useEffect(() => {
  fetchTrips(); // Handles validation, caching, error states
}, [fetchTrips]);
```

### ✅ Troubleshooting

#### **Common Issues**

1. **Zod validation errors**: Check your data structure matches the schema exactly
2. **Store not updating**: Ensure you're using the correct selector hooks
3. **Filters not working**: Verify filter store is properly connected to trip store
4. **Performance issues**: Use specific selector hooks instead of entire store state

#### **Debug Tools**

- Use Redux DevTools to inspect state changes
- Check browser console for Zod validation errors
- Use `TripService.getCachedTrips()` to inspect cached data
- Enable Zustand devtools in development

This implementation provides a robust, scalable, and maintainable solution for your TripListView component with full type safety and excellent developer experience.
