// Practical Example: User Management with Zod + Zustand
// This is a complete working example you can use in your Next.js project

import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// 1. Define Zod Schemas
const UserSchema = z.object({
  id: z.string(),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  age: z.number().min(18, 'Must be 18 or older').max(100, 'Must be under 100'),
  role: z.enum(['admin', 'user', 'moderator']),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
});

const CreateUserSchema = UserSchema.omit({ id: true, createdAt: true });

// 2. Generate TypeScript types from schemas
type User = z.infer<typeof UserSchema>;
type CreateUserData = z.infer<typeof CreateUserSchema>;

// 3. Define Zustand Store Interface
interface UserState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  addUser: (userData: CreateUserData) => Promise<void>;
  updateUser: (id: string, updates: Partial<User>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  fetchUsers: () => Promise<void>;
  clearError: () => void;
}

// 4. Create Zustand Store with Middleware
const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        users: [],
        isLoading: false,
        error: null,
        
        addUser: async (userData: CreateUserData) => {
          set({ isLoading: true, error: null });
          
          try {
            // Validate input data with Zod
            const validatedData = CreateUserSchema.parse(userData);
            
            // Create new user with generated ID and timestamp
            const newUser: User = {
              ...validatedData,
              id: crypto.randomUUID(),
              createdAt: new Date(),
            };
            
            // Validate the complete user object
            const validatedUser = UserSchema.parse(newUser);
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Add to store
            set((state) => ({
              users: [...state.users, validatedUser],
              isLoading: false,
            }));
            
            console.log('User added successfully:', validatedUser);
          } catch (error) {
            if (error instanceof z.ZodError) {
              const errorMessage = error.issues.map(issue => issue.message).join(', ');
              set({ error: `Validation error: ${errorMessage}`, isLoading: false });
            } else {
              set({ error: 'Failed to add user', isLoading: false });
            }
          }
        },
        
        updateUser: async (id: string, updates: Partial<User>) => {
          set({ isLoading: true, error: null });
          
          try {
            const currentUser = get().users.find(user => user.id === id);
            if (!currentUser) {
              throw new Error('User not found');
            }
            
            const updatedUser = { ...currentUser, ...updates };
            
            // Validate updated user
            const validatedUser = UserSchema.parse(updatedUser);
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 300));
            
            set((state) => ({
              users: state.users.map(user => 
                user.id === id ? validatedUser : user
              ),
              isLoading: false,
            }));
            
            console.log('User updated successfully:', validatedUser);
          } catch (error) {
            if (error instanceof z.ZodError) {
              const errorMessage = error.issues.map(issue => issue.message).join(', ');
              set({ error: `Validation error: ${errorMessage}`, isLoading: false });
            } else {
              set({ error: 'Failed to update user', isLoading: false });
            }
          }
        },
        
        deleteUser: async (id: string) => {
          set({ isLoading: true, error: null });
          
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 300));
            
            set((state) => ({
              users: state.users.filter(user => user.id !== id),
              isLoading: false,
            }));
            
            console.log('User deleted successfully');
          } catch (error) {
            set({ error: 'Failed to delete user', isLoading: false });
          }
        },
        
        fetchUsers: async () => {
          set({ isLoading: true, error: null });
          
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock API response
            const mockUsers = [
              {
                id: '1',
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30,
                role: 'admin' as const,
                isActive: true,
                createdAt: new Date('2024-01-01'),
              },
              {
                id: '2',
                name: 'Jane Smith',
                email: '<EMAIL>',
                age: 25,
                role: 'user' as const,
                isActive: true,
                createdAt: new Date('2024-01-02'),
              },
            ];
            
            // Validate each user from API
            const validatedUsers = mockUsers.map(user => UserSchema.parse(user));
            
            set({ users: validatedUsers, isLoading: false });
          } catch (error) {
            set({ error: 'Failed to fetch users', isLoading: false });
          }
        },
        
        clearError: () => set({ error: null }),
      }),
      {
        name: 'user-management-storage',
        partialize: (state) => ({ users: state.users }), // Only persist users
      }
    ),
    {
      name: 'user-management-store',
    }
  )
);

// 5. Form Component with Validation
const AddUserForm: React.FC = () => {
  const { addUser, isLoading, error, clearError } = useUserStore();
  const [formData, setFormData] = useState<Partial<CreateUserData>>({
    role: 'user',
    isActive: true,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormErrors({});
    
    try {
      await addUser(formData as CreateUserData);
      // Reset form on success
      setFormData({ role: 'user', isActive: true });
    } catch (error) {
      // Errors are handled in the store
    }
  };

  const handleInputChange = (field: keyof CreateUserData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Clear store error
    if (error) {
      clearError();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4 border rounded">
      <h3 className="text-lg font-semibold">Add New User</h3>
      
      {error && (
        <div className="p-2 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <div>
        <input
          type="text"
          placeholder="Name"
          value={formData.name || ''}
          onChange={(e) => handleInputChange('name', e.target.value)}
          className="w-full p-2 border rounded"
          required
        />
        {formErrors.name && <span className="text-red-500 text-sm">{formErrors.name}</span>}
      </div>
      
      <div>
        <input
          type="email"
          placeholder="Email"
          value={formData.email || ''}
          onChange={(e) => handleInputChange('email', e.target.value)}
          className="w-full p-2 border rounded"
          required
        />
        {formErrors.email && <span className="text-red-500 text-sm">{formErrors.email}</span>}
      </div>
      
      <div>
        <input
          type="number"
          placeholder="Age"
          value={formData.age || ''}
          onChange={(e) => handleInputChange('age', parseInt(e.target.value))}
          className="w-full p-2 border rounded"
          required
        />
        {formErrors.age && <span className="text-red-500 text-sm">{formErrors.age}</span>}
      </div>
      
      <div>
        <select
          value={formData.role || 'user'}
          onChange={(e) => handleInputChange('role', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="user">User</option>
          <option value="admin">Admin</option>
          <option value="moderator">Moderator</option>
        </select>
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          checked={formData.isActive || false}
          onChange={(e) => handleInputChange('isActive', e.target.checked)}
          className="mr-2"
        />
        <label>Active</label>
      </div>
      
      <button
        type="submit"
        disabled={isLoading}
        className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {isLoading ? 'Adding...' : 'Add User'}
      </button>
    </form>
  );
};

// 6. User List Component
const UserList: React.FC = () => {
  const { users, isLoading, error, fetchUsers, updateUser, deleteUser } = useUserStore();

  useEffect(() => {
    if (users.length === 0) {
      fetchUsers();
    }
  }, [fetchUsers, users.length]);

  const handleToggleActive = async (user: User) => {
    await updateUser(user.id, { isActive: !user.isActive });
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      await deleteUser(id);
    }
  };

  if (isLoading && users.length === 0) {
    return <div className="p-4">Loading users...</div>;
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Users ({users.length})</h3>
      
      {error && (
        <div className="p-2 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="space-y-2">
        {users.map((user) => (
          <div key={user.id} className="p-3 border rounded flex justify-between items-center">
            <div>
              <div className="font-medium">{user.name}</div>
              <div className="text-sm text-gray-600">{user.email}</div>
              <div className="text-sm text-gray-500">
                Age: {user.age} | Role: {user.role} | 
                Status: {user.isActive ? 'Active' : 'Inactive'}
              </div>
            </div>
            <div className="space-x-2">
              <button
                onClick={() => handleToggleActive(user)}
                className={`px-3 py-1 rounded text-sm ${
                  user.isActive 
                    ? 'bg-yellow-500 text-white hover:bg-yellow-600' 
                    : 'bg-green-500 text-white hover:bg-green-600'
                }`}
                disabled={isLoading}
              >
                {user.isActive ? 'Deactivate' : 'Activate'}
              </button>
              <button
                onClick={() => handleDelete(user.id)}
                className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                disabled={isLoading}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {users.length === 0 && !isLoading && (
        <div className="text-center text-gray-500 py-8">
          No users found. Add some users to get started!
        </div>
      )}
    </div>
  );
};

// 7. Main Component
const UserManagementExample: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Zod + Zustand User Management Example</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddUserForm />
        <UserList />
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h4 className="font-semibold mb-2">What this example demonstrates:</h4>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Zod schema validation for user data</li>
          <li>TypeScript type inference from Zod schemas</li>
          <li>Zustand store with async actions</li>
          <li>Error handling for validation and API calls</li>
          <li>Persistence with localStorage</li>
          <li>DevTools integration for debugging</li>
          <li>Form validation with real-time feedback</li>
          <li>CRUD operations with optimistic updates</li>
        </ul>
      </div>
    </div>
  );
};

export default UserManagementExample;
