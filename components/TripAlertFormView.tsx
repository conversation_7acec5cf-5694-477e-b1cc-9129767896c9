'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TripAlert } from '@/types/trip_alert';

interface TripAlertFormViewProps {
  tripAlert?: TripAlert | null;
  className?: string;
}

export const TripAlertFormView: React.FC<TripAlertFormViewProps> = ({
  tripAlert,
  className = ''
}) => {
  if (!tripAlert) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center text-gray-500">Alert details not found</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        {/* Alert Details Section */}
        <div className="mb-8">
          <h2 className="text-xl font-bold text-red-500 text-center mb-6">
            Al<PERSON>ails
          </h2>
          
          <div className="grid grid-cols-1 gap-1 text-sm">
            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Alert Type :</span>
              <span className="font-normal break-words">{tripAlert.alertType}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Alert Location :</span>
              <span className="font-normal break-words">{tripAlert.location.address}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Alert Comment :</span>
              <span className="font-normal break-words">{tripAlert.comments || ''}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Alert Status :</span>
              <span className="font-normal break-words">{tripAlert.alertStatus}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Received At :</span>
              <span className="font-normal break-words">{tripAlert.receivedAt}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Checkpost Name :</span>
              <span className="font-normal break-words">{tripAlert.checkpost?.name}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Checkpost Location :</span>
              <span className="font-normal break-words">{tripAlert.checkpost?.location.coordinates}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Checkpost Contact Number :</span>
              <span className="font-normal break-words">{tripAlert.checkpost?.contactNumber}</span>
            </div>
          </div>
        </div>

        {/* Trip Details Section */}
        <div>
          <h2 className="text-xl font-bold text-gray-700 text-center mb-6">
            Trip Details
          </h2>
          
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Transit Type :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.transitType}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Start Date :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.startDate}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Transit sequence no :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.transitSequenceNo}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Owner :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.owner}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Elocks :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.elocks}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Tracker :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.tracker}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Vehicle Details :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.vehicleDetails}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Complete Distance :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.completeDistance}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Remaining Distance :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.remainingDistance}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Driver Info :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.driverInfo}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Expected Arrival Date :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.expectedArrivalDate}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">End Date :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.endDate}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Security Notes :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.securityNotes}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Shipment Description :</span>
              <span className="font-normal break-words">{tripAlert.tripDetails?.shipmentDescription}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TripAlertFormView;