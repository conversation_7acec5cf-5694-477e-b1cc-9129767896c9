"use client";

import { DataTable } from "@/components/shared/DataTable";
import TripMasterDetailView from "@/components/TripMasterDetailView";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
} from "lucide-react";
import { useEffect } from "react";
import { useTripStore } from "@/stores/tripStore"; 
import { TripSchema } from "@/infrastructure/validation/tripSchema";

const statusConfig: Record<
  string,
  {
    color: string;
    icon: React.ReactNode;
  }
> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
};

export default function TripListView() {
  const {
    trips,
    fetchTrips,
    isLoading,
    error,
  } = useTripStore();

  useEffect(() => {
    fetchTrips(); 
    console.log(trips.length)
  }, [fetchTrips]);

  return (
    <div>
      {isLoading && <p>Loading trips...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      {!isLoading && (
        <DataTable<typeof TripSchema._type>
          keyField="id"
          data={trips}
          stickyHeader={true}
          expandable={true}
          expandRowByClick={false}
          pageSize={10}
          filterable={false}
          searchable={false}
          sortable={true}
          selectable={false}
          exportable={true}
          exportFormats={["xlsx"]}
          exportFileNameKey="trip-data"
          columns={[
            {
              key: "transitNumber",
              title: "Transit Number",
              render: (row) => (
                <span className="text-blue-400 font-medium">
                  {row.transitNumber}
                </span>
              ),
            },
            { key: "description", title: "Shipment Description" },
            { key: "entry", title: "Entry-port - Exit-port" },
            { key: "lastSeen", title: "Last Seen" },
            { key: "tracker", title: "Tracker" },
            { key: "driver", title: "Driver Name" },
            { key: "vehicle", title: "Vehicle" },
            { key: "alerts", title: "Alerts" },
            {
               key: "status",
        title: "Status",
        render: (row) => (
          <div className="flex gap-1 items-center">
            {row.status?.map((s, idx) => (
              <span key={idx}>{statusConfig[s]?.icon}</span>
            ))}
          </div>
            )
            },
          ]}
          expandedRowRender={(row) => <TripMasterDetailView row={row} />}
        />
      )}
    </div>
  );
}
