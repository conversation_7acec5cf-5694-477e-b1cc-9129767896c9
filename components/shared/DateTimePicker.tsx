"use client";

import React, { useState, useEffect, useRef } from "react";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";

// Types
type DateFormat =
  | "dd/mm/yyyy"
  | "mm/dd/yyyy"
  | "yyyy-mm-dd"
  | "dd-mm-yyyy"
  | "short"
  | "full";
type DateType = "gregorian" | "hijri";

interface HijriInfo {
  year: number;
  month: number;
  day: number;
  monthName: string;
}

interface DateTimePickerProps {
  value?: Date;
  onChange?: (date: Date) => void;
  label?: string;
  placeholder?: string;
  dateType?: DateType;
  format?: DateFormat;
  showTime?: boolean;
  timeFormat?: "12h" | "24h";
  allowTypeSwitch?: boolean;
  className?: string;
  disabled?: boolean;
  onFormattedDateChange?: (formattedDate: string) => void;
  // New props for self-contained component
  title?: string;
  showSelectedDate?: boolean;
  initialDate?: Date;
  containerClassName?: string;
  showActionButtons?: boolean;
}

// Hijri Date Utility Class
export class HijriDate {
  // Hijri month names in Arabic
  static hijriMonthsAr = [
    "محرم",
    "صفر",
    "ربيع الأول",
    "ربيع الثاني",
    "جمادى الأولى",
    "جمادى الثانية",
    "رجب",
    "شعبان",
    "رمضان",
    "شوال",
    "ذو القعدة",
    "ذو الحجة",
  ];

  // Hijri month names in English
  static hijriMonthsEn = [
    "Muharram",
    "Safar",
    "Rabi' al-awwal",
    "Rabi' al-thani",
    "Jumada al-awwal",
    "Jumada al-thani",
    "Rajab",
    "Sha'ban",
    "Ramadan",
    "Shawwal",
    "Dhu al-Qi'dah",
    "Dhu al-Hijjah",
  ];

  // Convert Gregorian date to Hijri
  static toHijri(gregorianDate: Date): HijriInfo {
    const jd = this.gregorianToJulian(gregorianDate);
    const hijri = this.julianToHijri(jd);

    return {
      year: hijri.year,
      month: hijri.month,
      day: hijri.day,
      monthName: this.hijriMonthsAr[hijri.month - 1],
    };
  }

  // Convert Hijri date to Gregorian
  static fromHijri(
    hijriYear: number,
    hijriMonth: number,
    hijriDay: number
  ): Date {
    const jd = this.hijriToJulian(hijriYear, hijriMonth, hijriDay);
    const gregorianDate = this.julianToGregorian(jd);
    return gregorianDate;
  }

  // Get number of days in Hijri month (Umm al-Qura accurate method)
  static getHijriMonthDays(year: number, month: number): number {
    // More accurate Umm al-Qura month length calculation
    // Based on the official Saudi Arabian calendar system
    const isLeapYear = this.isHijriLeapYear(year);

    // Standard month lengths in Umm al-Qura calendar
    // Months 1, 3, 5, 7, 9, 11 have 30 days
    // Months 2, 4, 6, 8, 10 have 29 days
    // Month 12 has 29 days in normal years, 30 days in leap years
    const standardLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
    
    if (month === 12 && isLeapYear) {
      return 30;
    }
    
    return standardLengths[month - 1];
  }

  // Check if Hijri year is leap year (Umm al-Qura accurate method)
  static isHijriLeapYear(year: number): boolean {
    // More accurate 30-year cycle with 11 leap years based on Umm al-Qura
    // This follows the official Saudi Arabian calendar system
    const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
    return leapYears.includes(year % 30);
  }

  // Helper: Convert Gregorian to Julian Day Number (more accurate)
  private static gregorianToJulian(date: Date): number {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // More precise Julian Day calculation
    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;

    const jd = day +
      Math.floor((153 * m + 2) / 5) +
      365 * y +
      Math.floor(y / 4) -
      Math.floor(y / 100) +
      Math.floor(y / 400) +
      1721119;

    return jd;
  }

  // Helper: Convert Julian Day Number to Hijri (Umm al-Qura accurate method)
  private static julianToHijri(jd: number): {
    year: number;
    month: number;
    day: number;
  } {
    // Umm al-Qura epoch: July 16, 622 CE (1 Muharram 1 AH)
    // Corrected epoch for better accuracy (adjusted by -1 day)
    const epochJd = 1948440;
    const daysSinceEpoch = jd - epochJd;

    // Handle negative days (before epoch)
    if (daysSinceEpoch < 0) {
      return { year: 1, month: 1, day: 1 };
    }

    // Estimate year using average lunar year length
    const avgLunarYear = 354.36707;
    let year = Math.floor(daysSinceEpoch / avgLunarYear) + 1;
    
    // Calculate total days up to estimated year
    let totalDays = 0;
    for (let y = 1; y < year; y++) {
      totalDays += this.isHijriLeapYear(y) ? 355 : 354;
    }

    // Adjust if estimate is too high
    while (totalDays > daysSinceEpoch) {
      year--;
      totalDays -= this.isHijriLeapYear(year) ? 355 : 354;
    }

    // Adjust if estimate is too low
    while (totalDays + (this.isHijriLeapYear(year) ? 355 : 354) <= daysSinceEpoch) {
      totalDays += this.isHijriLeapYear(year) ? 355 : 354;
      year++;
    }

    // Calculate remaining days in the year
    const daysInYear = daysSinceEpoch - totalDays;

    // Find the correct month within the year
    let month = 1;
    let daysInMonths = 0;
    while (month <= 12) {
      const monthLength = this.getHijriMonthDays(year, month);
      if (daysInMonths + monthLength > daysInYear) {
        break;
      }
      daysInMonths += monthLength;
      month++;
    }

    // Calculate the day of the month
    const day = daysInYear - daysInMonths + 1;

    return { year, month, day };
  }

  // Get current Hijri date as formatted string
  static getCurrentHijriDateString(language: "ar" | "en" = "ar"): string {
    const today = new Date();
    const hijriDate = this.toHijri(today);
    const monthNames =
      language === "ar" ? this.hijriMonthsAr : this.hijriMonthsEn;
    return `${hijriDate.day} ${monthNames[hijriDate.month - 1]} ${
      hijriDate.year
    } ${language === "ar" ? "هـ" : "AH"}`;
  }

  // Comprehensive test function for date conversion accuracy
  static testConversion(): void {
    
    // Test cases with known accurate conversions
    const testCases = [
      {
        gregorian: new Date(2040, 0, 1), // January 1, 2040
        expected: { day: 16, month: 12, year: 1461 },
        description: "January 1, 2040"
      },
      {
        gregorian: new Date(2025, 0, 1), // January 1, 2025
        expected: { day: 21, month: 6, year: 1446 },
        description: "January 1, 2025"
      },
      {
        gregorian: new Date(2024, 0, 1), // January 1, 2024
        expected: { day: 20, month: 6, year: 1445 },
        description: "January 1, 2024"
      }
    ];
    
    let passedTests = 0;
    const totalTests = testCases.length;
    const results: Array<{
      test: string;
      converted: string;
      expected: string;
      passed: boolean;
    }> = [];
    
    testCases.forEach((testCase) => {
      const hijriResult = this.toHijri(testCase.gregorian);
      
      const isCorrect = hijriResult.day === testCase.expected.day && 
                       hijriResult.month === testCase.expected.month && 
                       hijriResult.year === testCase.expected.year;
      
      if (isCorrect) {
        passedTests++;
      }
      
      results.push({
        test: testCase.description,
        converted: `${hijriResult.day}/${hijriResult.month}/${hijriResult.year}`,
        expected: `${testCase.expected.day}/${testCase.expected.month}/${testCase.expected.year}`,
        passed: isCorrect
      });
    });
    

    
    // Create summary for alert
    const summary = results.map(r => 
      `${r.test}: ${r.converted} ${r.passed ? '✓' : '✗'}`
    ).join('\n');
    
    const alertMessage = `Umm al-Qura Conversion Test Results:\n\n${summary}\n\nAccuracy: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`;
    
    if (typeof window !== 'undefined') {
      alert(alertMessage);
    }
  }

  // Helper function to format selected date based on picker type
  static formatSelectedDate(
    date: Date,
    isHijri: boolean = false,
    language: "ar" | "en" = "ar",
    showTime: boolean = false
  ): string {
    if (isHijri) {
      const hijriDate = this.toHijri(date);
      const monthNames =
        language === "ar" ? this.hijriMonthsAr : this.hijriMonthsEn;
      let dateStr = `${hijriDate.day} ${monthNames[hijriDate.month - 1]} ${
        hijriDate.year
      } ${language === "ar" ? "هـ" : "AH"}`;

      if (showTime) {
        const timeStr = date.toLocaleTimeString(
          language === "ar" ? "ar-SA" : "en-US",
          {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          }
        );
        dateStr += `, ${timeStr}`;
      }

      return dateStr;
    } else {
      if (showTime) {
        return date.toLocaleString(language === "ar" ? "ar-SA" : "en-US");
      } else {
        return date.toLocaleDateString(language === "ar" ? "ar-SA" : "en-US");
      }
    }
  }

  // Helper: Convert Hijri to Julian Day Number (Umm al-Qura accurate method)
  private static hijriToJulian(
    year: number,
    month: number,
    day: number
  ): number {
    // Umm al-Qura epoch: July 16, 622 CE (1 Muharram 1 AH)
    // Corrected epoch for consistency
    const epochJd = 1948440;
    
    // Calculate total days from epoch
    let totalDays = 0;
    
    // Add days for complete years
    for (let y = 1; y < year; y++) {
      totalDays += this.isHijriLeapYear(y) ? 355 : 354;
    }
    
    // Add days for complete months in the current year
    for (let m = 1; m < month; m++) {
      totalDays += this.getHijriMonthDays(year, m);
    }
    
    // Add remaining days (subtract 1 because we count from day 1)
    totalDays += day - 1;
    
    return epochJd + totalDays;
  }

  // Helper: Convert Julian Day Number to Gregorian
  private static julianToGregorian(jd: number): Date {
    const a = jd + 32044;
    const b = Math.floor((4 * a + 3) / 146097);
    const c = a - Math.floor((146097 * b) / 4);
    const d = Math.floor((4 * c + 3) / 1461);
    const e = c - Math.floor((1461 * d) / 4);
    const m = Math.floor((5 * e + 2) / 153);

    const day = e - Math.floor((153 * m + 2) / 5) + 1;
    const month = m + 3 - 12 * Math.floor(m / 10);
    const year = 100 * b + d - 4800 + Math.floor(m / 10);

    return new Date(year, month - 1, day);
  }
}

// Date formatting utility
function formatDateByType(
  date: Date | undefined,
  dateType: DateType,
  format: DateFormat,
  language: string,
  showTime: boolean = false,
  timeFormat: "12h" | "24h" = "24h",
  hijriInfo?: HijriInfo | null
): string {
  if (!date) return "";

  const formatTime = (date: Date): string => {
    if (timeFormat === "12h") {
      return date.toLocaleTimeString(language === "ar" ? "ar-SA" : "en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } else {
      return `${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`;
    }
  };

  let dateStr = "";

  if (dateType === "hijri") {
    const hijri = hijriInfo || HijriDate.toHijri(date);

    switch (format) {
      case "full":
        dateStr =
          language === "ar"
            ? `${hijri.day} ${hijri.monthName} ${hijri.year} هـ`
            : `${hijri.day} ${HijriDate.hijriMonthsEn[hijri.month - 1]} ${
                hijri.year
              } AH`;
        break;
      case "short":
        dateStr =
          language === "ar"
            ? `${hijri.day}/${hijri.month}/${hijri.year} هـ`
            : `${hijri.day}/${hijri.month}/${hijri.year} AH`;
        break;
      case "dd/mm/yyyy":
        dateStr = `${hijri.day.toString().padStart(2, "0")}/${hijri.month
          .toString()
          .padStart(2, "0")}/${hijri.year}`;
        break;
      case "mm/dd/yyyy":
        dateStr = `${hijri.month.toString().padStart(2, "0")}/${hijri.day
          .toString()
          .padStart(2, "0")}/${hijri.year}`;
        break;
      case "yyyy-mm-dd":
        dateStr = `${hijri.year}-${hijri.month
          .toString()
          .padStart(2, "0")}-${hijri.day.toString().padStart(2, "0")}`;
        break;
      case "dd-mm-yyyy":
        dateStr = `${hijri.day.toString().padStart(2, "0")}-${hijri.month
          .toString()
          .padStart(2, "0")}-${hijri.year}`;
        break;
    }
  } else {
    // Gregorian formatting
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    switch (format) {
      case "full":
        dateStr = date.toLocaleDateString(
          language === "ar" ? "ar-SA" : "en-US",
          {
            year: "numeric",
            month: "long",
            day: "numeric",
          }
        );
        break;
      case "short":
        dateStr = date.toLocaleDateString(
          language === "ar" ? "ar-SA" : "en-US",
          {
            year: "numeric",
            month: "short",
            day: "numeric",
          }
        );
        break;
      case "dd/mm/yyyy":
        dateStr = `${day.toString().padStart(2, "0")}/${month
          .toString()
          .padStart(2, "0")}/${year}`;
        break;
      case "mm/dd/yyyy":
        dateStr = `${month.toString().padStart(2, "0")}/${day
          .toString()
          .padStart(2, "0")}/${year}`;
        break;
      case "yyyy-mm-dd":
        dateStr = `${year}-${month.toString().padStart(2, "0")}-${day
          .toString()
          .padStart(2, "0")}`;
        break;
      case "dd-mm-yyyy":
        dateStr = `${day.toString().padStart(2, "0")}-${month
          .toString()
          .padStart(2, "0")}-${year}`;
        break;
    }
  }

  return showTime ? `${dateStr} ${formatTime(date)}` : dateStr;
}

// Main DateTimePicker Component
export default function DateTimePicker({
  value,
  onChange,
  label,
  placeholder,
  dateType = "hijri",
  format = "full",
  showTime = false,
  timeFormat = "24h",
  allowTypeSwitch = false,
  className = "",
  disabled = false,
  onFormattedDateChange,
  // New self-contained props
  title,
  showSelectedDate = false,
  initialDate,
  containerClassName = "",
  showActionButtons = false,
}: DateTimePickerProps) {
  const { language } = useLanguage();

  // Internal state for self-contained mode
  const [internalDate, setInternalDate] = useState<Date | undefined>(
    initialDate
  );
  const [internalFormattedDate, setInternalFormattedDate] =
    useState<string>("");

  // Use internal state if no external value/onChange provided (self-contained mode)
  const isStandalone = !value && !onChange;
  const currentValue = isStandalone ? internalDate : value;

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    currentValue
  );
  const [selectedHijriDate, setSelectedHijriDate] = useState<HijriInfo | null>(
    null
  );
  const [currentDateType, setCurrentDateType] = useState<DateType>(dateType);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [isManualInput, setIsManualInput] = useState(false);
  const [inputTimeout, setInputTimeout] = useState<NodeJS.Timeout | null>(null);
  const [justSelectedFromInput, setJustSelectedFromInput] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState({ hours: 0, minutes: 0 });
  const [calendarPosition, setCalendarPosition] = useState<"bottom" | "top">(
    "bottom"
  );

  // Navigation view state
  const [viewMode, setViewMode] = useState<"days" | "months" | "years">("days");
  const [yearRangeStart, setYearRangeStart] = useState(() => {
    const currentYear = currentMonth.getFullYear();
    return Math.floor(currentYear / 10) * 10;
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize with today's date if no value provided
  useEffect(() => {
    const today = new Date();
    if (!value) {
      setSelectedDate(today);
      if (currentDateType === "hijri") {
        setSelectedHijriDate(HijriDate.toHijri(today));
      }
    } else {
      setSelectedDate(value);
      if (currentDateType === "hijri") {
        setSelectedHijriDate(HijriDate.toHijri(value));
      }
    }
  }, [value]);

  // Handle calendar type switching - update current month to show equivalent date
  useEffect(() => {
    if (selectedDate) {
      // When switching calendar types, update the current month to show the selected date
      setCurrentMonth(selectedDate);
      
      // Update hijri date info when switching to hijri
      if (currentDateType === "hijri") {
        setSelectedHijriDate(HijriDate.toHijri(selectedDate));
      }
    }
  }, [currentDateType, selectedDate]);

  // Update input value when date changes (but not during manual input)
  useEffect(() => {
    if (!isManualInput && !justSelectedFromInput) {
      if (selectedDate) {
        const formatted = formatDateByType(
          selectedDate,
          currentDateType,
          format,
          language,
          showTime,
          timeFormat,
          selectedHijriDate
        );
        setInputValue(formatted);

        // Call the formatted date callback if provided
        if (onFormattedDateChange) {
          const formattedForDisplay = HijriDate.formatSelectedDate(
            selectedDate,
            currentDateType === "hijri",
            language as "ar" | "en",
            showTime
          );
          onFormattedDateChange(formattedForDisplay);
        }
      } else {
        setInputValue("");
        if (onFormattedDateChange) {
          onFormattedDateChange("");
        }
      }
    }

    // Reset the flag after processing
    if (justSelectedFromInput) {
      setJustSelectedFromInput(false);
    }
  }, [
    selectedDate,
    currentDateType,
    format,
    language,
    showTime,
    timeFormat,
    selectedHijriDate,
    onFormattedDateChange,
    isManualInput,
    justSelectedFromInput,
  ]);

  // Parse formatted date (supports multiple formats)
  const parseFormattedDate = (value: string): Date | null => {
    // Try to match Hijri date pattern: "5 Safar 1447 AH" or "5 صفر 1447 هـ"
    const hijriPattern = /^(\d{1,2})\s+([^\d\s]+)\s+(\d{4})\s*(AH|هـ)?$/i;
    const hijriMatch = value.match(hijriPattern);

    if (hijriMatch && currentDateType === "hijri") {
      const [, day, monthName, year] = hijriMatch;
      const dayNum = parseInt(day);
      const yearNum = parseInt(year);

      // Find month number from month name
      let monthNum = -1;

      // Check English month names
      const englishIndex = HijriDate.hijriMonthsEn.findIndex(
        (month) => month.toLowerCase() === monthName.toLowerCase()
      );
      if (englishIndex !== -1) {
        monthNum = englishIndex + 1;
      }

      // Check Arabic month names
      if (monthNum === -1) {
        const arabicIndex = HijriDate.hijriMonthsAr.findIndex(
          (month) => month === monthName
        );
        if (arabicIndex !== -1) {
          monthNum = arabicIndex + 1;
        }
      }

      // Validate ranges
      if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12) {
        try {
          return HijriDate.fromHijri(yearNum, monthNum, dayNum);
        } catch {
          return null;
        }
      }
    }

    // Try to match Gregorian date pattern: "July 30, 2025" or "30 July 2025"
    const gregorianPattern1 = /^([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})$/i; // "July 30, 2025"
    const gregorianPattern2 = /^(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})$/i; // "30 July 2025"

    const gregorianMatch1 = value.match(gregorianPattern1);
    const gregorianMatch2 = value.match(gregorianPattern2);

    if (gregorianMatch1 || gregorianMatch2) {
      let monthName: string, day: string, year: string;

      if (gregorianMatch1) {
        [, monthName, day, year] = gregorianMatch1;
      } else {
        [, day, monthName, year] = gregorianMatch2!;
      }

      const dayNum = parseInt(day);
      const yearNum = parseInt(year);

      // English month names
      const gregorianMonths = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];

      const monthIndex = gregorianMonths.findIndex(
        (month) => month.toLowerCase() === monthName.toLowerCase()
      );

      if (monthIndex !== -1 && dayNum >= 1 && dayNum <= 31) {
        try {
          const parsedDate = new Date(yearNum, monthIndex, dayNum);
          if (!isNaN(parsedDate.getTime())) {
            return parsedDate;
          }
        } catch {
          return null;
        }
      }
    }

    return null;
  };

  // Handle manual input changes and sync with calendar
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsManualInput(true);
    setInputValue(value);

    // Clear existing timeout
    if (inputTimeout) {
      clearTimeout(inputTimeout);
    }

    // Try to parse the input and update calendar view immediately
    if (value.trim()) {
      let parsedDate: Date | null = null;

      // First try to parse formatted date (e.g., "5 Safar 1447 AH" or "July 30, 2025")
      parsedDate = parseFormattedDate(value);

      // If that didn't work, try numeric format (e.g., "5/8/1445")
      if (!parsedDate) {
        const dateMatch = value.match(
          /^(\d{1,2})[\/\-\s\.]+(\d{1,2})[\/\-\s\.]+(\d{4})$/
        );

        if (dateMatch) {
          const [, day, month, year] = dateMatch;
          const dayNum = parseInt(day);
          const monthNum = parseInt(month);
          const yearNum = parseInt(year);

          // Validate basic ranges
          if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12) {
            try {
              if (currentDateType === "hijri") {
                // Convert Hijri to Gregorian
                parsedDate = HijriDate.fromHijri(yearNum, monthNum, dayNum);
              } else {
                // Create Gregorian date
                parsedDate = new Date(yearNum, monthNum - 1, dayNum);
              }
            } catch {
              // Ignore parsing errors
            }
          }
        }
      }

      // If we successfully parsed a date, select it
      if (parsedDate && !isNaN(parsedDate.getTime())) {
        // Clear timeout since we successfully parsed
        if (inputTimeout) {
          clearTimeout(inputTimeout);
          setInputTimeout(null);
        }

        // Navigate calendar to the parsed date
        setCurrentMonth(parsedDate);

        // Set flag to prevent useEffect from overriding input value
        setJustSelectedFromInput(true);

        // Select the date immediately
        handleDateSelect(parsedDate);

        // Allow formatting after a brief delay
        setTimeout(() => {
          setIsManualInput(false);
        }, 200);

        return; // Exit early since we successfully parsed and selected
      }
    }

    // Set timeout to reset manual input flag after 1.5 seconds of no typing
    // Only if we didn't successfully parse a date above
    const timeout = setTimeout(() => {
      setIsManualInput(false);
    }, 1500);
    setInputTimeout(timeout);
  };

  // Handle Enter key press to confirm input
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const value = (e.target as HTMLInputElement).value;
      let parsedDate: Date | null = null;

      // First try to parse formatted date (e.g., "5 Safar 1447 AH" or "July 30, 2025")
      parsedDate = parseFormattedDate(value);

      // If that didn't work, try numeric format
      if (!parsedDate) {
        const dateMatch = value.match(
          /(\d{1,2})[\/\-\s\.]+(\d{1,2})[\/\-\s\.]+(\d{4})/
        );

        if (dateMatch) {
          const [, day, month, year] = dateMatch;
          const dayNum = parseInt(day);
          const monthNum = parseInt(month);
          const yearNum = parseInt(year);

          if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12) {
            try {
              if (currentDateType === "hijri") {
                parsedDate = HijriDate.fromHijri(yearNum, monthNum, dayNum);
              } else {
                parsedDate = new Date(yearNum, monthNum - 1, dayNum);
              }
            } catch {
              // Ignore parsing errors
            }
          }
        }
      }

      // If we successfully parsed a date, select it
      if (parsedDate && !isNaN(parsedDate.getTime())) {
        setCurrentMonth(parsedDate);
        setJustSelectedFromInput(true);
        setIsManualInput(false);
        handleDateSelect(parsedDate);
        setIsOpen(false); // Close calendar on Enter
      }
    }
  };

  // Navigation functions for month/year selection
  const handleMonthClick = () => {
    setViewMode("months");
  };

  const handleYearClick = () => {
    setViewMode("years");
    const currentYear =
      currentDateType === "hijri"
        ? HijriDate.toHijri(currentMonth).year
        : currentMonth.getFullYear();
    setYearRangeStart(Math.floor(currentYear / 10) * 10);
  };

  const handleMonthSelect = (monthIndex: number) => {
    if (currentDateType === "hijri") {
      const currentHijri = HijriDate.toHijri(currentMonth);
      const newHijriDate = HijriDate.fromHijri(
        currentHijri.year,
        monthIndex + 1,
        1
      );
      setCurrentMonth(newHijriDate);
    } else {
      const newDate = new Date(currentMonth.getFullYear(), monthIndex, 1);
      setCurrentMonth(newDate);
    }
    setViewMode("days");
  };

  const handleYearSelect = (year: number) => {
    if (currentDateType === "hijri") {
      const currentHijri = HijriDate.toHijri(currentMonth);
      const newHijriDate = HijriDate.fromHijri(year, currentHijri.month, 1);
      setCurrentMonth(newHijriDate);
    } else {
      const newDate = new Date(year, currentMonth.getMonth(), 1);
      setCurrentMonth(newDate);
    }
    setViewMode("months");
  };

  // Handle outside click to close calendar and position calculation
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    function calculatePosition() {
      if (inputRef.current && isOpen) {
        const rect = inputRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;

        // If there's less than 400px below but more than 400px above, position on top
        if (spaceBelow < 400 && spaceAbove > 400) {
          setCalendarPosition("top");
        } else {
          setCalendarPosition("bottom");
        }
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      calculatePosition();
      window.addEventListener("scroll", calculatePosition);
      window.addEventListener("resize", calculatePosition);

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        window.removeEventListener("scroll", calculatePosition);
        window.removeEventListener("resize", calculatePosition);
      };
    }
  }, [isOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (inputTimeout) {
        clearTimeout(inputTimeout);
      }
    };
  }, [inputTimeout]);

  const handleDateSelect = (date: Date, hijriInfo?: HijriInfo) => {
    const newDate = new Date(date);

    if (showTime) {
      newDate.setHours(selectedTime.hours, selectedTime.minutes);
    }

    // Clear any pending timeout and reset manual input flag
    if (inputTimeout) {
      clearTimeout(inputTimeout);
      setInputTimeout(null);
    }
    setIsManualInput(false);
    setSelectedDate(newDate);

    if (hijriInfo) {
      setSelectedHijriDate(hijriInfo);
    } else if (currentDateType === "hijri") {
      setSelectedHijriDate(HijriDate.toHijri(newDate));
    }

    // Handle both external onChange and internal state
    if (isStandalone) {
      setInternalDate(newDate);
      // Update internal formatted date
      const formattedForDisplay = HijriDate.formatSelectedDate(
        newDate,
        currentDateType === "hijri",
        language as "ar" | "en",
        showTime
      );
      setInternalFormattedDate(formattedForDisplay);
    } else {
      onChange?.(newDate);
    }

    if (!showTime) {
      setIsOpen(false);
    }
  };

  const handleTimeChange = (hours: number, minutes: number) => {
    setSelectedTime({ hours, minutes });

    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(hours, minutes);
      setSelectedDate(newDate);
      onChange?.(newDate);
    }
  };

  const renderMonthSelector = () => {
    const months =
      currentDateType === "hijri"
        ? language === "ar"
          ? HijriDate.hijriMonthsAr
          : HijriDate.hijriMonthsEn
        : Array.from({ length: 12 }, (_, i) =>
            new Date(2000, i, 1).toLocaleDateString(
              language === "ar" ? "ar-SA" : "en-US",
              { month: "long" }
            )
          );

    const currentYear =
      currentDateType === "hijri"
        ? HijriDate.toHijri(currentMonth).year
        : currentMonth.getFullYear();

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (currentDateType === "hijri") {
                const currentHijri = HijriDate.toHijri(currentMonth);
                const newHijriDate = HijriDate.fromHijri(
                  currentHijri.year - 1,
                  currentHijri.month,
                  1
                );
                setCurrentMonth(newHijriDate);
              } else {
                setCurrentMonth(
                  new Date(
                    currentMonth.getFullYear() - 1,
                    currentMonth.getMonth(),
                    1
                  )
                );
              }
            }}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <button
            onClick={handleYearClick}
            className="font-medium text-sm hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200"
          >
            {currentYear}
          </button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (currentDateType === "hijri") {
                const currentHijri = HijriDate.toHijri(currentMonth);
                const newHijriDate = HijriDate.fromHijri(
                  currentHijri.year + 1,
                  currentHijri.month,
                  1
                );
                setCurrentMonth(newHijriDate);
              } else {
                setCurrentMonth(
                  new Date(
                    currentMonth.getFullYear() + 1,
                    currentMonth.getMonth(),
                    1
                  )
                );
              }
            }}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-1.5">
          {months.map((month, index) => (
            <button
              key={index}
              onClick={() => handleMonthSelect(index)}
              className={cn(
                "p-2 text-xs rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200 border border-transparent hover:border-blue-200",
                (currentDateType === "hijri"
                  ? HijriDate.toHijri(currentMonth).month - 1 === index
                  : currentMonth.getMonth() === index) &&
                  "bg-blue-500 text-white hover:bg-blue-600 border-blue-500"
              )}
            >
              {month}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderYearSelector = () => {
    const years = Array.from({ length: 10 }, (_, i) => yearRangeStart + i);
    const currentYear =
      currentDateType === "hijri"
        ? HijriDate.toHijri(currentMonth).year
        : currentMonth.getFullYear();

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setYearRangeStart(yearRangeStart - 10)}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="font-medium">
            {yearRangeStart} - {yearRangeStart + 9}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setYearRangeStart(yearRangeStart + 10)}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-1.5">
          {years.map((year) => (
            <button
              key={year}
              onClick={() => handleYearSelect(year)}
              className={cn(
                "p-2 text-xs rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200",
                currentYear === year &&
                  "bg-blue-500 text-white hover:bg-blue-600"
              )}
            >
              {year}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderCalendar = () => {
    if (viewMode === "months") {
      return renderMonthSelector();
    } else if (viewMode === "years") {
      return renderYearSelector();
    } else if (currentDateType === "hijri") {
      return renderHijriCalendar();
    } else {
      return renderGregorianCalendar();
    }
  };

  const renderGregorianCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    const today = new Date();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8" />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isToday = date.toDateString() === today.toDateString();
      const isSelected =
        selectedDate && date.toDateString() === selectedDate.toDateString();

      days.push(
        <button
          key={day}
          onClick={() => handleDateSelect(date)}
          className={cn(
            "w-8 h-8 text-sm font-medium rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200",
            isToday &&
              "ring-1 ring-blue-400 font-semibold bg-blue-50 text-blue-600",
            isSelected &&
              "bg-blue-500 text-white hover:bg-blue-600",
            !isSelected && !isToday && "hover:bg-blue-100 hover:text-blue-700"
          )}
        >
          {day}
        </button>
      );
    }

    return (
      <div className="px-4 pb-3">
        <div className="flex items-center justify-between mb-4">
          <button
            className="p-1.5 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            onClick={() => setCurrentMonth(new Date(year, month - 1, 1))}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleMonthClick}
              className="font-medium text-sm hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 border border-transparent hover:border-blue-200"
            >
              {currentMonth.toLocaleDateString(
                language === "ar" ? "ar-SA" : "en-US",
                { month: "long" }
              )}
            </button>
            <button
              onClick={handleYearClick}
              className="font-medium text-sm hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 border border-transparent hover:border-blue-200"
            >
              {currentMonth.getFullYear()}
            </button>
          </div>
          <button
            className="p-1.5 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            onClick={() => setCurrentMonth(new Date(year, month + 1, 1))}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-3">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div
              key={day}
              className="h-6 text-xs font-medium text-gray-500 flex items-center justify-center"
            >
              {language === "ar"
                ? ["أحد", "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"][
                    ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].indexOf(
                      day
                    )
                  ]
                : day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  const renderHijriCalendar = () => {
    const currentHijri = HijriDate.toHijri(currentMonth);
    const hijriMonths =
      language === "ar" ? HijriDate.hijriMonthsAr : HijriDate.hijriMonthsEn;

    // Get the first day of the current Hijri month in Gregorian
    const firstDayGregorian = HijriDate.fromHijri(
      currentHijri.year,
      currentHijri.month,
      1
    );
    const daysInMonth = HijriDate.getHijriMonthDays(
      currentHijri.year,
      currentHijri.month
    );
    const startingDayOfWeek = firstDayGregorian.getDay();

    const days = [];
    const today = new Date();
    const todayHijri = HijriDate.toHijri(today);

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8" />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const gregorianDate = HijriDate.fromHijri(
        currentHijri.year,
        currentHijri.month,
        day
      );
      const hijriInfo: HijriInfo = {
        year: currentHijri.year,
        month: currentHijri.month,
        day: day,
        monthName: hijriMonths[currentHijri.month - 1],
      };

      const isToday =
        todayHijri.year === currentHijri.year &&
        todayHijri.month === currentHijri.month &&
        todayHijri.day === day;

      const isSelected =
        selectedHijriDate &&
        selectedHijriDate.year === currentHijri.year &&
        selectedHijriDate.month === currentHijri.month &&
        selectedHijriDate.day === day;

      days.push(
        <button
          key={day}
          onClick={() => handleDateSelect(gregorianDate, hijriInfo)}
          className={cn(
            "w-8 h-8 text-sm font-medium rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200",
            isToday &&
              "ring-1 ring-blue-400 font-semibold bg-blue-50 text-blue-600",
            isSelected &&
              "bg-blue-500 text-white hover:bg-blue-600",
            !isSelected && !isToday && "hover:bg-blue-100 hover:text-blue-700"
          )}
        >
          {day}
        </button>
      );
    }

    return (
      <div className="px-4 pb-3">
        <div className="flex items-center justify-between mb-4">
          <button
            className="p-1.5 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            onClick={() => {
              const prevMonth =
                currentHijri.month === 1 ? 12 : currentHijri.month - 1;
              const prevYear =
                currentHijri.month === 1
                  ? currentHijri.year - 1
                  : currentHijri.year;
              const prevMonthGregorian = HijriDate.fromHijri(
                prevYear,
                prevMonth,
                1
              );
              setCurrentMonth(prevMonthGregorian);
            }}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleMonthClick}
              className="font-medium text-sm hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 border border-transparent hover:border-blue-200"
            >
              {hijriMonths[currentHijri.month - 1]}
            </button>
            <button
              onClick={handleYearClick}
              className="font-medium text-sm hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 border border-transparent hover:border-blue-200"
            >
              {currentHijri.year}
            </button>
          </div>
          <button
            className="p-1.5 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            onClick={() => {
              const nextMonth =
                currentHijri.month === 12 ? 1 : currentHijri.month + 1;
              const nextYear =
                currentHijri.month === 12
                  ? currentHijri.year + 1
                  : currentHijri.year;
              const nextMonthGregorian = HijriDate.fromHijri(
                nextYear,
                nextMonth,
                1
              );
              setCurrentMonth(nextMonthGregorian);
            }}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-3">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div
              key={day}
              className="h-6 text-xs font-medium text-gray-500 flex items-center justify-center"
            >
              {language === "ar"
                ? ["أحد", "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"][
                    ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].indexOf(
                      day
                    )
                  ]
                : day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  const renderTimePicker = () => {
    if (!showTime) return null;

    return (
      <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-xs font-medium text-gray-600">
              {language === "ar" ? "الساعة" : "Hour"}
            </label>
            <select
              value={selectedTime.hours}
              onChange={(e) =>
                handleTimeChange(parseInt(e.target.value), selectedTime.minutes)
              }
              className="px-2 py-1 border-0 bg-white rounded-lg shadow-sm ring-1 ring-gray-200 text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {Array.from({ length: 24 }, (_, i) => (
                <option key={i} value={i}>
                  {timeFormat === "12h"
                    ? (i === 0 ? 12 : i > 12 ? i - 12 : i)
                        .toString()
                        .padStart(2, "0")
                    : i.toString().padStart(2, "0")}
                </option>
              ))}
            </select>
            {timeFormat === "12h" && (
              <select
                value={selectedTime.hours >= 12 ? "PM" : "AM"}
                onChange={(e) => {
                  const isPM = e.target.value === "PM";
                  let newHours = selectedTime.hours;
                  if (isPM && selectedTime.hours < 12) {
                    newHours += 12;
                  } else if (!isPM && selectedTime.hours >= 12) {
                    newHours -= 12;
                  }
                  handleTimeChange(newHours, selectedTime.minutes);
                }}
                className="px-2 py-1 border-0 bg-white rounded-lg shadow-sm ring-1 ring-gray-200 text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:outline-none"
              >
                <option value="AM">AM</option>
                <option value="PM">PM</option>
              </select>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <label className="text-xs font-medium text-gray-600">
              {language === "ar" ? "الدقيقة" : "Minute"}
            </label>
            <select
              value={selectedTime.minutes}
              onChange={(e) =>
                handleTimeChange(selectedTime.hours, parseInt(e.target.value))
              }
              className="px-2 py-1 border-0 bg-white rounded-lg shadow-sm ring-1 ring-gray-200 text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {Array.from({ length: 60 }, (_, i) => (
                <option key={i} value={i}>
                  {i.toString().padStart(2, "0")}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to get color classes based on picker type
  const getColorClasses = () => {
    if (showTime) return "bg-blue-50 border-blue-200 text-blue-800";
    if (allowTypeSwitch)
      return "bg-purple-50 border-purple-200 text-purple-800";
    if (dateType === "gregorian")
      return "bg-orange-50 border-orange-200 text-orange-800";
    return "bg-green-50 border-green-200 text-green-800";
  };

  const pickerComponent = (
    <div
      className={cn("relative flex flex-col gap-2", className)}
      ref={containerRef}
    >
      {label && (
        <label className="text-sm font-medium text-gray-700">{label}</label>
      )}

      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onBlur={(e) => {
            // Only reset manual input if we're not clicking on the calendar
            const relatedTarget = e.relatedTarget as HTMLElement;
            const isClickingOnCalendar =
              relatedTarget &&
              (relatedTarget.closest(".calendar-container") ||
                relatedTarget.closest('[role="button"]'));

            if (!isClickingOnCalendar) {
              // Try to parse and select the current input value on blur
              const value = (e.target as HTMLInputElement).value;
              let parsedDate: Date | null = null;

              // First try to parse formatted date (e.g., "5 Safar 1447 AH" or "July 30, 2025")
              parsedDate = parseFormattedDate(value);

              // If that didn't work, try numeric format
              if (!parsedDate) {
                const dateMatch = value.match(
                  /^(\d{1,2})[\/\-\s\.]+(\d{1,2})[\/\-\s\.]+(\d{4})$/
                );

                if (dateMatch) {
                  const [, day, month, year] = dateMatch;
                  const dayNum = parseInt(day);
                  const monthNum = parseInt(month);
                  const yearNum = parseInt(year);

                  if (
                    dayNum >= 1 &&
                    dayNum <= 31 &&
                    monthNum >= 1 &&
                    monthNum <= 12
                  ) {
                    try {
                      if (currentDateType === "hijri") {
                        parsedDate = HijriDate.fromHijri(
                          yearNum,
                          monthNum,
                          dayNum
                        );
                      } else {
                        parsedDate = new Date(yearNum, monthNum - 1, dayNum);
                      }
                    } catch {
                      // Ignore parsing errors
                    }
                  }
                }
              }

              // If we successfully parsed a date, select it
              if (parsedDate && !isNaN(parsedDate.getTime())) {
                setCurrentMonth(parsedDate);
                setJustSelectedFromInput(true);
                handleDateSelect(parsedDate);
              }

              setIsManualInput(false);
              if (inputTimeout) {
                clearTimeout(inputTimeout);
                setInputTimeout(null);
              }
            }
          }}
          onClick={() => !disabled && setIsOpen(true)}
          placeholder={
            placeholder ||
            (currentDateType === "hijri"
              ? language === "ar"
                ? "5 صفر 1447 أو 5/8/1445"
                : "5 Safar 1447 AH or 5/8/1445"
              : language === "ar"
              ? "30 يوليو 2025 أو 30/7/2025"
              : "July 30, 2025 or 30/7/2025")
          }
          disabled={disabled}
          className={cn(
            "w-full px-4 py-3 border-0 bg-white rounded-xl shadow-sm ring-1 ring-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:shadow-md transition-all duration-200",
            disabled && "bg-gray-50 cursor-not-allowed ring-gray-100",
            language === "ar" && "text-right",
            "text-sm font-medium placeholder:text-gray-400"
          )}
        />

        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={cn(
            "absolute top-1/2 transform -translate-y-1/2 p-2 rounded-lg text-gray-400 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 hover:scale-110",
            language === "ar" ? "left-2" : "right-2"
          )}
        >
          <CalendarIcon className="w-5 h-5" />
        </button>

        {isOpen && (
          <div
            className={cn(
              "calendar-container absolute left-0 bg-white border-0 rounded-xl shadow-lg z-[9999]",
              "min-w-[280px] max-w-[320px] w-[90vw] sm:w-auto max-h-[480px] overflow-hidden",
              "ring-1 ring-black/5",
              calendarPosition === "top" ? "bottom-full mb-2" : "top-full mt-2"
            )}
            style={{
              boxShadow:
                "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05)",
            }}
          >
            {/* Calendar type switcher */}
            {allowTypeSwitch && (
              <div className="px-4 py-2 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-center">
                  <div className="bg-white rounded-lg p-0.5 shadow-sm ring-1 ring-black/5">
                    <div className="flex space-x-0.5">
                      <button
                        className={cn(
                          "px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200",
                          currentDateType === "gregorian"
                            ? "bg-blue-500 text-white"
                            : "text-gray-600 hover:text-blue-600 hover:bg-blue-50/50"
                        )}
                        onClick={() => setCurrentDateType("gregorian")}
                      >
                        {language === "ar" ? "ميلادي" : "Gregorian"}
                      </button>
                      <button
                        className={cn(
                          "px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200",
                          currentDateType === "hijri"
                            ? "bg-blue-500 text-white"
                            : "text-gray-600 hover:text-blue-600 hover:bg-blue-50/50"
                        )}
                        onClick={() => setCurrentDateType("hijri")}
                      >
                        {language === "ar" ? "هجري" : "Hijri"}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Calendar */}
            {renderCalendar()}

            {/* Time picker */}
            {renderTimePicker()}

            {/* Action buttons */}
            {showActionButtons && (
              <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50 flex justify-end space-x-2">
                <button
                  className="px-4 py-2 text-xs font-medium text-gray-600 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  {language === "ar" ? "إلغاء" : "Cancel"}
                </button>
                <button
                  className="px-4 py-2 text-xs font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-all duration-200"
                  onClick={() => {
                    if (selectedDate) {
                      onChange?.(selectedDate);
                    }
                    setIsOpen(false);
                  }}
                >
                  {language === "ar" ? "تأكيد" : "Confirm"}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // If title is provided, wrap in self-contained component
  if (title) {
    const hasSelectedDate = isStandalone ? internalDate : selectedDate;
    const displayFormattedDate = isStandalone
      ? internalFormattedDate
      : selectedDate
      ? HijriDate.formatSelectedDate(
          selectedDate,
          currentDateType === "hijri",
          language as "ar" | "en",
          showTime
        )
      : "";

    return (
      <div className={cn("space-y-4", containerClassName)}>
        <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
        {pickerComponent}
        {showSelectedDate && hasSelectedDate && displayFormattedDate && (
          <div className={cn("mt-2 p-3 border rounded-md", getColorClasses())}>
            <p className="text-sm">
              <strong>
                {language === "ar" ? "التاريخ المحدد:" : "Selected Date:"}
              </strong>{" "}
              {displayFormattedDate}
            </p>
          </div>
        )}
      </div>
    );
  }

  return pickerComponent;
}
