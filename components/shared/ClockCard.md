# ClockCard Component

## Overview

The `ClockCard` is a highly configurable React component that displays a real-time clock with customizable timezone, date format, time format, and display style options. It provides a clean, modern interface with optional settings panel for user customization.

## Features

- **Real-time Clock Display**: Shows current time with automatic updates
- **Timezone Support**: Configurable timezone selection with major world timezones
- **Multiple Display Styles**: Compact, detailed, and default view modes
- **Customizable Formats**: Flexible date and time format options
- **UI Control**: Granular control over which settings are available to users
- **Responsive Design**: Adapts to different screen sizes
- **Internationalization**: Supports multiple languages through context

## Props Interface

```typescript
interface ClockCardProps {
  // Display Configuration
  timezone?: string;                    // Default: 'America/New_York'
  dateFormat?: string;                  // Default: 'MMMM d, yyyy'
  timeFormat?: string;                  // Default: 'h:mm:ss a'
  displayStyle?: 'default' | 'compact' | 'detailed'; // Default: 'default'
  
  // Size Configuration
  size?: 'sm' | 'md' | 'lg';           // Default: 'md'
  
  // UI Control Props
  enableTimezoneControl?: boolean;      // Default: true
  enableDateFormatControl?: boolean;    // Default: true
  enableTimeFormatControl?: boolean;    // Default: true
  enableDisplayStyleControl?: boolean;  // Default: true
  
  // Layout
  className?: string;                   // Additional CSS classes
}
```

## Usage Examples

### Basic Usage

```tsx
import { ClockCard } from '@/components/shared/ClockCard';

function App() {
  return (
    <div>
      <ClockCard />
    </div>
  );
}
```

### Custom Timezone and Format

```tsx
<ClockCard 
  timezone="Europe/London"
  dateFormat="dd/MM/yyyy"
  timeFormat="HH:mm:ss"
  displayStyle="detailed"
/>
```

### Limited User Controls

```tsx
// Only allow time and date format changes
<ClockCard 
  enableTimezoneControl={false}
  enableDisplayStyleControl={false}
  enableTimeFormatControl={true}
  enableDateFormatControl={true}
/>
```

### Display-Only Clock

```tsx
// No user controls - display only
<ClockCard 
  enableTimezoneControl={false}
  enableDateFormatControl={false}
  enableTimeFormatControl={false}
  enableDisplayStyleControl={false}
  timezone="UTC"
  displayStyle="compact"
/>
```

### Size Variants

```tsx
// Small size - compact for dashboards
<ClockCard 
  size="sm"
  timezone="UTC"
  displayStyle="compact"
/>

// Medium size - default balanced view
<ClockCard 
  size="md"
  timezone="America/New_York"
/>

// Large size - prominent display
<ClockCard 
  size="lg"
  timezone="Europe/London"
  displayStyle="detailed"
/>
```

## Size Options

The ClockCard component supports three size variants to accommodate different layout requirements:

### Small (sm)
- **Dimensions**: Compact height (~80px) with reduced padding
- **Use Cases**: Dashboard widgets, sidebar components, grid layouts
- **Features**: Smaller fonts, minimal spacing, essential information only
- **Best For**: Space-constrained environments where multiple clocks are displayed

### Medium (md) - Default
- **Dimensions**: Balanced height (~110px) with standard padding
- **Use Cases**: Main content areas, standard card layouts
- **Features**: Optimal readability with balanced information density
- **Best For**: General-purpose clock display in most applications

### Large (lg)
- **Dimensions**: Prominent height (~120px) with generous padding
- **Use Cases**: Hero sections, primary time displays, focus areas
- **Features**: Large fonts, enhanced visual hierarchy, maximum readability
- **Best For**: Primary clock displays where time is the main focus

## Display Styles

### Default Style
- Standard clock display with time and date
- Balanced information density
- Suitable for most use cases

### Compact Style
- Minimal space usage
- Essential information only
- Ideal for dashboards or sidebars

### Detailed Style
- Maximum information display
- Includes additional time details
- Best for dedicated time displays

## Size and Style Combinations

The `size` and `displayStyle` props work together to create optimal layouts:

### Recommended Combinations

```tsx
// Dashboard widget - compact and small
<ClockCard size="sm" displayStyle="compact" />

// Standard card - balanced medium size
<ClockCard size="md" displayStyle="default" />

// Hero display - large with full details
<ClockCard size="lg" displayStyle="detailed" />

// Sidebar clock - small with essential info
<ClockCard size="sm" displayStyle="default" />

// Focus area - large but clean
<ClockCard size="lg" displayStyle="default" />
```

### Size-Specific Features

- **Small (sm)**: Optimized for `compact` and `default` styles
- **Medium (md)**: Works well with all display styles
- **Large (lg)**: Best suited for `default` and `detailed` styles

## Supported Timezones

The component supports major world timezones including:

- **Americas**: New York, Los Angeles, Chicago, Toronto, São Paulo
- **Europe**: London, Paris, Berlin, Rome, Madrid
- **Asia**: Tokyo, Shanghai, Mumbai, Dubai, Singapore
- **Pacific**: Sydney, Auckland
- **UTC**: Coordinated Universal Time

## Date Format Options

- `MMMM d, yyyy` - January 15, 2024
- `dd/MM/yyyy` - 15/01/2024
- `MM/dd/yyyy` - 01/15/2024
- `yyyy-MM-dd` - 2024-01-15
- `EEEE, MMMM d` - Monday, January 15

## Time Format Options

- `h:mm:ss a` - 2:30:45 PM (12-hour with seconds)
- `h:mm a` - 2:30 PM (12-hour without seconds)
- `HH:mm:ss` - 14:30:45 (24-hour with seconds)
- `HH:mm` - 14:30 (24-hour without seconds)

## Implementation Details

### Dependencies

- React hooks for state management and effects
- Tailwind CSS for styling
- Lucide React icons for UI elements
- Custom HijriDate utility for Islamic calendar conversion
- Native JavaScript Date API for time handling and timezone conversion

### State Management

The component uses React's `useState` and `useEffect` hooks to:
- Manage current time updates
- Handle user preference changes
- Control settings panel visibility
- Maintain timezone conversions
- Prevent hydration mismatches with `isMounted` state

### Size System

The component implements a responsive size system with three variants:

```typescript
const sizeStyles = {
  sm: {
    card: "p-3 h-[80px] w-full max-w-[280px]",
    time: "text-lg font-bold",
    date: "text-xs",
    settings: "text-xs"
  },
  md: {
    card: "p-5 h-[110px] w-full max-w-[320px]",
    time: "text-2xl font-bold",
    date: "text-sm",
    settings: "text-sm"
  },
  lg: {
    card: "p-6 h-[120px] w-full max-w-[360px]",
    time: "text-3xl font-bold",
    date: "text-base",
    settings: "text-base"
  }
};
```

- **Consistent Spacing**: Each size maintains proportional padding and margins
- **Typography Scale**: Font sizes scale appropriately across all sizes
- **Container Constraints**: Maximum widths prevent excessive stretching
- **Responsive Design**: Adapts to container constraints while maintaining aspect ratios

### React 19 Compatibility

While React 19 introduces the new `use` hook for data fetching and context consumption <mcreference link="https://medium.com/@ademyalcin27/the-new-use-hook-in-react-19-a-game-changer-for-simpler-data-fetching-and-context-management-cc45cc5ebd28" index="2">2</mcreference>, it cannot replace `useEffect` for timer-based operations like `setInterval`. The `use` hook is designed for asynchronous operations that resolve once (promises), while our clock component requires a persistent timer that runs continuously <mcreference link="https://overreacted.io/making-setinterval-declarative-with-react-hooks/" index="1">1</mcreference>. Therefore, `useEffect` remains the correct choice for managing the clock's timer functionality.

### Performance Considerations

- Time updates every second using `setInterval` <mcreference link="https://upmostly.com/tutorials/setinterval-in-react-components-using-hooks" index="3">3</mcreference>
- Cleanup of intervals on component unmount to prevent memory leaks
- Efficient re-rendering with proper dependency arrays
- Hydration-safe rendering with `isMounted` state check

## Accessibility

- Semantic HTML structure
- Proper ARIA labels for interactive elements
- Keyboard navigation support
- Screen reader compatible
- High contrast color schemes

## Browser Compatibility

- Modern browsers with ES6+ support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Styling

The component uses Tailwind CSS classes and can be customized through:

- `className` prop for additional styles
- CSS custom properties for theme customization
- Tailwind configuration modifications
- Component-level style overrides

## Error Handling

- Graceful fallback for invalid timezones
- Default format application for invalid date/time formats
- Console warnings for development debugging
- Robust error boundaries integration

## Testing

### Unit Tests

```typescript
// Example test structure
describe('ClockCard', () => {
  it('renders with default props', () => {
    // Test implementation
  });
  
  it('updates time every second', () => {
    // Test implementation
  });
  
  it('respects UI control props', () => {
    // Test implementation
  });
});
```

### Integration Tests

- Timezone conversion accuracy
- Format application correctness
- User interaction workflows
- Settings persistence

## Contributing

### Development Setup

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Run tests: `npm test`
4. Build for production: `npm run build`

### Code Standards

- TypeScript strict mode
- ESLint configuration compliance
- Prettier code formatting
- Comprehensive prop documentation

## Changelog

### v2.1.0
- Added comprehensive size system with sm, md, lg variants
- Enhanced documentation with size and style combination guides
- Improved responsive design with container constraints
- Added size-specific typography scaling
- Updated props interface with size configuration

### v2.0.0
- Added UI control props for granular feature management
- Enhanced timezone support
- Improved accessibility features
- Performance optimizations

### v1.0.0
- Initial release
- Basic clock functionality
- Timezone and format support
- Settings panel implementation

## License

This component is part of the TTS Template project and follows the project's licensing terms.