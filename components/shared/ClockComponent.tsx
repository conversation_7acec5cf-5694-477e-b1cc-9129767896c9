// components/ClockComponent.tsx
"use client";
import React, { useState, useEffect, useRef } from "react";
import { Clock, Settings } from "lucide-react";
import { useLanguage } from "../../contexts/LanguageContext";

// import { Button } from '../ui/button';
// Hijri date conversion utility (Umm al-Qura accurate algorithm)
class HijriDate {
  // Hijri month names in Arabic
  static hijriMonthsAr = [
    "محرم",
    "صفر",
    "ربيع الأول",
    "ربيع الثاني",
    "جمادى الأولى",
    "جمادى الثانية",
    "رجب",
    "شعبان",
    "رمضان",
    "شوال",
    "ذو القعدة",
    "ذو الحجة",
  ];

  // Hijri month names in English
  static hijriMonthsEn = [
    "Muharram",
    "Safar",
    "<PERSON><PERSON><PERSON> al-awwal",
    "<PERSON><PERSON><PERSON> <PERSON>-than<PERSON>",
    "<PERSON><PERSON> al-awwal",
    "<PERSON><PERSON> al-thani",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "Ramadan",
    "<PERSON><PERSON>",
    "<PERSON><PERSON> al-<PERSON>",
    "Dhu al-Hijjah",
  ];

  // Convert Gregorian date to Hijri
  static toHijri(gregorianDate: Date): {
    year: number;
    month: number;
    day: number;
    monthName: string;
  } {
    const jd = this.gregorianToJulian(gregorianDate);
    const hijri = this.julianToHijri(jd);

    return {
      year: hijri.year,
      month: hijri.month,
      day: hijri.day,
      monthName: this.hijriMonthsAr[hijri.month - 1],
    };
  }

  // Convert Hijri date to Gregorian
  static fromHijri(
    hijriYear: number,
    hijriMonth: number,
    hijriDay: number
  ): Date {
    const jd = this.hijriToJulian(hijriYear, hijriMonth, hijriDay);
    const gregorianDate = this.julianToGregorian(jd);
    return gregorianDate;
  }

  // Get number of days in Hijri month (Umm al-Qura accurate method)
  static getHijriMonthDays(year: number, month: number): number {
    // More accurate Umm al-Qura month length calculation
    // Based on the official Saudi Arabian calendar system
    const isLeapYear = this.isHijriLeapYear(year);

    // Standard month lengths in Umm al-Qura calendar
    // Months 1, 3, 5, 7, 9, 11 have 30 days
    // Months 2, 4, 6, 8, 10 have 29 days
    // Month 12 has 29 days in normal years, 30 days in leap years
    const standardLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
    
    if (month === 12 && isLeapYear) {
      return 30;
    }
    
    return standardLengths[month - 1];
  }

  // Check if Hijri year is leap year (Umm al-Qura accurate method)
  static isHijriLeapYear(year: number): boolean {
    // More accurate 30-year cycle with 11 leap years based on Umm al-Qura
    // This follows the official Saudi Arabian calendar system
    const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
    return leapYears.includes(year % 30);
  }

  // Helper: Convert Gregorian to Julian Day Number (more accurate)
  private static gregorianToJulian(date: Date): number {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // More precise Julian Day calculation
    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;

    const jd = day +
      Math.floor((153 * m + 2) / 5) +
      365 * y +
      Math.floor(y / 4) -
      Math.floor(y / 100) +
      Math.floor(y / 400) +
      1721119;

    return jd;
  }

  // Helper: Convert Julian Day Number to Hijri (Umm al-Qura accurate method)
  private static julianToHijri(jd: number): {
    year: number;
    month: number;
    day: number;
  } {
    // Umm al-Qura epoch: July 16, 622 CE (1 Muharram 1 AH)
    // Corrected epoch for better accuracy (adjusted by -1 day)
    const epochJd = 1948440;
    const daysSinceEpoch = jd - epochJd;

    // Handle negative days (before epoch)
    if (daysSinceEpoch < 0) {
      return { year: 1, month: 1, day: 1 };
    }

    // Estimate year using average lunar year length
    const avgLunarYear = 354.36707;
    let year = Math.floor(daysSinceEpoch / avgLunarYear) + 1;
    
    // Calculate total days up to estimated year
    let totalDays = 0;
    for (let y = 1; y < year; y++) {
      totalDays += this.isHijriLeapYear(y) ? 355 : 354;
    }

    // Adjust if estimate is too high
    while (totalDays > daysSinceEpoch) {
      year--;
      totalDays -= this.isHijriLeapYear(year) ? 355 : 354;
    }

    // Adjust if estimate is too low
    while (totalDays + (this.isHijriLeapYear(year) ? 355 : 354) <= daysSinceEpoch) {
      totalDays += this.isHijriLeapYear(year) ? 355 : 354;
      year++;
    }

    // Calculate remaining days in the year
    const daysInYear = daysSinceEpoch - totalDays;

    // Find the correct month within the year
    let month = 1;
    let daysInMonths = 0;
    while (month <= 12) {
      const monthLength = this.getHijriMonthDays(year, month);
      if (daysInMonths + monthLength > daysInYear) {
        break;
      }
      daysInMonths += monthLength;
      month++;
    }

    // Calculate the day of the month
    const day = daysInYear - daysInMonths + 1;

    return { year, month, day };
  }

  // Helper: Convert Hijri to Julian Day Number (Umm al-Qura accurate method)
  private static hijriToJulian(
    year: number,
    month: number,
    day: number
  ): number {
    // Umm al-Qura epoch: July 16, 622 CE (1 Muharram 1 AH)
    // Corrected epoch for consistency
    const epochJd = 1948440;
    
    // Calculate total days from epoch
    let totalDays = 0;
    
    // Add days for complete years
    for (let y = 1; y < year; y++) {
      totalDays += this.isHijriLeapYear(y) ? 355 : 354;
    }
    
    // Add days for complete months in the current year
    for (let m = 1; m < month; m++) {
      totalDays += this.getHijriMonthDays(year, m);
    }
    
    // Add remaining days (subtract 1 because we count from day 1)
    totalDays += day - 1;
    
    return epochJd + totalDays;
  }

  // Helper: Convert Julian Day Number to Gregorian
  private static julianToGregorian(jd: number): Date {
    const a = jd + 32044;
    const b = Math.floor((4 * a + 3) / 146097);
    const c = a - Math.floor((146097 * b) / 4);
    const d = Math.floor((4 * c + 3) / 1461);
    const e = c - Math.floor((1461 * d) / 4);
    const m = Math.floor((5 * e + 2) / 153);

    const day = e - Math.floor((153 * m + 2) / 5) + 1;
    const month = m + 3 - 12 * Math.floor(m / 10);
    const year = 100 * b + d - 4800 + Math.floor(m / 10);

    return new Date(year, month - 1, day);
  }
}
interface ClockCardProps {
  variant?: "default" | "compact" | "detailed";
  showSettings?: boolean;
  className?: string; //دي لو حابب تضيف CSS class علشان تغير شكل التصميم.
  // UI Control Props - control what features are visible to end users
  size?: "sm" | "md" | "lg";
  enableTimezoneControl?: boolean;
  enableDateFormatControl?: boolean;
  enableTimeFormatControl?: boolean;
  enableDisplayStyleControl?: boolean;
  dateFormat?: "hijri" | "gregorian" | "both"; // ⬅️ Add this line
  initialTimezone?: string; // ⬅️ Add this line
}

type DateFormat = "both" | "gregorian" | "hijri";
type TimeFormat = "12h" | "24h";
type DisplayStyle = "digital" | "analog" | "minimal";
type Timezone = string;

// Common timezones
const TIMEZONES = [
  { value: "Asia/Riyadh", label: "Riyadh (GMT+3)", labelAr: "الرياض (GMT+3)" },
  { value: "Asia/Dubai", label: "Dubai (GMT+4)", labelAr: "دبي (GMT+4)" },
  { value: "Asia/Kuwait", label: "Kuwait (GMT+3)", labelAr: "الكويت (GMT+3)" },
  { value: "Asia/Qatar", label: "Doha (GMT+3)", labelAr: "الدوحة (GMT+3)" },
  {
    value: "Asia/Bahrain",
    label: "Manama (GMT+3)",
    labelAr: "المنامة (GMT+3)",
  },
  { value: "Europe/London", label: "London (GMT+0)", labelAr: "لندن (GMT+0)" },
  {
    value: "America/New_York",
    label: "New York (GMT-5)",
    labelAr: "نيويورك (GMT-5)",
  },
  { value: "Asia/Tokyo", label: "Tokyo (GMT+9)", labelAr: "طوكيو (GMT+9)" },
  {
    value: "Australia/Sydney",
    label: "Sydney (GMT+11)",
    labelAr: "سيدني (GMT+11)",
  },
];

export default function ClockCard({
  variant = "default",
  showSettings = true,
  className = "",
  // UI Control Props - default to true (all features enabled)
  size = "md", // ✅ Default to 'md'
  enableTimezoneControl = true,
  enableDateFormatControl = true,
  enableTimeFormatControl = true,
  enableDisplayStyleControl = true,
  dateFormat: dateFormatProp = "both",
  initialTimezone = "Asia/Riyadh", // ⬅️ Add this line
}: ClockCardProps) {
  const { language } = useLanguage();
  const componentRef = useRef<HTMLDivElement>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Load settings from localStorage or use defaults
  const [dateFormat, setDateFormat] = useState<DateFormat>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('clockcard-dateFormat');
      return (saved as DateFormat) || dateFormatProp;
    }
    return dateFormatProp;
  });

  const [timeFormat, setTimeFormat] = useState<TimeFormat>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('clockcard-timeFormat');
      return (saved as TimeFormat) || "24h";
    }
    return "24h";
  });
  
  const [displayStyle, setDisplayStyle] = useState<DisplayStyle>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('clockcard-displayStyle');
      return (saved as DisplayStyle) || "digital";
    }
    return "digital";
  });
  
  const [timezone, setTimezone] = useState<Timezone>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('clockcard-timezone');
      return saved || initialTimezone;
    }
    return initialTimezone;
  });
  
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const sizeStyles = {
    sm: {
      card: "p-3 w-full max-w-[280px]",
      cardHeight: displayStyle === "analog" ? "h-[140px]" : "h-[80px]",
      icon: "w-4 h-4",
      iconContainer: "p-1.5 w-8 h-8",
      time: "text-lg",
      date: "text-xs font-medium",
      analog: "w-20 h-20",
    },
    md: {
      card: "p-5 w-full max-w-[320px]",
      cardHeight: displayStyle === "analog" ? "h-[180px]" : "h-[110px]",
      icon: "w-6 h-6",
      iconContainer: "p-2.5 w-11 h-11",
      time: "text-2xl",
      date: "text-sm font-medium",
      analog: "w-32 h-32",
    },
    lg: {
      card: "p-6 w-full max-w-[360px]",
      cardHeight: displayStyle === "analog" ? "h-[220px]" : "h-[120px]",
      icon: "w-7 h-7",
      iconContainer: "p-3 w-12 h-12",
      time: "text-3xl",
      date: "text-base font-medium",
      analog: "w-40 h-40",
    },
  };

  useEffect(() => {
    setIsMounted(true);
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Click outside to close settings panel
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (componentRef.current && !componentRef.current.contains(event.target as Node)) {
        setShowSettingsPanel(false);
      }
    };

    if (showSettingsPanel) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSettingsPanel]);

  // Save settings to localStorage
  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('clockcard-dateFormat', dateFormat);
    }
  }, [dateFormat, isMounted]);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('clockcard-timeFormat', timeFormat);
    }
  }, [timeFormat, isMounted]);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('clockcard-displayStyle', displayStyle);
    }
  }, [displayStyle, isMounted]);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('clockcard-timezone', timezone);
    }
  }, [timezone, isMounted]);

  // Prevent hydration mismatch by showing static time until mounted
  if (!isMounted) {
    return (
      <div
        className={`
        ${variant === "compact" ? "p-4" : "p-6"} 
        bg-white rounded-lg shadow-md border border-gray-200 
        ${className}
      `}
      >
        <div className="text-center">
          <div className="text-4xl font-bold text-blue-900 mb-4 font-mono">
            --:--:--
          </div>
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }
  // Get time in selected timezone
  const getTimeInTimezone = (date: Date): Date => {
    return new Date(date.toLocaleString("en-US", { timeZone: timezone }));
  };

  const formatTime = (date: Date): string => {
    const timeInTimezone = getTimeInTimezone(date);
    if (timeFormat === "12h") {
      return timeInTimezone.toLocaleTimeString(
        language === "ar" ? "ar-SA" : "en-US",
        {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: true,
          timeZone: timezone,
        }
      );
    } else {
      return timeInTimezone.toLocaleTimeString(
        language === "ar" ? "ar-SA" : "en-US",
        {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
          timeZone: timezone,
        }
      );
    }
  };
  const formatGregorianDate = (date: Date): string => {
    return date.toLocaleDateString(language === "ar" ? "ar-SA" : "en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: timezone,
    });
  };

  const formatHijriDate = (date: Date): string => {
    const timeInTimezone = getTimeInTimezone(date);
    const hijri = HijriDate.toHijri(timeInTimezone);
    if (language === "ar") {
      return `${hijri.day} ${hijri.monthName} ${hijri.year} هـ`;
    } else {
      return `${hijri.day} ${HijriDate.hijriMonthsEn[hijri.month - 1]} ${hijri.year} AH`;
    }
  };
  const renderAnalogClock = () => {
    const timeInTimezone = getTimeInTimezone(currentTime);
    const hours = timeInTimezone.getHours() % 12;
    const minutes = timeInTimezone.getMinutes();
    const seconds = timeInTimezone.getSeconds();

    const hourAngle = hours * 30 + minutes * 0.5;
    const minuteAngle = minutes * 6;
    const secondAngle = seconds * 6;

    const clockSize = sizeStyle.analog;

    return (
      <div className={`relative ${clockSize} mx-auto`}>
        <div className="absolute inset-0 rounded-full border-4 border-blue-300 bg-white shadow-lg">
          {/* Hour markers */}
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-6 bg-blue-400"
              style={{
                top: "8px",
                left: "50%",
                transformOrigin: `50% ${parseInt(clockSize.split('-')[1]) / 2 - 8}px`,
                transform: `translateX(-50%) rotate(${i * 30}deg)`,
              }}
            />
          ))}

          {/* Hour hand */}
          <div
            className="absolute w-1 bg-blue-800 rounded-full"
            style={{
              height: `${parseInt(clockSize.split('-')[1]) * 0.4}px`,
              top: `${parseInt(clockSize.split('-')[1]) * 0.1}px`,
              left: "50%",
              transformOrigin: `50% ${parseInt(clockSize.split('-')[1]) * 0.4}px`,
              transform: `translateX(-50%) rotate(${hourAngle}deg)`,
            }}
          />

          {/* Minute hand */}
          <div
            className="absolute w-0.5 bg-blue-600 rounded-full"
            style={{
              height: `${parseInt(clockSize.split('-')[1]) * 0.6}px`,
              top: `${parseInt(clockSize.split('-')[1]) * 0.05}px`,
              left: "50%",
              transformOrigin: `50% ${parseInt(clockSize.split('-')[1]) * 0.6}px`,
              transform: `translateX(-50%) rotate(${minuteAngle}deg)`,
            }}
          />

          {/* Second hand */}
          <div
            className="absolute w-0.5 bg-red-500 rounded-full"
            style={{
              height: `${parseInt(clockSize.split('-')[1]) * 0.7}px`,
              top: `${parseInt(clockSize.split('-')[1]) * 0.02}px`,
              left: "50%",
              transformOrigin: `50% ${parseInt(clockSize.split('-')[1]) * 0.7}px`,
              transform: `translateX(-50%) rotate(${secondAngle}deg)`,
            }}
          />

          {/* Center dot */}
          <div className="absolute w-3 h-3 bg-blue-800 rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
      </div>
    );
  };
  const renderSettingsPanel = () => {
    if (!showSettingsPanel) return null;

    return (
      <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-[9999]">
        <div className="space-y-4">
          {enableDateFormatControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === "ar" ? "تنسيق التاريخ" : "Date Format"}
              </label>
              <select
                value={dateFormat}
                onChange={(e) => setDateFormat(e.target.value as DateFormat)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="both">
                  {language === "ar"
                    ? "هجري وميلادي"
                    : "Both Hijri & Gregorian"}
                </option>
                <option value="gregorian">
                  {language === "ar" ? "ميلادي فقط" : "Gregorian Only"}
                </option>
                <option value="hijri">
                  {language === "ar" ? "هجري فقط" : "Hijri Only"}
                </option>
              </select>
            </div>
          )}

          {enableTimeFormatControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === "ar" ? "تنسيق الوقت" : "Time Format"}
              </label>
              <select
                value={timeFormat}
                onChange={(e) => setTimeFormat(e.target.value as TimeFormat)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="24h">
                  {language === "ar" ? "24 ساعة" : "24 Hour"}
                </option>
                <option value="12h">
                  {language === "ar" ? "12 ساعة" : "12 Hour"}
                </option>
              </select>
            </div>
          )}

          {enableDisplayStyleControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === "ar" ? "نمط العرض" : "Display Style"}
              </label>
              <select
                value={displayStyle}
                onChange={(e) =>
                  setDisplayStyle(e.target.value as DisplayStyle)
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="digital">
                  {language === "ar" ? "رقمي" : "Digital"}
                </option>
                <option value="analog">
                  {language === "ar" ? "تناظري" : "Analog"}
                </option>
                <option value="minimal">
                  {language === "ar" ? "مبسط" : "Minimal"}
                </option>
              </select>
            </div>
          )}

          {enableTimezoneControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === "ar" ? "المنطقة الزمنية" : "Timezone"}
              </label>
              <select
                value={timezone}
                onChange={(e) => setTimezone(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {TIMEZONES.map((tz) => (
                  <option key={tz.value} value={tz.value}>
                    {language === "ar" ? tz.labelAr : tz.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
    );
  };

  // SummaryCard-style color configuration
  const colorStyle = {
    card: "bg-gradient-to-br from-blue-50 via-indigo-50/80 to-blue-100 border-blue-200/60 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10 backdrop-blur-sm",
    text: "text-blue-900 font-medium",
    icon: "text-blue-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-blue-500/15 to-blue-600/10 hover:from-blue-500/25 hover:to-blue-600/15 ring-1 ring-blue-500/20",
    value: "text-blue-900 font-bold tracking-tight",
  };

  const sizeStyle = sizeStyles[size];

  return (
    <div
      ref={componentRef}
      className={`
        ${sizeStyle.card}
        ${sizeStyle.cardHeight}
        ${colorStyle.card}
        rounded-xl border shadow-sm
        transition-all duration-200 ease-out
        hover:shadow-md hover:scale-[1.02]
        relative group
        ${displayStyle === "analog" ? "flex flex-col items-center justify-center" : "flex flex-row items-center justify-between"}
        ${className}
      `}
    >
      {/* Icon section - only show for non-analog styles */}
      {displayStyle !== "analog" && (
        <div
          className={`
            ${sizeStyle.iconContainer || "p-2.5 w-11 h-11"}
            ${colorStyle.iconBg}
            rounded-lg
            transition-all duration-200 ease-out
            flex items-center justify-center
            group-hover:scale-105
            shadow-sm
            relative z-10 shrink-0
          `}
        >
          <Clock
            className={`${sizeStyle.icon || "w-6 h-6"} ${colorStyle.icon}`}
          />
        </div>
      )}

      {/* Content section */}
      <div
        className={`${displayStyle === "analog" ? "w-full flex flex-col justify-center items-center" : `flex-1 flex flex-col justify-center ${language === "ar" ? "text-right pr-4" : "text-left pl-4"}`}`}
      >
        {displayStyle === "analog" ? (
           <>
             {renderAnalogClock()}
             {/* Date Label for analog */}
             <p
               className={`${sizeStyle.date || "text-sm font-medium"} ${
                 colorStyle.text
               } leading-snug relative z-10 line-clamp-1 opacity-80 mt-2 text-center`}
               title={
                 dateFormat === "hijri"
                   ? formatHijriDate(currentTime)
                   : formatGregorianDate(currentTime)
               }
             >
               {dateFormat === "hijri"
                 ? formatHijriDate(currentTime)
                 : formatGregorianDate(currentTime)}
             </p>
           </>
        ) : displayStyle === "minimal" ? (
          /* Minimal style - time only */
          <p
            className={`${sizeStyle.time || "text-2xl"} ${
              colorStyle.value
            } leading-tight font-mono text-center`}
            title={`Current Time: ${formatTime(currentTime)}`}
          >
            {formatTime(currentTime)}
          </p>
        ) : (
          /* Digital style - default */
          <>
            {/* Time Value */}
            <p
              className={`${sizeStyle.time || "text-2xl"} ${
                colorStyle.value
              } leading-tight font-mono mb-1`}
              title={`Current Time: ${formatTime(currentTime)}`}
            >
              {formatTime(currentTime)}
            </p>

            {/* Date Label */}
            <p
              className={`${sizeStyle.date || "text-sm font-medium"} ${
                colorStyle.text
              } leading-snug relative z-10 line-clamp-1 opacity-80`}
              title={
                dateFormat === "hijri"
                  ? formatHijriDate(currentTime)
                  : formatGregorianDate(currentTime)
              }
            >
              {dateFormat === "hijri"
                ? formatHijriDate(currentTime)
                : formatGregorianDate(currentTime)}
            </p>
          </>
        )}
      </div>

      {/* Settings button */}
      {showSettings && (
        <div className={`${displayStyle === "analog" ? "absolute top-2 right-2" : "ml-2"} flex items-center`}>
          <button
            onClick={() => setShowSettingsPanel(!showSettingsPanel)}
            className="p-2 rounded-lg hover:bg-blue-100 transition-colors text-blue-600 hover:text-blue-800"
            title={language === "ar" ? "الإعدادات" : "Settings"}
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Settings Panel */}
      {renderSettingsPanel()}
    </div>
  );
}
