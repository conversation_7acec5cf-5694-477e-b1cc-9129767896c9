'use client';
import React, { useState } from 'react';

interface DatesFilterProps {
  value: {
    transitDate: string;
    entryFrom: string;
    entryTo: string;
  };
  onChange: (value: { transitDate: string; entryFrom: string; entryTo: string }) => void;
}

export default function DatesFilter({
  value = { transitDate: '', entryFrom: '', entryTo: '' },
  onChange = () => {},
}: DatesFilterProps) {
  const [expanded, setExpanded] = useState(false);

  const update = (field: 'transitDate' | 'entryFrom' | 'entryTo', v: string) => {
    onChange({ ...value, [field]: v });
  };

  return (
    <div className="border border-gray-200 rounded mb-4">
      <button
        type="button"
        onClick={() => setExpanded((v) => !v)}
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
      >
        <span className="font-medium">Dates</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            expanded ? '' : 'rotate-180'
          }`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {expanded && (
        <div className="px-4 py-3 space-y-3">
          <div>
            <label className="block text-sm mb-1">Transit Date</label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              value={value.transitDate}
              onChange={(e) => update('transitDate', e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Entry Date From</label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              value={value.entryFrom}
              onChange={(e) => update('entryFrom', e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Entry Date To</label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              value={value.entryTo}
              onChange={(e) => update('entryTo', e.target.value)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
