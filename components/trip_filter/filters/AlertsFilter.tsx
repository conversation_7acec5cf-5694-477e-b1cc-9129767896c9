'use client';
import React, { useState } from 'react';

interface AlertsFilterProps {
  options?: string[];
  selected: string[];
  onToggle: (option: string) => void;
}

export default function AlertsFilter({
  options = [
    'Acknowledge',
    'Not Acknowledge',
    'Tracker Tamper',
    'Tracker Dropped',
    'Lock Tamper',
    'Lock Open',
    'Lock Connection Lost',
    'Tracker Battery Low',
    'Lock Low Battery',
    'Lock Very Low Battery',
    'GSM Signal Lost',
    'GPS Signal Lost',
    'Geofence Entry Breach',
    'Geofence Exit Breach',
    'Tracker Connection Lost',
    'Trip Distance Exceeded',
    'Trip Time Exceeded',
    'Over Speeding',
    'Truck Stopped',
    'Wrong Direction',
    'Entry into Customs Area',
    'Truck Moved',
    '4 hours exceeded',
    'Suspected Area',
    'Medium Priority',
    'Low Priority',
  ],
  selected,
  onToggle,
}: AlertsFilterProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="border border-gray-200 rounded mb-4">
      {/* Header */}
      <button
        type="button"
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
        onClick={() => setExpanded((v) => !v)}
      >
        <span className="font-medium">Alerts</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${expanded ? '' : 'rotate-180'}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Body */}
      {expanded && (
        <div className="px-4 py-3 grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
          {options.map((opt) => (
            <label key={opt} className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-blue-600"
                checked={selected.includes(opt)}
                onChange={() => onToggle(opt)}
              />
              <span className="ml-2 text-sm">{opt}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}
