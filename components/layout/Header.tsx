"use client";

import Link from "next/link";
import Image from "next/image";
import { User } from "lucide-react";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import LanguageSwitcher from "./LanguageSwitcher";
import MobileNavigation from "./MobileNavigation";
import Navbar from "./Navbar";
import { useLanguage } from "../../contexts/LanguageContext";
import { useAuth } from "../../contexts/AuthContext";

export default function Header() {
  const { t } = useLanguage();
  const { user, logout } = useAuth();

  return (
    <header className="w-full fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
      {/* Top Header Section */}
      <div className="bg-[#082d4f] h-12 text-white">
        <div className="flex justify-between items-center px-4 md:px-6 h-full">
          {/* Left Side - Mobile Nav and Demo Link */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {/* Mobile Navigation Button */}
            <div className="md:hidden">
              <MobileNavigation />
            </div>

            {/* Demo Link */}
            <Link href="/demo">
              <Button
                variant="ghost"
                className="text-white hover:text-gray-200 hover:bg-white/10 text-sm"
              >
                {t("navigation.demo")}
              </Button>
            </Link>
            
            {/* KB Link */}
            <Link href="/kb">
              <Button
                variant="ghost"
                className="text-white hover:text-gray-200 hover:bg-white/10 text-sm"
              >
                {t("navigation.kb")}
              </Button>
            </Link>
          </div>

          {/* Right Side - Language Switcher and User Account */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <LanguageSwitcher />

            {/* User Account Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:text-gray-200 hover:bg-white/10"
                >
                  <User className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                  <span className="text-xs">
                    {user?.employeeName ||
                      user?.employeeNameArabic ||
                      t("header.userAccount")}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>
                  <span>{t("header.profile")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <span>{t("header.settings")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={logout}
                  className="text-red-600 hover:text-red-700"
                >
                  <span>{t("header.logout")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main Header Section - Hidden on Mobile */}
      <div className="hidden md:block bg-white border-b border-gray-200 h-20">
        <div className="flex justify-between items-center px-4 md:px-6 h-full">
          {/* Center - Navigation */}
          <div className="flex-1 flex justify-center">
            <Navbar />
          </div>

          {/* Right Side - Logo */}
          <div className="hidden md:flex items-center ml-6 rtl:ml-0 rtl:mr-6">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="relative w-60 h-20 flex-shrink-0 logo-container">
                <Image
                  src="/logo.png"
                  alt="Government Portal Logo"
                  fill
                  className="object-contain logo-image"
                  priority
                />
              </div>
              <div className="text-base font-semibold text-gray-700 hidden lg:block"></div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
