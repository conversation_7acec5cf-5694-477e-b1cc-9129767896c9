import TripAlertMapViewer from "./TripAlertMapViewer";
import tripAlertsData from "../data/trip_alerts.json";
import { TripAlert } from "@/types/trip_alert";
import TripAlertFormView from "./TripAlertFormView";
import { useTripAlertData } from "@/hooks/useTripAlertData";
import Link from "next/link";

interface TripAlertsData {
  tripAlerts: TripAlert[];
}

interface Props {
  row: TripAlert;
}

export default function TripAlertMasterDetailView({ row }: Props) {
  const tripAlerts = (tripAlertsData as unknown as TripAlertsData).tripAlerts;
  const tripAlert = useTripAlertData(row.alertId);

  // Get the tripId from the alert detail for map filtering
  const selectedTripId = tripAlert?.tripAlert?.tripDetails?.tripId;

  if (!tripAlert) {
    return (
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 text-center py-8">
          <div className="text-red-500">Alert details not found</div>
        </div>
      </div>
    );
  }

  // Navigation icons configuration
  const navigationIcons = [
    {
      id: "trip_map",
      icon: "🗺️",
      title: "Trip Map",
    },
    {
      id: "trip_alerts",
      icon: "🔔",
      title: "Trip Alerts",
    },
    {
      id: "movement_report",
      icon: "⚠️",
      title: "Movement Report",
    },
    {
      id: "trip_pings",
      icon: "📊",
      title: "Trip Pings",
    },
    {
      id: "trip_activities_report",
      icon: "✉️",
      title: "Activities Report",
    },
  ];

  return (
    <div>
      <div className="m-2 p-2 flex justify-end gap-4 bg-teal">
        {navigationIcons.map((nav) => (
          <Link
            key={nav.id}
            href={`/trip-details?trip_id=${selectedTripId}&current_tab=${nav.id}`}
            className={`w-8 h-8 rounded-md flex items-center justify-center text-white text-sm hover:opacity-80 transition-opacity bg-blue-500`}
            title={nav.title}
          >
            {nav.icon}
          </Link>
        ))}
      </div>
      <div className="master-detail-view grid grid-cols-12 gap-4 p-2">
        {/* Left Sidebar / Detail Panel (3 cols) */}
        <div className="col-span-12 md:col-span-3 text-wrap">
          <div className="text-sm text-gray-600 space-y-1">
            <TripAlertFormView tripAlert={tripAlert?.tripAlert} />
          </div>
        </div>

        {/* Map Viewer (9 cols) */}
        <div className="col-span-12 md:col-span-9">
          <TripAlertMapViewer
            tripAlerts={tripAlerts}
            selectedTripId={selectedTripId}
            className="h-[600px]"
          />
        </div>
      </div>
    </div>
  );
}
