export const appSettings = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || '/api',
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '5000'),
    retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3')
  },
  signalr: {
    hubUrl: process.env.NEXT_PUBLIC_SIGNALR_HUB_URL || '/hubs/trips',
    reconnectInterval: parseInt(process.env.NEXT_PUBLIC_SIGNALR_RECONNECT_INTERVAL || '5000')
  },
  features: {
    useRealTimeUpdates: process.env.NEXT_PUBLIC_USE_REALTIME === 'true',
    useApiService: process.env.NEXT_PUBLIC_USE_API === 'true'
  }
};