export interface TripAlert {
  alertId: string;
  alertType: string;
  alertStatus: string;
  transitNumber?: string;
  tripId: string;
  transitSequence: number;
  routeName: string;
  shipmentDescription: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  severity: string;
  description: string;
  acknowledgedBy?: string | null;
  acknowledgedAt?: string | null;
  resolvedBy?: string | null;
  resolvedAt?: string | null;
  comments?: string | null;
  relatedData: {
    speed: number;
    batteryLevel: number;
    signalStrength: number;
    geofenceId: string | null;
  };
  receivedAt?: string;
  checkpost?: {
    name: string;
    location: {
      latitude: number;
      longitude: number;
      coordinates: string;
    };
    contactNumber: string;
  };
  tripDetails?: {
    tripId: string;
    transitType: string;
    startDate: string;
    transitSequenceNo: string;
    owner: string;
    elocks: string;
    tracker: string;
    vehicleDetails: string;
    completeDistance: string;
    remainingDistance: string;
    driverInfo: string;
    expectedArrivalDate: string;
    endDate: string;
    securityNotes: string;
    shipmentDescription: string;
  };
}
