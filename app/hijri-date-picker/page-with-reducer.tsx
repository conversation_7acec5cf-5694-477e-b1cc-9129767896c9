"use client";

import React, { useReducer, useCallback } from "react";
import DateTimePicker, { HijriDate } from "@/components/shared/DateTimePicker";
import { useLanguage } from "@/contexts/LanguageContext";

// Enhanced state management with useReducer
interface DatePickerState {
  selectedDate?: Date;
  formattedDate: string;
}

interface DatePickersState {
  basicHijri: DatePickerState;
  hijriDateTime: DatePickerState;
  switchable: DatePickerState;
  gregorian: DatePickerState;
}

type DatePickerAction =
  | { type: "UPDATE_DATE"; picker: keyof DatePickersState; date: Date }
  | { type: "UPDATE_FORMAT"; picker: keyof DatePickersState; formattedDate: string }
  | { type: "RESET_PICKER"; picker: keyof DatePickersState }
  | { type: "RESET_ALL" };

const initialState: DatePickersState = {
  basicHijri: { selectedDate: new Date(), formattedDate: "" },
  hijriDateTime: { selectedDate: undefined, formattedDate: "" },
  switchable: { selectedDate: undefined, formattedDate: "" },
  gregorian: { selectedDate: undefined, formattedDate: "" },
};

function datePickerReducer(state: DatePickersState, action: DatePickerAction): DatePickersState {
  switch (action.type) {
    case "UPDATE_DATE":
      return {
        ...state,
        [action.picker]: {
          ...state[action.picker],
          selectedDate: action.date,
        },
      };
    case "UPDATE_FORMAT":
      return {
        ...state,
        [action.picker]: {
          ...state[action.picker],
          formattedDate: action.formattedDate,
        },
      };
    case "RESET_PICKER":
      return {
        ...state,
        [action.picker]: { selectedDate: undefined, formattedDate: "" },
      };
    case "RESET_ALL":
      return initialState;
    default:
      return state;
  }
}

// Custom hook for date picker actions
function useDatePickerActions(dispatch: React.Dispatch<DatePickerAction>) {
  const updateDate = useCallback(
    (picker: keyof DatePickersState) => (date: Date) => {
      dispatch({ type: "UPDATE_DATE", picker, date });
    },
    [dispatch]
  );

  const updateFormat = useCallback(
    (picker: keyof DatePickersState) => (formattedDate: string) => {
      dispatch({ type: "UPDATE_FORMAT", picker, formattedDate });
    },
    [dispatch]
  );

  const resetPicker = useCallback(
    (picker: keyof DatePickersState) => () => {
      dispatch({ type: "RESET_PICKER", picker });
    },
    [dispatch]
  );

  const resetAll = useCallback(() => {
    dispatch({ type: "RESET_ALL" });
  }, [dispatch]);

  return { updateDate, updateFormat, resetPicker, resetAll };
}

// Configuration for different picker types
interface PickerConfig {
  key: keyof DatePickersState;
  title: string;
  dateType: "hijri" | "gregorian";
  showTime?: boolean;
  allowTypeSwitch?: boolean;
  colorClasses: string;
}

export default function HijriDatePickerDemoWithReducer() {
  const { language } = useLanguage();
  const [state, dispatch] = useReducer(datePickerReducer, initialState);
  const actions = useDatePickerActions(dispatch);

  // Configuration for all pickers
  const pickerConfigs: PickerConfig[] = [
    {
      key: "basicHijri",
      title: language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker",
      dateType: "hijri",
      colorClasses: "bg-green-50 border-green-200 text-green-800",
    },
    {
      key: "hijriDateTime",
      title: language === "ar" ? "منتقي التاريخ والوقت الهجري" : "Hijri Date & Time Picker",
      dateType: "hijri",
      showTime: true,
      colorClasses: "bg-blue-50 border-blue-200 text-blue-800",
    },
    {
      key: "switchable",
      title: language === "ar" ? "منتقي التاريخ القابل للتبديل" : "Switchable Calendar Type",
      dateType: "hijri",
      allowTypeSwitch: true,
      colorClasses: "bg-purple-50 border-purple-200 text-purple-800",
    },
    {
      key: "gregorian",
      title: language === "ar" ? "منتقي التاريخ الميلادي" : "Gregorian Date Picker",
      dateType: "gregorian",
      colorClasses: "bg-orange-50 border-orange-200 text-orange-800",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
          </h1>
          
          {/* Control Panel */}
          <div className="mb-6">
            <button
              onClick={actions.resetAll}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
            >
              {language === "ar" ? "إعادة تعيين الكل" : "Reset All"}
            </button>
          </div>
          
          {/* Today's Date Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الميلادي" : "Gregorian Date"}
              </h3>
              <p className="text-lg text-gray-900">
                {new Date().toLocaleDateString(
                  language === "ar" ? "ar-SA" : "en-US",
                  {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                  }
                )}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
              </h3>
              <p
                className="text-lg text-gray-900"
                dir={language === "ar" ? "rtl" : "ltr"}
              >
                {HijriDate.getCurrentHijriDateString(language as "ar" | "en")}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {pickerConfigs.map((config) => {
            const pickerState = state[config.key];
            return (
              <div key={config.key} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-800">
                    {config.title}
                  </h2>
                  <button
                    onClick={actions.resetPicker(config.key)}
                    className="text-sm px-2 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                  >
                    {language === "ar" ? "إعادة تعيين" : "Reset"}
                  </button>
                </div>
                
                <DateTimePicker
                  label={config.title}
                  value={pickerState.selectedDate}
                  onChange={actions.updateDate(config.key)}
                  dateType={config.dateType}
                  format="full"
                  showTime={config.showTime}
                  timeFormat={config.showTime ? "12h" : "24h"}
                  allowTypeSwitch={config.allowTypeSwitch}
                  placeholder={
                    language === "ar"
                      ? config.showTime
                        ? "اختر التاريخ والوقت"
                        : "اختر التاريخ"
                      : config.showTime
                      ? "Select date and time"
                      : "Select date"
                  }
                  onFormattedDateChange={actions.updateFormat(config.key)}
                />
                
                {pickerState.selectedDate && (
                  <div className={`mt-2 p-3 rounded-md ${config.colorClasses}`}>
                    <p className="text-sm">
                      <strong>
                        {language === "ar"
                          ? config.showTime
                            ? "التاريخ والوقت المحدد:"
                            : "التاريخ المحدد:"
                          : config.showTime
                          ? "Selected Date & Time:"
                          : "Selected Date:"}
                      </strong>{" "}
                      {pickerState.formattedDate}
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
