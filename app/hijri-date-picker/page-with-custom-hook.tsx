"use client";

import React, { useState, useCallback } from "react";
import DateTimePicker, { HijriDate } from "@/components/shared/DateTimePicker";
import { useLanguage } from "@/contexts/LanguageContext";

// Custom hook for managing date picker state
interface DatePickerState {
  selectedDate?: Date;
  formattedDate: string;
}

function useDatePicker(initialDate?: Date) {
  const [state, setState] = useState<DatePickerState>({
    selectedDate: initialDate,
    formattedDate: "",
  });

  const updateDate = useCallback((date: Date) => {
    setState(prev => ({ ...prev, selectedDate: date }));
  }, []);

  const updateFormattedDate = useCallback((formattedDate: string) => {
    setState(prev => ({ ...prev, formattedDate }));
  }, []);

  const reset = useCallback(() => {
    setState({ selectedDate: undefined, formattedDate: "" });
  }, []);

  return {
    ...state,
    updateDate,
    updateFormattedDate,
    reset,
  };
}

// Alternative: Props-based approach for reusable components
interface DatePickerDemoProps {
  title: string;
  dateType?: "hijri" | "gregorian";
  showTime?: boolean;
  allowTypeSwitch?: boolean;
  initialDate?: Date;
  className?: string;
}

function DatePickerDemo({
  title,
  dateType = "hijri",
  showTime = false,
  allowTypeSwitch = false,
  initialDate,
  className = "",
}: DatePickerDemoProps) {
  const { language } = useLanguage();
  const datePicker = useDatePicker(initialDate);

  const getColorClasses = () => {
    if (showTime) return "bg-blue-50 border-blue-200 text-blue-800";
    if (allowTypeSwitch) return "bg-purple-50 border-purple-200 text-purple-800";
    return "bg-green-50 border-green-200 text-green-800";
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
      <DateTimePicker
        label={title}
        value={datePicker.selectedDate}
        onChange={datePicker.updateDate}
        dateType={dateType}
        format="full"
        showTime={showTime}
        timeFormat={showTime ? "12h" : "24h"}
        allowTypeSwitch={allowTypeSwitch}
        placeholder={
          language === "ar"
            ? showTime
              ? "اختر التاريخ والوقت"
              : "اختر التاريخ"
            : showTime
            ? "Select date and time"
            : "Select date"
        }
        onFormattedDateChange={datePicker.updateFormattedDate}
      />
      {datePicker.selectedDate && (
        <div className={`mt-2 p-3 rounded-md ${getColorClasses()}`}>
          <p className="text-sm">
            <strong>
              {language === "ar"
                ? showTime
                  ? "التاريخ والوقت المحدد:"
                  : "التاريخ المحدد:"
                : showTime
                ? "Selected Date & Time:"
                : "Selected Date:"}
            </strong>{" "}
            {datePicker.formattedDate}
          </p>
        </div>
      )}
    </div>
  );
}

export default function HijriDatePickerDemoWithHook() {
  const { language } = useLanguage();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
          </h1>
          
          {/* Today's Date Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الميلادي" : "Gregorian Date"}
              </h3>
              <p className="text-lg text-gray-900">
                {new Date().toLocaleDateString(
                  language === "ar" ? "ar-SA" : "en-US",
                  {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                  }
                )}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-700 mb-2">
                {language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
              </h3>
              <p
                className="text-lg text-gray-900"
                dir={language === "ar" ? "rtl" : "ltr"}
              >
                {HijriDate.getCurrentHijriDateString(language as "ar" | "en")}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Basic Hijri Date Picker */}
          <DatePickerDemo
            title={language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
            dateType="hijri"
            initialDate={new Date()}
          />

          {/* Hijri Date & Time Picker */}
          <DatePickerDemo
            title={
              language === "ar"
                ? "منتقي التاريخ والوقت الهجري"
                : "Hijri Date & Time Picker"
            }
            dateType="hijri"
            showTime={true}
          />

          {/* Switchable Calendar Type */}
          <DatePickerDemo
            title={
              language === "ar"
                ? "منتقي التاريخ القابل للتبديل"
                : "Switchable Calendar Type"
            }
            dateType="hijri"
            allowTypeSwitch={true}
          />

          {/* Gregorian Date Picker */}
          <DatePickerDemo
            title={
              language === "ar"
                ? "منتقي التاريخ الميلادي"
                : "Gregorian Date Picker"
            }
            dateType="gregorian"
          />
        </div>
      </div>
    </div>
  );
}
