'use client';

import SummaryCard from '@/components/shared/SummaryCard';
import TripListView from '@/components/TripListView';
import ClockCard from '@/components/shared/ClockComponent';

export default function MyAssignedPortsPage() {
  const cardSize = "sm";
  const showTrends = false;
  const dashboardCards = [
    {
      id: "active-trips-with-alerts",
      titleKey: "dashboard.card.activeTripsWithAlerts",
      value: 2,
      icon: "bell-orange" as const,
      color: "orange" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "active-trips-without-alerts",
      titleKey: "dashboard.card.activeTripsWithoutAlerts",
      value: 1553,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "total-active-trips",
      titleKey: "dashboard.card.totalActiveTrips",
      value: 2,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "total-closed-trips",
      titleKey: "dashboard.card.totalClosedTrips",
      value: 2,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    
  ];

  return (
   <div className="bg-gray-100 min-h-screen p-6 space-y-2 ">
   
         
           <div className="mb-4">
             <h2 className="text-xl font-semibold text-gray-900 mb-4">
               {/* {t("dummy.section.individualCards")} */}
             </h2>
             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                <ClockCard variant="compact" showSettings={false} dateFormat='hijri' size={cardSize}/>

               {dashboardCards.slice(0, 5).map((card) => (
                
                 <SummaryCard
                   key={`individual-${card.id}`}
                   id={card.id}
                   titleKey={card.titleKey}
                   value={card.value}
                   icon={card.icon}
                   color={card.color}
                   size={cardSize}
                   showTrends={showTrends}
                   trend={card.trend}
                   className="w-full"
                 />
               ))}
             </div>
           </div>
            <TripListView/>
           
         </div>
  );
}
