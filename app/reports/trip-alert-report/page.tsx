'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import SummaryCard from '@/components/shared/SummaryCard';
import ClockCard from '@/components/shared/ClockComponent';
import TripAlertList from '@/components/AlertListView';
import TripFilter from '@/components/trip_filter/TripFilter';


export default function  TripAlert() {
  const { direction } = useLanguage();
  const cardSize = "md";
  const showTrends = false;

  // Handler for filter functionality
  const handleFilterSearch = (filters: {
    entryPorts: string[];
    exitPorts: string[];
    selectedAlerts: string[];
    truckInfo: {
      transitNo: string;
      seqNo: string;
      driverName: string;
      plate: string;
      tracker: string;
      tripCode: string;
      priority: string[];
      status: string[];
      location: string[];
    };
    dates: {
      transitDate: string;
      entryFrom: string;
      entryTo: string;
    };
    orderBy: {
      direction: "asc" | "desc";
      field: string;
    };
    shipmentDesc: string[];
    transitTypes: string[];
    createdRange: {
      createdAtStart: string;
      createdAtEnd: string;
    };
  }) => {
    console.log('Applied filters:', filters);
    // TODO: Implement filter logic to update the data displayed
    // This could involve updating state, making API calls, etc.
  };
  const dashboardCards = [
    {
      id: "inactive-trips-count",
      titleKey: "dashboard.card.activeTrips",
      value: 1551,
      icon: "bell-off" as const,
      color: "green" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "resolved-trips-count",
      titleKey: "dashboard.card.closedTrips",
      value: 1562,
      icon: "check" as const,
      color: "red" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "critical-alerts-count",
      titleKey: "dashboard.card.notAcknowledgeAlerts",
      value: 0,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "pending-reviews-count",
      titleKey: "dashboard.card.acknowledgeAlerts",
      value: 847,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    
  ];

  return (
   <div className="bg-gray-100 min-h-screen p-6 space-y-2 " dir={direction}>
     {/* Trip Filter positioned in top-left */}
     

           <div className="mb-4">
             <h2 className="text-xl font-semibold text-gray-900 mb-4">
               {/* {t("dummy.section.individualCards")} */}
             </h2>
             <TripFilter onSearch={handleFilterSearch} />
             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                <ClockCard variant="compact" showSettings={false} dateFormat='hijri' size={cardSize}/>

               {dashboardCards.slice(0, 5).map((card) => (
                
                 <SummaryCard
                   key={`individual-${card.id}`}
                   id={card.id}
                   titleKey={card.titleKey}
                   value={card.value}
                   icon={card.icon}
                   color={card.color}
                   size={cardSize}
                   showTrends={showTrends}
                   trend={card.trend}
                   className="w-full"
                 />
               ))}
             </div>
           </div>
            <TripAlertList/>
           
         </div>
  );
}