"use client";

import React, { useState } from "react";
import ClockComponent from "@/components/shared/ClockComponent";
import { useLanguage } from "@/contexts/LanguageContext";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code }: CodeBlockProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
      <pre className="text-sm text-gray-100">
        <code>{code}</code>
      </pre>
    </div>
  );
}

interface ExampleSectionProps {
  title: string;
  description: string;
  code: string;
  children: React.ReactNode;
}

function ExampleSection({ title, description, code, children }: ExampleSectionProps) {
  const [showCode, setShowCode] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
      <div className="mb-4">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <button
          onClick={() => setShowCode(!showCode)}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          {showCode ? "Hide Code" : "Show Code"} {showCode ? "▲" : "▼"}
        </button>
      </div>
      
      {showCode && (
        <div className="mb-6">
          <CodeBlock code={code} />
        </div>
      )}
      
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Live Demo:</h4>
        {children}
      </div>
    </div>
  );
}

export default function ClockCardKnowledgeBase() {
  const { language } = useLanguage();

  const basicExample = `import ClockComponent from "@/components/shared/ClockComponent";

function BasicExample() {
  return (
    <ClockComponent
      size="md"
      variant="default"
    />
  );
}`;

  const analogExample = `import ClockComponent from "@/components/shared/ClockComponent";

function AnalogExample() {
  return (
    <ClockComponent
      size="lg"
      variant="detailed"
    />
  );
}`;

  const compactExample = `import ClockComponent from "@/components/shared/ClockComponent";

function CompactExample() {
  return (
    <ClockComponent
      size="sm"
      variant="compact"
    />
  );
}`;

  const customizedExample = `import ClockComponent from "@/components/shared/ClockComponent";

function CustomizedExample() {
  return (
    <ClockComponent
      size="lg"
      variant="detailed"
      enableTimezoneControl={true}
      enableTimeFormatControl={true}
      enableDateFormatControl={true}
    />
  );
}`;

  const multipleTimezoneExample = `import ClockComponent from "@/components/shared/ClockComponent";

function MultipleTimezoneExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <ClockComponent
                size="md"
                variant="default"
                showSettings={true}
                enableDisplayStyleControl={true}
                enableTimezoneControl={true}
              />
      <ClockComponent
                size="md"
                variant="default"
                showSettings={true}
                enableTimezoneControl={true}
              />
      <ClockComponent
        size="md"
        variant="default"
        enableTimezoneControl={true}
      />
    </div>
  );
}`;

  return (
    <div className="p-8">
      <div className="max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "مكون الساعة" : "Clock Card Component"}
          </h1>
          <p className="text-xl text-gray-600">
            {language === "ar"
              ? "دليل شامل لاستخدام مكون الساعة مع جميع الخصائص والأحجام والأنماط"
              : "Comprehensive guide to using the Clock Card component with all props, sizes, and styles"}
          </p>
        </div>

        {/* Props Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "الخصائص المتاحة" : "Available Props"}
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Prop</th>
                  <th className="text-left py-2 px-4">Type</th>
                  <th className="text-left py-2 px-4">Default</th>
                  <th className="text-left py-2 px-4">Description</th>
                </tr>
              </thead>
              <tbody className="text-gray-600">
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">variant</td>
                  <td className="py-2 px-4">&quot;default&quot; | &quot;compact&quot; | &quot;detailed&quot;</td>
                  <td className="py-2 px-4">&quot;default&quot;</td>
                  <td className="py-2 px-4">Clock card variant</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">size</td>
                  <td className="py-2 px-4">&quot;sm&quot; | &quot;md&quot; | &quot;lg&quot;</td>
                  <td className="py-2 px-4">&quot;md&quot;</td>
                  <td className="py-2 px-4">Clock card size</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">showSettings</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">true</td>
                  <td className="py-2 px-4">Show settings button</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">enableTimezoneControl</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">true</td>
                  <td className="py-2 px-4">Enable timezone selection</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">enableDateFormatControl</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">true</td>
                  <td className="py-2 px-4">Enable date format control</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">enableTimeFormatControl</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">true</td>
                  <td className="py-2 px-4">Enable time format control</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">className</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Additional CSS classes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Examples */}
        <ExampleSection
          title={language === "ar" ? "مثال أساسي - ساعة رقمية" : "Basic Example - Digital Clock"}
          description={language === "ar" 
            ? "ساعة رقمية بسيطة بالحجم المتوسط مع المنطقة الزمنية للرياض"
            : "Simple digital clock with medium size and Riyadh timezone"}
          code={basicExample}
        >
          <ClockComponent
            size="md"
            variant="default"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "ساعة تناظرية" : "Analog Clock"}
          description={language === "ar"
            ? "ساعة تناظرية كبيرة مع عرض التاريخ والمنطقة الزمنية لنيويورك"
            : "Large analog clock with date display and New York timezone"}
          code={analogExample}
        >
          <ClockComponent
            size="lg"
            variant="detailed"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "ساعة مدمجة" : "Compact Clock"}
          description={language === "ar"
            ? "ساعة صغيرة مدمجة بدون عرض التاريخ للمساحات المحدودة"
            : "Small compact clock without date display for limited spaces"}
          code={compactExample}
        >
          <ClockComponent
            size="sm"
            variant="compact"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "ساعة مخصصة" : "Customized Clock"}
          description={language === "ar"
            ? "ساعة مخصصة مع عرض المنطقة الزمنية وتنسيق 12 ساعة"
            : "Customized clock with timezone display and 12-hour format"}
          code={customizedExample}
        >
          <ClockComponent
            size="lg"
            variant="detailed"
            enableTimezoneControl={true}
            enableTimeFormatControl={true}
            enableDateFormatControl={true}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "مناطق زمنية متعددة" : "Multiple Timezones"}
          description={language === "ar"
            ? "عرض عدة ساعات لمناطق زمنية مختلفة في شبكة"
            : "Display multiple clocks for different timezones in a grid"}
          code={multipleTimezoneExample}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <ClockComponent
                size="md"
                variant="default"
                enableTimezoneControl={true}
              />
            <ClockComponent
                size="md"
                variant="default"
                enableTimezoneControl={true}
              />
            <ClockComponent
                size="md"
                variant="default"
                showSettings={true}
                enableDisplayStyleControl={true}
                enableTimezoneControl={true}
              />
          </div>
        </ExampleSection>

        {/* Size Options */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "خيارات الأحجام" : "Size Options"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "يدعم مكون الساعة ثلاثة أحجام مختلفة لتناسب احتياجات التصميم المختلفة"
              : "The Clock component supports three different sizes to fit various design needs"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم صغير (sm)" : "Small Size (sm)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "80px ارتفاع - مناسب للشريط الجانبي" : "80px height - Perfect for sidebars"}
              </p>
              <ClockComponent
                size="sm"
                variant="default"
                showSettings={true}
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم متوسط (md)" : "Medium Size (md)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "120px ارتفاع - الحجم الافتراضي" : "120px height - Default size"}
              </p>
              <ClockComponent
                size="md"
                variant="default"
                showSettings={true}
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم كبير (lg)" : "Large Size (lg)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "160px ارتفاع - للعرض البارز" : "160px height - For prominent display"}
              </p>
              <ClockComponent
                size="lg"
                variant="default"
                showSettings={true}
              />
            </div>
          </div>
        </div>

        {/* Style Comparison */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "مقارنة الأنماط" : "Style Comparison"}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "النمط الرقمي" : "Digital Style"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "عرض رقمي واضح وسهل القراءة" : "Clear digital display, easy to read"}
              </p>
              <ClockComponent
              size="lg"
              variant="detailed"
              showSettings={true}
              enableTimezoneControl={true}
            />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "النمط التناظري" : "Analog Style"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "ساعة تناظرية كلاسيكية مع عقارب" : "Classic analog clock with hands"}
              </p>
              <ClockComponent
              size="lg"
              variant="detailed"
              showSettings={true}
              enableDisplayStyleControl={true}
            />
            </div>
          </div>
        </div>

        {/* Hidden Settings Examples */}
        <ExampleSection
          title={language === "ar" ? "ساعة بدون إعدادات" : "Clock with Hidden Settings"}
          description={language === "ar"
            ? "ساعة بدون زر الإعدادات للعرض النظيف"
            : "Clock without settings button for clean display"}
          code={`import ClockComponent from "@/components/shared/ClockComponent";

function HiddenSettingsExample() {
  return (
    <ClockComponent
      size="md"
      variant="default"
      showSettings={false}
    />
  );
}`}
        >
          <ClockComponent
            size="md"
            variant="default"
            showSettings={false}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "ساعة بالتاريخ الهجري كافتراضي" : "Clock with Hijri Date as Default"}
          description={language === "ar"
            ? "ساعة تعرض التاريخ الهجري كتنسيق افتراضي بدون إعدادات"
            : "Clock displaying Hijri date as default format without settings"}
          code={`import ClockComponent from "@/components/shared/ClockComponent";

function HijriDefaultExample() {
  return (
    <ClockComponent
      size="lg"
      variant="detailed"
      showSettings={false}
      dateFormat="hijri"
    />
  );
}`}
        >
          <ClockComponent
            size="lg"
            variant="detailed"
            showSettings={false}
            dateFormat="hijri"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "مجموعة ساعات بالتاريخ الهجري" : "Multiple Clocks with Hijri Date"}
          description={language === "ar"
            ? "عرض عدة ساعات بالتاريخ الهجري لمناطق زمنية مختلفة"
            : "Display multiple clocks with Hijri date for different timezones"}
          code={`import ClockComponent from "@/components/shared/ClockComponent";

function HijriMultipleExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <ClockComponent
        size="md"
        variant="default"
        showSettings={false}
        dateFormat="hijri"
        initialTimezone="Asia/Riyadh"
      />
      <ClockComponent
        size="md"
        variant="default"
        showSettings={false}
        dateFormat="hijri"
        initialTimezone="Asia/Mecca"
      />
      <ClockComponent
        size="md"
        variant="default"
        showSettings={false}
        dateFormat="hijri"
        initialTimezone="Asia/Dubai"
      />
    </div>
  );
}`}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <ClockComponent
              size="md"
              variant="default"
              showSettings={false}
              dateFormat="hijri"
              initialTimezone="Asia/Riyadh"
            />
            <ClockComponent
              size="md"
              variant="default"
              showSettings={false}
              dateFormat="hijri"
              initialTimezone="Asia/Qatar"
            />
            <ClockComponent
              size="md"
              variant="default"
              showSettings={false}
              dateFormat="hijri"
              initialTimezone="Asia/Dubai"
            />
          </div>
        </ExampleSection>

        {/* Supported Timezones */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "المناطق الزمنية المدعومة" : "Supported Timezones"}
          </h2>
          <p className="text-gray-600 mb-4">
            {language === "ar"
              ? "يدعم المكون جميع المناطق الزمنية IANA. إليك بعض الأمثلة الشائعة:"
              : "The component supports all IANA timezones. Here are some common examples:"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <ClockComponent
              size="sm"
              variant="compact"
              showSettings={true}
              enableTimezoneControl={true}
              initialTimezone="Asia/Riyadh"
            />
            <ClockComponent
              size="sm"
              variant="compact"
              showSettings={true}
              enableTimezoneControl={true}
              initialTimezone="America/New_York"
            />
            <ClockComponent
              size="sm"
              variant="compact"
              showSettings={true}
              enableTimezoneControl={true}
              initialTimezone="Europe/London"
            />
            <ClockComponent
              size="sm"
              variant="compact"
              showSettings={true}
              enableTimezoneControl={true}
              initialTimezone="Asia/Tokyo"
            />
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              {language === "ar" ? "أمثلة على المناطق الزمنية:" : "Timezone Examples:"}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800 text-sm">
              <div>
                <p><code className="bg-blue-100 px-2 py-1 rounded">Asia/Riyadh</code> - {language === "ar" ? "الرياض، السعودية" : "Riyadh, Saudi Arabia"}</p>
                <p><code className="bg-blue-100 px-2 py-1 rounded">America/New_York</code> - {language === "ar" ? "نيويورك، الولايات المتحدة" : "New York, USA"}</p>
                <p><code className="bg-blue-100 px-2 py-1 rounded">Europe/London</code> - {language === "ar" ? "لندن، المملكة المتحدة" : "London, UK"}</p>
              </div>
              <div>
                <p><code className="bg-blue-100 px-2 py-1 rounded">Asia/Tokyo</code> - {language === "ar" ? "طوكيو، اليابان" : "Tokyo, Japan"}</p>
                <p><code className="bg-blue-100 px-2 py-1 rounded">Australia/Sydney</code> - {language === "ar" ? "سيدني، أستراليا" : "Sydney, Australia"}</p>
                <p><code className="bg-blue-100 px-2 py-1 rounded">Europe/Paris</code> - {language === "ar" ? "باريس، فرنسا" : "Paris, France"}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Best Practices */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "أفضل الممارسات" : "Best Practices"}
          </h2>
          <ul className="space-y-3 text-gray-600">
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "استخدم الحجم الصغير (sm) في الشريط الجانبي أو المساحات المحدودة"
                : "Use small size (sm) for sidebars or limited spaces"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "اختر النمط التناظري للتصميمات الكلاسيكية والرقمي للوضوح"
                : "Choose analog style for classic designs, digital for clarity"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "اعرض المنطقة الزمنية عند عرض عدة ساعات"
                : "Show timezone when displaying multiple clocks"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "استخدم تنسيق 24 ساعة للتطبيقات الرسمية"
                : "Use 24-hour format for official applications"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "تأكد من صحة معرف المنطقة الزمنية IANA"
                : "Ensure valid IANA timezone identifiers"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "اختبر الساعة مع مناطق زمنية مختلفة للتأكد من الدقة"
                : "Test the clock with different timezones for accuracy"}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}