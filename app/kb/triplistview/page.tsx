"use client";

import React, { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import TripListView from "@/components/TripListView";
import Link from "next/link";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code, language = "typescript" }: CodeBlockProps) {
  return (
    <div className="relative">
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
        <code className={`language-${language}`}>{code}</code>
      </pre>
    </div>
  );
}

export default function TripListViewPage() {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  const [activeTab, setActiveTab] = useState("overview");

  const content = {
    en: {
      title: "TripListView Component",
      subtitle: "Advanced Trip Management List with Expandable Details",
      tabs: {
        overview: "Overview",
        props: "Props & Usage",
        nested: "Nested Components",
        demo: "Live Demo"
      },
      overview: {
        title: "Component Overview",
        description: "TripListView is a comprehensive React component that displays trip data in a tabular format with expandable rows for detailed information. It combines DataTable functionality with master-detail views for enhanced trip management.",
        features: [
          "Displays trip data in a sortable, exportable table format",
          "Expandable rows showing detailed trip information and map visualization",
          "Status indicators with icons for trip monitoring",
          "Integration with DataTable component for advanced table features",
          "Real-time data fetching from JSON API",
          "Master-detail view with trip summary and interactive map"
        ],
        location: "/components/TripListView.tsx"
      },
      relatedComponents: {
        title: "المكونات ذات الصلة",
        description: "المكونات التي تعمل مع TripListView:",
        items: [
          {
            name: "TripMasterDetailView",
            link: "/kb/tripmasterdetailview",
            description: "مكون عرض التفاصيل القابل للتوسيع المستخدم داخل صفوف TripListView"
          },
          {
            name: "TripDetailSummary",
            link: "/kb/tripdetailsummary",
            description: "مكون الملخص المستخدم داخل الشريط الجانبي لـ TripMasterDetailView"
          }
        ]
      },
      props: {
        title: "Props & Usage",
        description: "TripListView is a standalone component that doesn't require external props. It manages its own state and data fetching.",
        dataStructure: {
          title: "Data Structure (Trip Interface)",
          description: "The component works with Trip objects that contain comprehensive trip information:"
        },
        usage: {
          title: "Basic Usage",
          description: "Import and use the component directly in your React application:"
        }
      },
      nested: {
        title: "Nested Components",
        description: "TripListView utilizes several nested components to provide comprehensive functionality:",
        components: [
          {
            name: "DataTable<Trip>",
            path: "/components/shared/DataTable.tsx",
            description: "Core table component providing sorting, filtering, pagination, and export functionality",
            props: [
              "keyField: 'id' - Unique identifier field",
              "data: Trip[] - Array of trip objects",
              "stickyHeader: true - Fixed header on scroll",
              "expandable: true - Enables row expansion",
              "pageSize: 10 - Number of rows per page",
              "sortable: true - Column sorting capability",
              "exportable: true - Excel export functionality"
            ]
          },
          {
            name: "TripMasterDetailView",
            path: "/components/TripMasterDetailView.tsx",
            description: "Expanded row content showing detailed trip information and map visualization",
            props: [
              "row: Trip - The trip object for the expanded row"
            ]
          },
          {
            name: "TripDetailSummary",
            path: "/components/TripDetailSummary.tsx",
            description: "Displays comprehensive trip details in the sidebar of the master-detail view",
            props: [
              "trip: Trip - Trip data object with all details"
            ]
          },
          {
            name: "TripAlertMapViewer",
            path: "/components/TripAlertMapViewer.tsx",
            description: "Interactive map component showing trip route and alert locations",
            props: [
              "tripAlerts: TripAlert[] - Array of trip alerts",
              "selectedTripId: string - ID of the selected trip",
              "className: string - CSS classes for styling"
            ]
          }
        ],
        icons: {
          title: "Icon Components",
          description: "Status indicators using Lucide React icons:",
          items: [
            "CheckIcon - Active trip status (green)",
            "BatteryIcon - Charging status (gray)",
            "PowerOff - Offline status (red)"
          ]
        }
      },
      demo: {
        title: "Live Demo",
        description: "Interactive demonstration of the TripListView component with real data:"
      }
    },
    ar: {
      title: "مكون TripListView",
      subtitle: "قائمة إدارة الرحلات المتقدمة مع تفاصيل قابلة للتوسيع",
      tabs: {
        overview: "نظرة عامة",
        props: "الخصائص والاستخدام",
        nested: "المكونات المتداخلة",
        demo: "عرض تفاعلي"
      },
      overview: {
        title: "نظرة عامة على المكون",
        description: "TripListView هو مكون React شامل يعرض بيانات الرحلات في تنسيق جدولي مع صفوف قابلة للتوسيع للحصول على معلومات مفصلة. يجمع بين وظائف DataTable مع عروض التفاصيل الرئيسية لإدارة محسنة للرحلات.",
        features: [
          "يعرض بيانات الرحلات في تنسيق جدول قابل للفرز والتصدير",
          "صفوف قابلة للتوسيع تظهر معلومات مفصلة عن الرحلة وتصور الخريطة",
          "مؤشرات الحالة مع الرموز لمراقبة الرحلة",
          "التكامل مع مكون DataTable للميزات المتقدمة للجدول",
          "جلب البيانات في الوقت الفعلي من JSON API",
          "عرض رئيسي-تفصيلي مع ملخص الرحلة وخريطة تفاعلية"
        ],
        location: "/components/TripListView.tsx"
      },
      props: {
        title: "الخصائص والاستخدام",
        description: "TripListView هو مكون مستقل لا يتطلب خصائص خارجية. يدير حالته الخاصة وجلب البيانات.",
        dataStructure: {
          title: "هيكل البيانات (واجهة Trip)",
          description: "يعمل المكون مع كائنات Trip التي تحتوي على معلومات شاملة عن الرحلة:"
        },
        usage: {
          title: "الاستخدام الأساسي",
          description: "استورد واستخدم المكون مباشرة في تطبيق React الخاص بك:"
        }
      },
      nested: {
        title: "المكونات المتداخلة",
        description: "يستخدم TripListView عدة مكونات متداخلة لتوفير وظائف شاملة:",
        components: [
          {
            name: "DataTable<Trip>",
            path: "/components/shared/DataTable.tsx",
            description: "مكون الجدول الأساسي الذي يوفر الفرز والتصفية والترقيم ووظائف التصدير",
            props: [
              "keyField: 'id' - حقل المعرف الفريد",
              "data: Trip[] - مصفوفة من كائنات الرحلة",
              "stickyHeader: true - رأس ثابت عند التمرير",
              "expandable: true - يمكن توسيع الصفوف",
              "pageSize: 10 - عدد الصفوف في كل صفحة",
              "sortable: true - قابلية فرز الأعمدة",
              "exportable: true - وظيفة تصدير Excel"
            ]
          },
          {
            name: "TripMasterDetailView",
            path: "/components/TripMasterDetailView.tsx",
            description: "محتوى الصف الموسع يظهر معلومات مفصلة عن الرحلة وتصور الخريطة",
            props: [
              "row: Trip - كائن الرحلة للصف الموسع"
            ]
          },
          {
            name: "TripDetailSummary",
            path: "/components/TripDetailSummary.tsx",
            description: "يعرض تفاصيل شاملة عن الرحلة في الشريط الجانبي للعرض الرئيسي-التفصيلي",
            props: [
              "trip: Trip - كائن بيانات الرحلة مع جميع التفاصيل"
            ]
          },
          {
            name: "TripAlertMapViewer",
            path: "/components/TripAlertMapViewer.tsx",
            description: "مكون خريطة تفاعلي يظهر مسار الرحلة ومواقع التنبيهات",
            props: [
              "tripAlerts: TripAlert[] - مصفوفة تنبيهات الرحلة",
              "selectedTripId: string - معرف الرحلة المحددة",
              "className: string - فئات CSS للتصميم"
            ]
          }
        ],
        icons: {
          title: "مكونات الرموز",
          description: "مؤشرات الحالة باستخدام رموز Lucide React:",
          items: [
            "CheckIcon - حالة الرحلة النشطة (أخضر)",
            "BatteryIcon - حالة الشحن (رمادي)",
            "PowerOff - حالة عدم الاتصال (أحمر)"
          ]
        }
      },
      demo: {
        title: "عرض تفاعلي",
        description: "عرض تفاعلي لمكون TripListView مع بيانات حقيقية:"
      }
    }
  };

  const t = content[language];

  const tripInterfaceCode = `interface Trip {
  id: number;
  transitNumber: number;
  description: string;
  entry: string;
  lastSeen: string;
  tracker: string;
  driver: string;
  vehicle: string;
  alerts: string;
  status: ("active" | "charging" | "offline")[];
  tripId: string;
  tripStatus: 'pending' | 'activated' | 'ended' | 'cancelled';
  transitType: string;
  creationDate: string;
  activationDate: string | null;
  completionDate: string | null;
  driver_details: TripDriver;
  vehicle_details: TripVehicle;
  route: TripRoute;
  shipment: TripShipment;
  tracking: TripTracking;
  compliance: TripCompliance;
}`;

  const usageCode = `import TripListView from "@/components/TripListView";

function TripsPage() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Trip Management</h1>
      <TripListView />
    </div>
  );
}`;

  const dataTablePropsCode = `<DataTable<Trip>
  keyField="id"
  data={data}
  stickyHeader={true}
  expandable={true}
  expandRowByClick={false}
  pageSize={10}
  filterable={false}
  searchable={false}
  sortable={true}
  selectable={false}
  exportable={true}
  exportFormats={["xlsx"]}
  exportFileNameKey="trip-data"
  columns={[
    {
      key: "transitNumber",
      title: "Transit Number",
      render: (row) => (
        <span className="text-blue-400 font-medium">
          {row.transitNumber}
        </span>
      ),
    },
    { key: "description", title: "Shipment Description" },
    { key: "entry", title: "Entry-port - Exit-port" },
    // ... more columns
  ]}
  expandedRowRender={(row) => <TripMasterDetailView row={row} />}
/>`;

  return (
    <div className={`container mx-auto p-6 ${isRTL ? "text-right" : "text-left"}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">{t.title}</h1>
          <p className="text-xl text-muted-foreground">{t.subtitle}</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 border-b">
          {Object.entries(t.tabs).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === key
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              {label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === "overview" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.overview.title}</h2>
                <p className="text-lg mb-4">{t.overview.description}</p>
                <div className="mb-4">
                  <Badge variant="outline" className="mb-2">{t.overview.location}</Badge>
                </div>
                <h3 className="text-lg font-semibold mb-3">Key Features:</h3>
                <ul className="space-y-2">
                  {t.overview.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </Card>
            </>
          )}

          {activeTab === "props" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.props.title}</h2>
                <p className="mb-6">{t.props.description}</p>
                
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t.props.dataStructure.title}</h3>
                  <p className="mb-4">{t.props.dataStructure.description}</p>
                  <CodeBlock code={tripInterfaceCode} />
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">{t.props.usage.title}</h3>
                  <p className="mb-4">{t.props.usage.description}</p>
                  <CodeBlock code={usageCode} />
                </div>
              </Card>
            </>
          )}

          {activeTab === "nested" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.nested.title}</h2>
                <p className="mb-6">{t.nested.description}</p>
                
                <div className="space-y-6">
                  {t.nested.components.map((component, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">{component.name}</h3>
                        <Badge variant="outline" className="text-xs">{component.path}</Badge>
                      </div>
                      <p className="text-muted-foreground mb-3">{component.description}</p>
                      <div>
                        <h4 className="font-medium mb-2">Props:</h4>
                        <ul className="space-y-1">
                          {component.props.map((prop, propIndex) => (
                            <li key={propIndex} className="text-sm font-mono bg-muted p-2 rounded">
                              {prop}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">{t.nested.icons.title}</h3>
                  <p className="mb-3">{t.nested.icons.description}</p>
                  <ul className="space-y-2">
                    {t.nested.icons.items.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary mt-1">•</span>
                        <span className="font-mono text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">DataTable Configuration Example:</h3>
                  <CodeBlock code={dataTablePropsCode} />
                </div>
              </Card>

              {/* Related Components */}
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{language === "en" ? "Related Components" : "المكونات ذات الصلة"}</h2>
                <p className="mb-4">{language === "en" ? "Components that work together with TripListView:" : "المكونات التي تعمل مع TripListView:"}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link href="/kb/tripmasterdetailview" className="block">
                    <div className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <h3 className="font-semibold text-primary mb-2">TripMasterDetailView</h3>
                      <p className="text-sm text-muted-foreground">{language === "en" ? "Expandable detail view component used within TripListView rows" : "مكون عرض التفاصيل القابل للتوسيع المستخدم داخل صفوف TripListView"}</p>
                    </div>
                  </Link>
                  <Link href="/kb/tripdetailsummary" className="block">
                    <div className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <h3 className="font-semibold text-primary mb-2">TripDetailSummary</h3>
                      <p className="text-sm text-muted-foreground">{language === "en" ? "Summary component used within TripMasterDetailView sidebar" : "مكون الملخص المستخدم داخل الشريط الجانبي لـ TripMasterDetailView"}</p>
                    </div>
                  </Link>
                </div>
              </Card>
            </>
          )}

          {activeTab === "demo" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.demo.title}</h2>
                <p className="mb-6">{t.demo.description}</p>
                <div className="border rounded-lg p-4 bg-muted/50">
                  <TripListView />
                </div>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  );
}