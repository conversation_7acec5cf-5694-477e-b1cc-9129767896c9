"use client";

import React, { useState } from "react";
import SummaryCard from "@/components/shared/SummaryCard";
import DashboardSummaryCards from "@/components/shared/DashboardSummaryCards";
import { useLanguage } from "@/contexts/LanguageContext";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code }: CodeBlockProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
      <pre className="text-sm text-gray-100">
        <code>{code}</code>
      </pre>
    </div>
  );
}

interface ExampleSectionProps {
  title: string;
  description: string;
  code: string;
  children: React.ReactNode;
}

function ExampleSection({ title, description, code, children }: ExampleSectionProps) {
  const [showCode, setShowCode] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
      <div className="mb-4">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <button
          onClick={() => setShowCode(!showCode)}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          {showCode ? "Hide Code" : "Show Code"} {showCode ? "▲" : "▼"}
        </button>
      </div>
      
      {showCode && (
        <div className="mb-6">
          <CodeBlock code={code} />
        </div>
      )}
      
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Live Demo:</h4>
        {children}
      </div>
    </div>
  );
}

export default function SummaryCardKnowledgeBase() {
  const { language } = useLanguage();

  const basicExample = `import SummaryCard from "@/components/shared/SummaryCard";

function BasicExample() {
  return (
    <SummaryCard
      id="active-trips"
      titleKey="dashboard.card.activeTrips"
      value="1,234"
      icon="check"
      color="blue"
    />
  );
}`;

  const loadingExample = `import SummaryCard from "@/components/shared/SummaryCard";

function LoadingExample() {
  return (
    <SummaryCard
      id="loading-example"
      titleKey="dashboard.card.monitoredVehicles"
      value="---"
      icon="eye"
      color="gray"
      loading={true}
    />
  );
}`;

  const trendExample = `import SummaryCard from "@/components/shared/SummaryCard";

function TrendExample() {
  return (
    <SummaryCard
      id="trend-example"
      titleKey="dashboard.card.activeTrips"
      value="1,234"
      icon="check"
      color="green"
      trend={{ value: 12.5, direction: "up" }}
      showTrends={true}
    />
  );
}`;

  const dashboardExample = `import DashboardSummaryCards from "@/components/shared/DashboardSummaryCards";

function DashboardExample() {
  const sampleCards = [
    {
      id: "active-trips",
      titleKey: "dashboard.card.activeTrips",
      value: 1234,
      icon: "check",
      color: "green",
      trend: { value: 12.5, direction: "up" },
    },
    {
      id: "alerts-pending",
      titleKey: "dashboard.card.notAcknowledgeAlerts",
      value: 56,
      icon: "bell-orange",
      color: "orange",
      trend: { value: 8.3, direction: "down" },
    }
  ];

  return (
    <DashboardSummaryCards
      cards={sampleCards}
      layout="auto"
      showTrends={true}
      cardSize="md"
    />
  );
}`;

  // Sample data for examples
  const sampleCards = [
    {
      id: "active-trips",
      titleKey: "dashboard.card.activeTrips",
      value: 1234,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 12.5, direction: "up" as const },
    },
    {
      id: "alerts-pending",
      titleKey: "dashboard.card.notAcknowledgeAlerts",
      value: 56,
      icon: "bell-orange" as const,
      color: "orange" as const,
      trend: { value: 8.3, direction: "down" as const },
    },
    {
      id: "closed-trips",
      titleKey: "dashboard.card.closedTrips",
      value: 789,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "acknowledged-alerts",
      titleKey: "dashboard.card.acknowledgeAlerts",
      value: 345,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 15.7, direction: "up" as const },
    },
    {
      id: "monitored-vehicles",
      titleKey: "dashboard.card.monitoredVehicles",
      value: 67,
      icon: "eye" as const,
      color: "blue" as const,
      trend: { value: 3.2, direction: "down" as const },
    },
  ];

  return (
    <div className="p-8">
      <div className="max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "مكون بطاقة الملخص" : "Summary Card Component"}
          </h1>
          <p className="text-xl text-gray-600">
            {language === "ar"
              ? "دليل شامل لاستخدام مكون بطاقة الملخص مع جميع الخصائص والأحجام والأنماط"
              : "Comprehensive guide to using the Summary Card component with all props, sizes, and styles"}
          </p>
        </div>

        {/* Props Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "الخصائص المتاحة" : "Available Props"}
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Prop</th>
                  <th className="text-left py-2 px-4">Type</th>
                  <th className="text-left py-2 px-4">Default</th>
                  <th className="text-left py-2 px-4">Description</th>
                </tr>
              </thead>
              <tbody className="text-gray-600">
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">id</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Unique identifier for the card</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">titleKey</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Translation key for the card title</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">value</td>
                  <td className="py-2 px-4">string | number</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Main value to display</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">icon</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Icon identifier (check, bell-orange, etc.)</td>
                </tr>
                <tr className="border-b">
                   <td className="py-2 px-4 font-mono">color</td>
                   <td className="py-2 px-4">&quot;blue&quot; | &quot;green&quot; | &quot;orange&quot; | &quot;red&quot; | &quot;gray&quot;</td>
                   <td className="py-2 px-4">&quot;blue&quot;</td>
                   <td className="py-2 px-4">Color theme for the card</td>
                 </tr>
                 <tr className="border-b">
                   <td className="py-2 px-4 font-mono">size</td>
                   <td className="py-2 px-4">&quot;sm&quot; | &quot;md&quot; | &quot;lg&quot;</td>
                   <td className="py-2 px-4">&quot;md&quot;</td>
                   <td className="py-2 px-4">Card size</td>
                 </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">loading</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Show loading state</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">trend</td>
                  <td className="py-2 px-4">object</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Trend data with value and direction</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">showTrends</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Display trend indicators</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">onClick</td>
                  <td className="py-2 px-4">function</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Click handler function</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">className</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Additional CSS classes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Examples */}
        <ExampleSection
          title={language === "ar" ? "مثال أساسي" : "Basic Example"}
          description={language === "ar" 
            ? "بطاقة ملخص بسيطة مع أيقونة ولون"
            : "Simple summary card with icon and color"}
          code={basicExample}
        >
          <SummaryCard
            id="basic-example"
            titleKey="dashboard.card.activeTrips"
            value="1,234"
            icon="check"
            color="blue"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "حالة التحميل" : "Loading State"}
          description={language === "ar"
            ? "بطاقة ملخص في حالة التحميل"
            : "Summary card in loading state"}
          code={loadingExample}
        >
          <SummaryCard
            id="loading-example"
            titleKey="dashboard.card.monitoredVehicles"
            value="---"
            icon="eye"
            color="gray"
            loading={true}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "بطاقة مع اتجاه" : "Card with Trend"}
          description={language === "ar"
            ? "بطاقة ملخص مع عرض اتجاه التغيير"
            : "Summary card with trend indicator"}
          code={trendExample}
        >
          <SummaryCard
            id="trend-example"
            titleKey="dashboard.card.activeTrips"
            value="1,234"
            icon="check"
            color="green"
            trend={{ value: 12.5, direction: "up" }}
            showTrends={true}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "مجموعة بطاقات لوحة التحكم" : "Dashboard Cards Collection"}
          description={language === "ar"
            ? "مجموعة كاملة من بطاقات لوحة التحكم"
            : "Complete collection of dashboard summary cards"}
          code={dashboardExample}
        >
          <DashboardSummaryCards
            cards={sampleCards.slice(0, 4)}
            layout="auto"
            showTrends={true}
            cardSize="md"
          />
        </ExampleSection>

        {/* Size Options */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "خيارات الأحجام" : "Size Options"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "يدعم مكون بطاقة الملخص ثلاثة أحجام مختلفة لتناسب احتياجات التصميم المختلفة"
              : "The Summary Card component supports three different sizes to fit various design needs"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم صغير (sm)" : "Small Size (sm)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "مناسب للشريط الجانبي والمساحات المحدودة" : "Perfect for sidebars and limited spaces"}
              </p>
              <SummaryCard
                id="size-sm"
                titleKey="dashboard.card.activeTrips"
                value="1,234"
                icon="check"
                color="blue"
                size="sm"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم متوسط (md)" : "Medium Size (md)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "الحجم الافتراضي للاستخدام العام" : "Default size for general use"}
              </p>
              <SummaryCard
                id="size-md"
                titleKey="dashboard.card.activeTrips"
                value="1,234"
                icon="check"
                color="green"
                size="md"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "حجم كبير (lg)" : "Large Size (lg)"}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {language === "ar" ? "للعرض البارز والمعلومات المهمة" : "For prominent display and important information"}
              </p>
              <SummaryCard
                id="size-lg"
                titleKey="dashboard.card.activeTrips"
                value="1,234"
                icon="check"
                color="orange"
                size="lg"
              />
            </div>
          </div>
        </div>

        {/* Color Variations */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "تنويعات الألوان" : "Color Variations"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "يدعم المكون خمسة ألوان مختلفة لتمثيل أنواع مختلفة من البيانات"
              : "The component supports five different colors to represent different types of data"}
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            {['blue', 'green', 'orange', 'red', 'gray'].map((color, index) => (
              <div key={color}>
                <h4 className="text-sm font-medium text-gray-700 mb-2 capitalize">
                  {color} Theme
                </h4>
                <SummaryCard
                  id={`color-${color}`}
                  titleKey="dashboard.card.activeTrips"
                  value={1000 + index * 100}
                  icon="check"
                  color={color as "blue" | "green" | "orange" | "red" | "gray"}
                  size="sm"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Icon Variations */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "تنويعات الأيقونات" : "Icon Variations"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "مجموعة من الأيقونات المتاحة لأنواع مختلفة من البيانات"
              : "Collection of available icons for different types of data"}
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
            {[
              { icon: 'check', label: 'Success/Active' },
              { icon: 'bell-orange', label: 'Warning Alerts' },
              { icon: 'bell-gray', label: 'Acknowledged' },
              { icon: 'bell-off', label: 'Disabled' },
              { icon: 'prohibition', label: 'Blocked/Closed' },
              { icon: 'eye', label: 'Monitoring' }
            ].map((item, index) => (
              <div key={item.icon}>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  {item.label}
                </h4>
                <SummaryCard
                  id={`icon-${item.icon}`}
                  titleKey="dashboard.card.activeTrips"
                  value={500 + index * 50}
                  icon={item.icon as "check" | "bell-orange" | "bell-gray" | "bell-off" | "prohibition" | "eye"}
                  color="blue"
                  size="sm"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Dashboard Layouts */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "تخطيطات لوحة التحكم" : "Dashboard Layouts"}
          </h2>
          
          <div className="space-y-8">
            {/* Auto Layout */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تخطيط تلقائي" : "Auto Layout"}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {language === "ar" ? "شبكة متجاوبة تتكيف مع حجم الشاشة" : "Responsive grid that adapts to screen size"}
              </p>
              <DashboardSummaryCards
                cards={sampleCards}
                layout="auto"
                showTrends={true}
                cardSize="sm"
              />
            </div>

            {/* Horizontal Layout */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تخطيط أفقي" : "Horizontal Layout"}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {language === "ar" ? "صف واحد من البطاقات" : "Single row of cards"}
              </p>
              <DashboardSummaryCards
                cards={sampleCards.slice(0, 4)}
                layout="horizontal"
                showTrends={false}
                cardSize="md"
              />
            </div>

            {/* Grid 2x3 Layout */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "شبكة 2×3" : "Grid 2×3 Layout"}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {language === "ar" ? "شبكة ثابتة من صفين وثلاثة أعمدة" : "Fixed grid of 2 rows and 3 columns"}
              </p>
              <DashboardSummaryCards
                cards={sampleCards}
                layout="grid-2x3"
                showTrends={true}
                cardSize="sm"
              />
            </div>
          </div>
        </div>

        {/* Interactive Features */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "الميزات التفاعلية" : "Interactive Features"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "البطاقات قابلة للنقر ويمكن إضافة معالجات الأحداث"
              : "Cards are clickable and can have event handlers attached"}
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {sampleCards.slice(0, 4).map((card) => (
              <SummaryCard
                key={`interactive-${card.id}`}
                {...card}
                size="sm"
                showTrends={true}
                onClick={() => alert(`Clicked: ${card.titleKey}`)}
              />
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              {language === "ar" ? "مثال على معالج النقر:" : "Click Handler Example:"}
            </h4>
            <CodeBlock code={`<SummaryCard
  id="interactive-card"
  titleKey="dashboard.card.activeTrips"
  value="1,234"
  icon="check"
  color="blue"
  onClick={() => {
    // Handle card click
    console.log('Card clicked!');
    // Navigate to details page
    router.push('/trips');
  }}
/>`} />
          </div>
        </div>
      </div>
    </div>
  );
}