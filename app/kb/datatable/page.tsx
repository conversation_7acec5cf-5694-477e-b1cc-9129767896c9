"use client";

import React, { useState } from "react";
import { DataTable } from "@/components/shared/DataTable";
import { useLanguage } from "@/hooks/useLanguage";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, Eye } from "lucide-react";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code }: CodeBlockProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
      <pre className="text-sm text-gray-100">
        <code>{code}</code>
      </pre>
    </div>
  );
}

interface ExampleSectionProps {
  title: string;
  description: string;
  code: string;
  children: React.ReactNode;
}

function ExampleSection({ title, description, code, children }: ExampleSectionProps) {
  const [showCode, setShowCode] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
      <div className="mb-4">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <button
          onClick={() => setShowCode(!showCode)}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          {showCode ? "Hide Code" : "Show Code"} {showCode ? "▲" : "▼"}
        </button>
      </div>
      
      {showCode && (
        <div className="mb-6">
          <CodeBlock code={code} />
        </div>
      )}
      
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Live Demo:</h4>
        {children}
      </div>
    </div>
  );
}

export default function DataTableKnowledgeBase() {
  const { language } = useLanguage();
  const isRTL = language === "ar";

  // Sample data for demonstrations
  const sampleUsers = [
    { id: 1, name: "أحمد محمد", email: "<EMAIL>", role: "مطور", status: "نشط", amount: 1250.50 },
    { id: 2, name: "فاطمة علي", email: "<EMAIL>", role: "مصممة", status: "نشط", amount: 980.25 },
    { id: 3, name: "محمد حسن", email: "<EMAIL>", role: "مدير", status: "غير نشط", amount: 2100.75 },
    { id: 4, name: "عائشة أحمد", email: "<EMAIL>", role: "محللة", status: "نشط", amount: 1500.00 },
    { id: 5, name: "يوسف محمود", email: "<EMAIL>", role: "مطور", status: "معلق", amount: 750.30 },
  ];

  const products = [
    { id: 1, name: "لابتوب Dell", category: "إلكترونيات", price: 15000, stock: 25, status: "متوفر" },
    { id: 2, name: "ماوس لاسلكي", category: "إكسسوارات", price: 150, stock: 100, status: "متوفر" },
    { id: 3, name: "شاشة Samsung", category: "إلكترونيات", price: 8000, stock: 0, status: "نفد المخزون" },
    { id: 4, name: "لوحة مفاتيح", category: "إكسسوارات", price: 300, stock: 50, status: "متوفر" },
  ];

  const orders = [
    { id: "ORD-001", customer: "أحمد محمد", total: 1250.50, status: "مكتمل", date: "2024-01-15" },
    { id: "ORD-002", customer: "فاطمة علي", total: 980.25, status: "قيد المعالجة", date: "2024-01-16" },
    { id: "ORD-003", customer: "محمد حسن", total: 2100.75, status: "ملغي", date: "2024-01-17" },
  ];

  // Column definitions
   const userColumns = [
     { key: "name", title: isRTL ? "الاسم" : "Name" },
     { key: "email", title: isRTL ? "البريد الإلكتروني" : "Email" },
     { key: "role", title: isRTL ? "الدور" : "Role" },
     { 
       key: "status", 
       title: isRTL ? "الحالة" : "Status", 
       render: (row: { status: string }) => (
          <Badge variant={row.status === "نشط" || row.status === "Active" ? "default" : "secondary"}>
            {row.status}
          </Badge>
        )
     },
     { 
       key: "amount", 
       title: isRTL ? "المبلغ" : "Amount", 
       render: (row: { amount: number }) => `$${row.amount.toFixed(2)}`
     },
   ];

  const productColumns = [
     { key: "name", title: isRTL ? "اسم المنتج" : "Product Name" },
     { key: "category", title: isRTL ? "الفئة" : "Category" },
     { 
       key: "price", 
       title: isRTL ? "السعر" : "Price", 
       render: (row: { price: number }) => `${row.price} ${isRTL ? "ريال" : "SAR"}`
     },
     { key: "stock", title: isRTL ? "المخزون" : "Stock" },
     { 
       key: "status", 
       title: isRTL ? "الحالة" : "Status",
       render: (row: { status: string }) => (
         <Badge variant={row.status === "متوفر" || row.status === "Available" ? "default" : "destructive"}>
           {row.status}
         </Badge>
       )
     },
   ];

  const actionColumns = [
     ...userColumns.slice(0, 4),
     {
       key: "actions",
       title: isRTL ? "الإجراءات" : "Actions",
       render: (row: { name: string }) => (
         <div className="flex gap-2">
           <Button size="sm" variant="outline" onClick={() => alert(`View ${row.name}`)}>
             <Eye className="h-4 w-4" />
           </Button>
           <Button size="sm" variant="outline" onClick={() => alert(`Edit ${row.name}`)}>
             <Edit className="h-4 w-4" />
           </Button>
           <Button size="sm" variant="destructive" onClick={() => alert(`Delete ${row.name}`)}>
             <Trash2 className="h-4 w-4" />
           </Button>
         </div>
       ),
     },
   ];

  const expandableColumns = [
     { key: "name", title: isRTL ? "الاسم" : "Name" },
     { key: "email", title: isRTL ? "البريد الإلكتروني" : "Email" },
     { key: "role", title: isRTL ? "الدور" : "Role" },
   ];

  const expandedRowRender = (row: { name: string; email: string; role: string; status: string; amount: number }) => (
    <div className="p-4 bg-gray-50 rounded">
      <h4 className="font-semibold mb-2">{isRTL ? "تفاصيل إضافية" : "Additional Details"}</h4>
       <div className="grid grid-cols-2 gap-4 text-sm">
         <div>
           <span className="font-medium">{isRTL ? "الحالة:" : "Status:"}</span> {row.status}
         </div>
         <div>
           <span className="font-medium">{isRTL ? "المبلغ:" : "Amount:"}</span> ${row.amount}
         </div>
        <div>
          <span className="font-medium">{isRTL ? "تاريخ الانضمام:" : "Join Date:"}</span> 2024-01-01
        </div>
        <div>
          <span className="font-medium">{isRTL ? "آخر نشاط:" : "Last Activity:"}</span> 2024-01-20
        </div>
      </div>
    </div>
  );

  // Code examples
  const basicExample = `import DataTable from "@/components/shared/DataTable";

function BasicExample() {
  const data = [
    { id: 1, name: "أحمد محمد", email: "<EMAIL>", role: "مطور" },
    { id: 2, name: "فاطمة علي", email: "<EMAIL>", role: "مصممة" },
  ];

  const columns = [
    { key: "name", label: "الاسم", sortable: true },
    { key: "email", label: "البريد الإلكتروني", sortable: true },
    { key: "role", label: "الدور", sortable: true },
  ];

  return (
    <DataTable
      data={data}
      columns={columns}
      searchable
      sortable
    />
  );
}`;

  const actionsExample = `import DataTable from "@/components/shared/DataTable";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, Eye } from "lucide-react";

function ActionsExample() {
  const columns = [
    { key: "name", label: "الاسم", sortable: true },
    { key: "email", label: "البريد الإلكتروني", sortable: true },
    {
      key: "actions",
      label: "الإجراءات",
      render: (_, row) => (
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Eye className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Edit className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="destructive">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      data={data}
      columns={columns}
      searchable
      sortable
    />
  );
}`;

  const expandableExample = `import DataTable from "@/components/shared/DataTable";

function ExpandableExample() {
  const expandedRowRender = (row) => (
    <div className="p-4 bg-gray-50 rounded">
      <h4 className="font-semibold mb-2">تفاصيل إضافية</h4>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>الحالة: \{row.status\}</div>
        <div>المبلغ: $\{row.amount\}</div>
      </div>
    </div>
  );

  return (
    <DataTable
      data={data}
      columns={columns}
      expandable
      expandedRowRender={expandedRowRender}
      searchable
      sortable
    />
  );
}`;

  const selectableExample = `import DataTable from "@/components/shared/DataTable";

function SelectableExample() {
  const [selectedRows, setSelectedRows] = useState([]);

  const handleSelectionChange = (selectedRowKeys) => {
    setSelectedRows(selectedRowKeys);
    console.log('Selected rows:', selectedRowKeys);
  };

  return (
    <DataTable
      data={data}
      columns={columns}
      selectable
      onSelectionChange={handleSelectionChange}
      searchable
      sortable
    />
  );
}`;

  const exportableExample = `import DataTable from "@/components/shared/DataTable";

function ExportableExample() {
  return (
    <DataTable
      data={data}
      columns={columns}
      exportable
      exportFilename="users-data"
      searchable
      sortable
    />
  );
}`;

  return (
    <div className="p-6">
      <div className="max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {isRTL ? "جدول البيانات" : "Data Table"}
          </h1>
          <p className="text-lg text-gray-600">
            {isRTL
              ? "دليل شامل لاستخدام مكون جدول البيانات مع جميع الخصائص والأمثلة التفاعلية"
              : "Comprehensive guide to using the DataTable component with all props and interactive examples"}
          </p>
        </div>

        {/* Props Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {isRTL ? "الخصائص المتاحة" : "Component Props"}
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Prop</th>
                  <th className="text-left py-2 px-4">Type</th>
                  <th className="text-left py-2 px-4">Default</th>
                  <th className="text-left py-2 px-4">Description</th>
                </tr>
              </thead>
              <tbody className="text-gray-600">
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">data</td>
                  <td className="py-2 px-4">Array&lt;Record&lt;string, any&gt;&gt;</td>
                  <td className="py-2 px-4">[]</td>
                  <td className="py-2 px-4">Array of data objects to display</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">columns</td>
                  <td className="py-2 px-4">ColumnDef[]</td>
                  <td className="py-2 px-4">[]</td>
                  <td className="py-2 px-4">Column definitions with key, label, sortable, render</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">searchable</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Enable search functionality</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">sortable</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Enable sorting functionality</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">selectable</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Enable row selection with checkboxes</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">expandable</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Enable row expansion</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">exportable</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Enable data export functionality</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">pageSize</td>
                  <td className="py-2 px-4">number</td>
                  <td className="py-2 px-4">10</td>
                  <td className="py-2 px-4">Number of rows per page</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">onSelectionChange</td>
                  <td className="py-2 px-4">(keys: string[]) =&gt; void</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Callback when selection changes</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">expandedRowRender</td>
                  <td className="py-2 px-4">(record: any) =&gt; ReactNode</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Function to render expanded row content</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Examples */}
        <ExampleSection
          title={isRTL ? "مثال أساسي" : "Basic Example"}
          description={isRTL ? "جدول بيانات بسيط مع البحث والترتيب" : "Simple data table with search and sorting"}
          code={basicExample}
        >
          <DataTable
             data={sampleUsers}
             columns={userColumns}
             keyField="id"
             searchable
             sortable
           />
        </ExampleSection>

        <ExampleSection
          title={isRTL ? "جدول مع الإجراءات" : "Table with Actions"}
          description={isRTL ? "جدول يحتوي على أزرار إجراءات لكل صف" : "Table with action buttons for each row"}
          code={actionsExample}
        >
          <DataTable
             data={sampleUsers}
             columns={actionColumns}
             keyField="id"
             searchable
             sortable
           />
        </ExampleSection>

        <ExampleSection
          title={isRTL ? "صفوف قابلة للتوسيع" : "Expandable Rows"}
          description={isRTL ? "جدول مع إمكانية توسيع الصفوف لإظهار تفاصيل إضافية" : "Table with expandable rows to show additional details"}
          code={expandableExample}
        >
          <DataTable
             data={sampleUsers}
             columns={expandableColumns}
             keyField="id"
             expandable
             expandedRowRender={expandedRowRender}
             searchable
             sortable
           />
        </ExampleSection>

        <ExampleSection
          title={isRTL ? "صفوف قابلة للتحديد" : "Selectable Rows"}
          description={isRTL ? "جدول مع إمكانية تحديد صفوف متعددة" : "Table with multi-row selection capability"}
          code={selectableExample}
        >
          <DataTable
             data={sampleUsers}
             columns={userColumns}
             keyField="id"
             selectable
             searchable
             sortable
          />
        </ExampleSection>

        <ExampleSection
          title={isRTL ? "ألوان الحالة" : "Status Colors"}
          description={isRTL ? "جدول مع ألوان مختلفة للحالات" : "Table with different colors for status indicators"}
          code={`// Using Badge component for status colors
const columns = [
  {
    key: "status",
    label: "Status",
    render: (value) => (
      <Badge variant={value === "Available" ? "default" : "destructive"}>
        {value}
      </Badge>
    )
  }
];`}
        >
          <DataTable
             data={products}
             columns={productColumns}
             keyField="id"
             searchable
             sortable
           />
        </ExampleSection>

        <ExampleSection
          title={isRTL ? "قابل للتصدير" : "Exportable Data"}
          description={isRTL ? "جدول مع إمكانية تصدير البيانات" : "Table with data export functionality"}
          code={exportableExample}
        >
          <DataTable
             data={orders}
             columns={[
               { key: "id", title: isRTL ? "رقم الطلب" : "Order ID" },
                { key: "customer", title: isRTL ? "العميل" : "Customer" },
                { 
                  key: "total", 
                  title: isRTL ? "المجموع" : "Total", 
                  render: (row: { total: number }) => `$${row.total.toFixed(2)}`
                },
                { key: "status", title: isRTL ? "الحالة" : "Status" },
                { key: "date", title: isRTL ? "التاريخ" : "Date" },
             ]}
             keyField="id"
             exportable
             exportFileNameKey="orders-data"
             searchable
             sortable
          />
        </ExampleSection>

        {/* Best Practices */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {isRTL ? "أفضل الممارسات" : "Best Practices"}
          </h2>
          <ul className="space-y-3 text-gray-600">
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "استخدم مفاتيح فريدة لكل عمود في تعريف الأعمدة"
                : "Use unique keys for each column in column definitions"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "قم بتوفير تسميات واضحة ومفهومة للأعمدة"
                : "Provide clear and descriptive labels for columns"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "استخدم دالة render لتخصيص عرض البيانات المعقدة"
                : "Use render function for custom display of complex data"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "فعّل البحث والترتيب للجداول الكبيرة"
                : "Enable search and sorting for large datasets"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "استخدم التحديد المتعدد بحذر لتجنب الإرباك"
                : "Use multi-selection carefully to avoid user confusion"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {isRTL
                ? "قم بتوفير ملاحظات بصرية واضحة للحالات المختلفة"
                : "Provide clear visual feedback for different states"}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}