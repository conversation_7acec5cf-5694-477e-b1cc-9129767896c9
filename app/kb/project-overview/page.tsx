"use client";

import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function ProjectOverviewPage() {
  const { language } = useLanguage();
  const isRTL = language === "ar";

  const content = {
    en: {
      title: "Project Overview",
      subtitle: "Trip Tracking System (TTS) - Technical Architecture",
      introduction: {
        title: "Introduction",
        description: "The Trip Tracking System (TTS) is a comprehensive logistics monitoring platform designed specifically for the Zakat, Tax and Customs Authority (ZATCA) to provide real-time tracking and management of cargo transportation across Saudi Arabia's border control points, airports, seaports, and land ports."
      },
      purpose: {
        title: "Primary Purpose",
        items: [
          "Monitor cross-border cargo movements in real-time",
          "Receive and manage critical alerts from tracking devices",
          "Ensure compliance with customs regulations and security protocols",
          "Track shipment progress through designated routes and checkpoints",
          "Coordinate responses to security breaches and operational incidents"
        ]
      },
      users: {
        title: "Target Users",
        items: [
          "ZATCA Customs Officers: Monitor assigned ports and routes",
          "ZATCA Supervisors: Oversee multiple locations and coordinate responses",
          "ZATCA Administrators: Manage system users and configure monitoring parameters",
          "System Administrators: Maintain technical infrastructure and integrations"
        ]
      },
      tech: {
        title: "Core Technologies",
        frontend: "Next.js 15.3.5, React 19+, TypeScript, Tailwind CSS",
        ui: "Shadcn/UI, Lucide React Icons",
        state: "React Hooks, Context API, Static JSON APIs",
        tools: "ESLint, Prettier, npm/yarn, Git"
      },
      hierarchy: {
        title: "Project Hierarchy & Component Structure",
        description: "The application follows a modular architecture with clear separation of concerns:"
      },
      pages: {
        title: "Application Pages",
        main: {
          title: "Main Dashboard Pages",
          items: [
            { path: "/dashboard", name: "Dashboard Overview", desc: "Comprehensive overview with statistics and alerts summary" },
            { path: "/location-monitor", name: "Location Monitor", desc: "Real-time monitoring dashboard for tracking active trips" },
            { path: "/focused-trips", name: "Focused Trips", desc: "Dedicated view for high-priority flagged shipments" },
            { path: "/my-assigned-ports", name: "My Assigned Ports", desc: "Personal workspace for assigned ports and routes" },
            { path: "/suspicious-trips", name: "Suspicious Trips", desc: "Security-focused view for flagged trips" },
            { path: "/reports", name: "Reports", desc: "Comprehensive reporting module with various report types" }
          ]
        },
        support: {
          title: "Support & Configuration Pages",
          items: [
            { path: "/configuration", name: "Configuration", desc: "System settings and user preferences" },
            { path: "/login", name: "Authentication", desc: "User login and authentication" },
            { path: "/trip-details", name: "Trip Details", desc: "Detailed trip information and management" }
          ]
        },
        kb: {
          title: "Knowledge Base (KB) Pages",
          items: [
            { path: "/kb/project-overview", name: "Project Overview", desc: "This page - project documentation and hierarchy" },
            { path: "/kb/datepicker", name: "Date Time Picker", desc: "Hijri and Gregorian date picker component documentation" },
            { path: "/kb/datatable", name: "Data Table", desc: "Reusable data table component with sorting and filtering" },
            { path: "/kb/clockcard", name: "Clock Card", desc: "Real-time clock component with timezone support" },
            { path: "/kb/summarycard", name: "Summary Card", desc: "Reusable summary card component for displaying metrics" }
          ]
        }
      },
      components: {
        title: "Component Architecture",
        ui: {
          title: "UI Components (/components/ui/)",
          desc: "Base UI components built on Shadcn/UI and Radix UI",
          items: ["button.tsx", "card.tsx", "table.tsx", "dropdown-menu.tsx", "badge.tsx", "tooltip.tsx"]
        },
        layout: {
          title: "Layout Components (/components/layout/)",
          desc: "Application layout and navigation components",
          items: ["Header.tsx", "Navbar.tsx", "Footer.tsx", "PageTemplate.tsx", "Breadcrumb.tsx", "LanguageSwitcher.tsx"]
        },
        shared: {
          title: "Shared Components (/components/shared/)",
          desc: "Reusable business logic components",
          items: ["SummaryCard.tsx", "ClockComponent.tsx", "DataTable.tsx", "DateTimePicker.tsx", "DashboardSummaryCards.tsx"]
        },
        features: {
          title: "Feature Components (/components/)",
          desc: "Specialized components for specific features",
          items: ["AlertListView.tsx", "TripAlertMapViewer.tsx", "TripFilter.tsx", "SearchPanel.tsx", "ViewToggle.tsx"]
        }
      }
    },
    ar: {
      title: "نظرة عامة على المشروع",
      subtitle: "نظام تتبع الرحلات (TTS) - الهيكل التقني",
      introduction: {
        title: "المقدمة",
        description: "نظام تتبع الرحلات (TTS) هو منصة مراقبة لوجستية شاملة مصممة خصيصاً لهيئة الزكاة والضريبة والجمارك (ZATCA) لتوفير تتبع وإدارة في الوقت الفعلي لنقل البضائع عبر نقاط المراقبة الحدودية والمطارات والموانئ البحرية والبرية في المملكة العربية السعودية."
      },
      purpose: {
        title: "الغرض الأساسي",
        items: [
          "مراقبة حركة البضائع عبر الحدود في الوقت الفعلي",
          "استقبال وإدارة التنبيهات الحرجة من أجهزة التتبع",
          "ضمان الامتثال للوائح الجمركية وبروتوكولات الأمان",
          "تتبع تقدم الشحنات عبر الطرق ونقاط التفتيش المحددة",
          "تنسيق الاستجابات للانتهاكات الأمنية والحوادث التشغيلية"
        ]
      },
      users: {
        title: "المستخدمون المستهدفون",
        items: [
          "ضباط الجمارك في ZATCA: مراقبة الموانئ والطرق المخصصة",
          "مشرفو ZATCA: الإشراف على مواقع متعددة وتنسيق الاستجابات",
          "مديرو ZATCA: إدارة مستخدمي النظام وتكوين معاملات المراقبة",
          "مديرو النظام: صيانة البنية التحتية التقنية والتكاملات"
        ]
      },
      tech: {
        title: "التقنيات الأساسية",
        frontend: "Next.js 15.3.5, React 19+, TypeScript, Tailwind CSS",
        ui: "Shadcn/UI, Lucide React Icons",
        state: "React Hooks, Context API, Static JSON APIs",
        tools: "ESLint, Prettier, npm/yarn, Git"
      },
      hierarchy: {
        title: "هيكل المشروع وبنية المكونات",
        description: "يتبع التطبيق هيكلاً معيارياً مع فصل واضح للاهتمامات:"
      },
      pages: {
        title: "صفحات التطبيق",
        main: {
          title: "صفحات لوحة التحكم الرئيسية",
          items: [
            { path: "/dashboard", name: "نظرة عامة على لوحة التحكم", desc: "نظرة عامة شاملة مع الإحصائيات وملخص التنبيهات" },
            { path: "/location-monitor", name: "مراقب الموقع", desc: "لوحة مراقبة في الوقت الفعلي لتتبع الرحلات النشطة" },
            { path: "/focused-trips", name: "الرحلات المركزة", desc: "عرض مخصص للشحنات عالية الأولوية المميزة" },
            { path: "/my-assigned-ports", name: "موانئي المخصصة", desc: "مساحة عمل شخصية للموانئ والطرق المخصصة" },
            { path: "/suspicious-trips", name: "الرحلات المشبوهة", desc: "عرض مركز على الأمان للرحلات المميزة" },
            { path: "/reports", name: "التقارير", desc: "وحدة تقارير شاملة مع أنواع تقارير متنوعة" }
          ]
        },
        support: {
          title: "صفحات الدعم والتكوين",
          items: [
            { path: "/configuration", name: "التكوين", desc: "إعدادات النظام وتفضيلات المستخدم" },
            { path: "/login", name: "المصادقة", desc: "تسجيل دخول المستخدم والمصادقة" },
            { path: "/trip-details", name: "تفاصيل الرحلة", desc: "معلومات مفصلة عن الرحلة وإدارتها" }
          ]
        },
        kb: {
          title: "صفحات قاعدة المعرفة (KB)",
          items: [
            { path: "/kb/project-overview", name: "نظرة عامة على المشروع", desc: "هذه الصفحة - توثيق المشروع والهيكل" },
            { path: "/kb/datepicker", name: "منتقي التاريخ والوقت", desc: "توثيق مكون منتقي التاريخ الهجري والميلادي" },
            { path: "/kb/datatable", name: "جدول البيانات", desc: "مكون جدول بيانات قابل لإعادة الاستخدام مع الفرز والتصفية" },
            { path: "/kb/clockcard", name: "بطاقة الساعة", desc: "مكون ساعة في الوقت الفعلي مع دعم المنطقة الزمنية" },
            { path: "/kb/summarycard", name: "بطاقة الملخص", desc: "مكون بطاقة ملخص قابل لإعادة الاستخدام لعرض المقاييس" }
          ]
        }
      },
      components: {
        title: "هيكل المكونات",
        ui: {
          title: "مكونات واجهة المستخدم (/components/ui/)",
          desc: "مكونات واجهة المستخدم الأساسية المبنية على Shadcn/UI و Radix UI",
          items: ["button.tsx", "card.tsx", "table.tsx", "dropdown-menu.tsx", "badge.tsx", "tooltip.tsx"]
        },
        layout: {
          title: "مكونات التخطيط (/components/layout/)",
          desc: "مكونات تخطيط التطبيق والتنقل",
          items: ["Header.tsx", "Navbar.tsx", "Footer.tsx", "PageTemplate.tsx", "Breadcrumb.tsx", "LanguageSwitcher.tsx"]
        },
        shared: {
          title: "المكونات المشتركة (/components/shared/)",
          desc: "مكونات منطق الأعمال القابلة لإعادة الاستخدام",
          items: ["SummaryCard.tsx", "ClockComponent.tsx", "DataTable.tsx", "DateTimePicker.tsx", "DashboardSummaryCards.tsx"]
        },
        features: {
          title: "مكونات الميزات (/components/)",
          desc: "مكونات متخصصة لميزات محددة",
          items: ["AlertListView.tsx", "TripAlertMapViewer.tsx", "TripFilter.tsx", "SearchPanel.tsx", "ViewToggle.tsx"]
        }
      }
    }
  };

  const t = content[language];

  return (
    <div className={`container mx-auto p-6 ${isRTL ? "text-right" : "text-left"}`}>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">{t.title}</h1>
          <p className="text-xl text-muted-foreground">{t.subtitle}</p>
        </div>

        {/* Introduction */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-4">{t.introduction.title}</h2>
          <p className="text-lg leading-relaxed">{t.introduction.description}</p>
        </Card>

        {/* Purpose */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-4">{t.purpose.title}</h2>
          <ul className="space-y-2">
            {t.purpose.items.map((item, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-primary mt-1">•</span>
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </Card>

        {/* Target Users */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-4">{t.users.title}</h2>
          <ul className="space-y-2">
            {t.users.items.map((item, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-primary mt-1">•</span>
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </Card>

        {/* Technologies */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-4">{t.tech.title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Frontend:</h3>
              <p className="text-sm">{t.tech.frontend}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">UI Framework:</h3>
              <p className="text-sm">{t.tech.ui}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">State Management:</h3>
              <p className="text-sm">{t.tech.state}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Development Tools:</h3>
              <p className="text-sm">{t.tech.tools}</p>
            </div>
          </div>
        </Card>

        {/* Project Hierarchy */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-4">{t.hierarchy.title}</h2>
          <p className="mb-6">{t.hierarchy.description}</p>

          {/* Application Pages */}
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">{t.pages.title}</h3>
              
              {/* Main Dashboard Pages */}
              <div className="mb-6">
                <h4 className="text-lg font-medium mb-3 text-primary">{t.pages.main.title}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {t.pages.main.items.map((page, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="secondary" className="text-xs">{page.path}</Badge>
                      </div>
                      <h5 className="font-medium">{page.name}</h5>
                      <p className="text-sm text-muted-foreground">{page.desc}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Support Pages */}
              <div className="mb-6">
                <h4 className="text-lg font-medium mb-3 text-primary">{t.pages.support.title}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {t.pages.support.items.map((page, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="secondary" className="text-xs">{page.path}</Badge>
                      </div>
                      <h5 className="font-medium">{page.name}</h5>
                      <p className="text-sm text-muted-foreground">{page.desc}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* KB Pages */}
              <div className="mb-6">
                <h4 className="text-lg font-medium mb-3 text-primary">{t.pages.kb.title}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {t.pages.kb.items.map((page, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">{page.path}</Badge>
                      </div>
                      <h5 className="font-medium">{page.name}</h5>
                      <p className="text-sm text-muted-foreground">{page.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Component Architecture */}
            <div>
              <h3 className="text-xl font-semibold mb-4">{t.components.title}</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* UI Components */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2 text-primary">{t.components.ui.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{t.components.ui.desc}</p>
                  <div className="flex flex-wrap gap-1">
                    {t.components.ui.items.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{item}</Badge>
                    ))}
                  </div>
                </div>

                {/* Layout Components */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2 text-primary">{t.components.layout.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{t.components.layout.desc}</p>
                  <div className="flex flex-wrap gap-1">
                    {t.components.layout.items.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{item}</Badge>
                    ))}
                  </div>
                </div>

                {/* Shared Components */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2 text-primary">{t.components.shared.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{t.components.shared.desc}</p>
                  <div className="flex flex-wrap gap-1">
                    {t.components.shared.items.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{item}</Badge>
                    ))}
                  </div>
                </div>

                {/* Feature Components */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2 text-primary">{t.components.features.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{t.components.features.desc}</p>
                  <div className="flex flex-wrap gap-1">
                    {t.components.features.items.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{item}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}