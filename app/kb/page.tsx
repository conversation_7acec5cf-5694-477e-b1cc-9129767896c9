"use client";

import React from "react";
import Link from "next/link";
import { useLanguage } from "@/contexts/LanguageContext";
import { Calendar, Table } from "lucide-react";

export default function KnowledgeBasePage() {
  const { language } = useLanguage();
  const isRTL = language === "ar";

  const kbItems = [
    {
      title: isRTL ? "منتقي التاريخ والوقت" : "Date Time Picker",
      href: "/kb/datepicker",
      icon: <Calendar size={20} />
    },
    {
      title: isRTL ? "جدول البيانات" : "Data Table",
      href: "/kb/datatable",
      icon: <Table size={20} />
    }
  ];

  return (
    <div className="p-8">
      <div className="max-w-4xl">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? "مرحباً بك في قاعدة المعرفة" : "Welcome to Knowledge Base"}
          </h2>
          <p className="text-gray-600 mb-6">
            {isRTL 
              ? "اختر مكوناً من الشريط الجانبي لعرض الوثائق والأمثلة التفاعلية المفصلة."
              : "Select a component from the sidebar to view detailed documentation and interactive examples."
            }
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {kbItems.map((item, index) => (
              <Link key={index} href={item.href}>
                <div className="border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
                  <div className={`flex items-center mb-3 ${
                    isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'
                  }`}>
                    <div className="text-blue-600">{item.icon}</div>
                    <h3 className="text-lg font-semibold text-gray-900">{item.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    {isRTL ? "انقر لعرض الوثائق" : "Click to view documentation"}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}