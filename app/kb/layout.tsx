"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import { useLanguage } from "@/contexts/LanguageContext";
import { ChevronRight, ChevronDown, Calendar, Table, Clock, BarChart3, Layers, FileText, List, Map, Info } from "lucide-react";

interface SidebarItemProps {
  title: string;
  href: string;
  icon: React.ReactNode;
  isActive?: boolean;
  isSubItem?: boolean;
}

function SidebarItem({ title, href, icon, isActive, isSubItem = false }: SidebarItemProps) {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  
  return (
    <Link href={href}>
      <div className={`flex items-center py-3 text-sm font-medium rounded-lg transition-colors ${
        isSubItem ? 'px-8' : 'px-4'
      } ${
        isActive 
          ? `bg-blue-100 text-blue-700 ${
              isRTL ? 'border-l-2 border-blue-700' : 'border-r-2 border-blue-700'
            }` 
          : 'text-gray-700 hover:bg-gray-100'
      }`}>
        <div className={isRTL ? 'ml-3' : 'mr-3'}>{icon}</div>
        <span className="flex-1">{title}</span>
        {!isSubItem && (
          <ChevronRight size={16} className={`text-gray-400 ${
            isRTL ? 'rotate-180' : ''
          }`} />
        )}
      </div>
    </Link>
  );
}

interface SidebarSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultExpanded?: boolean;
}

function SidebarSection({ title, icon, children, defaultExpanded = false }: SidebarSectionProps) {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  
  return (
    <div>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-100"
      >
        <div className={isRTL ? 'ml-3' : 'mr-3'}>{icon}</div>
        <span className="flex-1 text-left">{title}</span>
        {isExpanded ? (
          <ChevronDown size={16} className="text-gray-400" />
        ) : (
          <ChevronRight size={16} className={`text-gray-400 ${
            isRTL ? 'rotate-180' : ''
          }`} />
        )}
      </button>
      {isExpanded && (
        <div className="mt-1 space-y-1">
          {children}
        </div>
      )}
    </div>
  );
}

export default function KBLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  const pathname = usePathname();

  const sharedComponentItems = [
    {
      title: isRTL ? "منتقي التاريخ والوقت" : "Date Time Picker",
      href: "/kb/datepicker",
      icon: <Calendar size={18} />
    },
    {
      title: isRTL ? "جدول البيانات" : "Data Table",
      href: "/kb/datatable",
      icon: <Table size={18} />
    },
    {
      title: isRTL ? "بطاقة الساعة" : "Clock Card",
      href: "/kb/clockcard",
      icon: <Clock size={18} />
    },
    {
      title: isRTL ? "بطاقة الملخص" : "Summary Card",
      href: "/kb/summarycard",
      icon: <BarChart3 size={18} />
    }
  ];

  const projectComponentItems = [
    {
      title: isRTL ? "نظرة عامة على المشروع" : "Project Overview",
      href: "/kb/project-overview",
      icon: <FileText size={18} />
    },
    {
      title: isRTL ? "عرض قائمة الرحلات" : "TripListView",
      href: "/kb/triplistview",
      icon: <List size={18} />
    },
    {
      title: isRTL ? "عرض تفاصيل الرحلة الرئيسي" : "TripMasterDetailView",
      href: "/kb/tripmasterdetailview",
      icon: <Map size={18} />
    },
    {
      title: isRTL ? "ملخص تفاصيل الرحلة" : "TripDetailSummary",
      href: "/kb/tripdetailsummary",
      icon: <Info size={18} />
    }
  ];

  // Check if any shared component is active to expand the section by default
  const isSharedComponentActive = sharedComponentItems.some(item => pathname === item.href);
  const isProjectComponentActive = projectComponentItems.some(item => pathname === item.href);

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="flex">
        {/* Fixed Sidebar */}
        <div className={`w-64 bg-white shadow-sm min-h-screen fixed ${
          isRTL 
            ? 'right-0 border-l border-gray-200' 
            : 'left-0 border-r border-gray-200'
        }`}>
          <div className="p-6 border-b border-gray-200">
            <Link href="/kb">
              <h1 className="text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors cursor-pointer">
                {isRTL ? "قاعدة المعرفة" : "Knowledge Base"}
              </h1>
            </Link>
            <p className="text-sm text-gray-600 mt-1">
              {isRTL ? "مكونات النظام" : "System Components"}
            </p>
          </div>
          
          <nav className="p-4 space-y-2">
            <SidebarSection
              title={isRTL ? "مكونات المشروع" : "Project Components"}
              icon={<FileText size={20} />}
              defaultExpanded={isProjectComponentActive}
            >
              {projectComponentItems.map((item, index) => (
                <SidebarItem
                  key={`project-${index}`}
                  title={item.title}
                  href={item.href}
                  icon={item.icon}
                  isActive={pathname === item.href}
                  isSubItem={true}
                />
              ))}
            </SidebarSection>
            
            <SidebarSection
              title={isRTL ? "المكونات المشتركة" : "Shared Components"}
              icon={<Layers size={20} />}
              defaultExpanded={isSharedComponentActive}
            >
              {sharedComponentItems.map((item, index) => (
                <SidebarItem
                  key={`shared-${index}`}
                  title={item.title}
                  href={item.href}
                  icon={item.icon}
                  isActive={pathname === item.href}
                  isSubItem={true}
                />
              ))}
            </SidebarSection>
          </nav>
        </div>

        {/* Main Content with margin to account for fixed sidebar */}
        <div className={`flex-1 ${
          isRTL ? 'mr-64' : 'ml-64'
        }`}>
          {children}
        </div>
      </div>
    </div>
  );
}