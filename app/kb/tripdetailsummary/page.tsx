"use client";

import React, { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { TripDetailSummary } from "@/components/TripDetailSummary";
import { Trip } from "@/types/trip";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code, language = "typescript" }: CodeBlockProps) {
  return (
    <div className="relative">
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
        <code className={`language-${language}`}>{code}</code>
      </pre>
    </div>
  );
}

export default function TripDetailSummaryPage() {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  const [activeTab, setActiveTab] = useState("overview");

  const content = {
    en: {
      title: "TripDetailSummary Component",
      subtitle: "Comprehensive Trip Information Display with Navigation",
      tabs: {
        overview: "Overview",
        props: "Props & Usage",
        features: "Features & Layout",
        demo: "Live Demo"
      },
      overview: {
        title: "Component Overview",
        description: "TripDetailSummary is a React component that displays comprehensive trip information in a structured card layout. It presents trip details, vehicle information, driver data, shipment details, and provides navigation icons for related trip functions.",
        features: [
          "Structured display of trip information in a card layout",
          "Responsive grid layout adapting to different screen sizes",
          "Comprehensive trip data including codes, transit info, and tracking",
          "Vehicle and driver information display",
          "Shipment and security details",
          "Navigation icons for trip-related actions",
          "Alert details section with status indicators",
          "Graceful handling of missing or null data"
        ],
        location: "/components/TripDetailSummary.tsx",
        usedIn: "Used within TripMasterDetailView as the sidebar component"
      },
      props: {
        title: "Props & Interface",
        description: "TripDetailSummary accepts trip data and optional styling props.",
        interface: {
          title: "Props Interface",
          description: "The component accepts the following props:"
        },
        usage: {
          title: "Usage Examples",
          description: "Common usage patterns for the TripDetailSummary component:"
        },
        nullHandling: {
          title: "Null Data Handling",
          description: "The component gracefully handles missing or null trip data:",
          items: [
            "Displays 'Trip not found' message when trip prop is null or undefined",
            "Shows 'N/A' for missing or undefined nested properties",
            "Safely accesses nested object properties with optional chaining",
            "Provides fallback values for all displayed fields"
          ]
        }
      },
      features: {
        title: "Features & Layout Structure",
        description: "The component is organized into several key sections:",
        sections: [
          {
            name: "Header Section",
            description: "Card header with centered title 'Trip Details'",
            elements: ["CardHeader with centered title"]
          },
          {
            name: "Trip Information Grid",
            description: "Two-column responsive grid displaying key trip data",
            elements: [
              "Trip Code and Transit Number",
              "Elocks and Tracker information",
              "Distance metrics (complete and remaining)",
              "Arrival and End dates"
            ]
          },
          {
            name: "Detailed Information",
            description: "Full-width sections for longer content",
            elements: [
              "Owner description",
              "Vehicle details (plate, model, type, country)",
              "Driver information (name, nationality)",
              "Security notes",
              "Shipment description"
            ]
          },
          {
            name: "Alert Details Section",
            description: "Dedicated section for trip alerts",
            elements: [
              "Alert status header",
              "Alert information display",
              "'No Alerts' fallback message"
            ]
          },
          {
            name: "Navigation Icons",
            description: "Action buttons for trip-related functions",
            elements: [
              "Trip Map (🗺️)",
              "Trip Alerts (🔔)",
              "Movement Report (⚠️)",
              "Trip Pings (📊)",
              "Activities Report (✉️)"
            ]
          }
        ],
        styling: {
          title: "Styling & Responsive Design",
          description: "The component uses Tailwind CSS classes for styling:",
          features: [
            "Responsive grid layout (1 column on mobile, 2 columns on large screens)",
            "Consistent spacing and typography",
            "Border separators between information rows",
            "Hover effects on navigation icons",
            "Text truncation and word breaking for long content",
            "Color-coded elements (gray for labels, bold for values)"
          ]
        }
      },
      demo: {
        title: "Live Demo",
        description: "Interactive demonstration of the TripDetailSummary component with sample data:",
        note: "The component displays comprehensive trip information in a structured format."
      },
      relatedComponents: {
        title: "Related Components",
        description: "Components that work together with TripDetailSummary:",
        items: [
          {
            name: "TripMasterDetailView",
            link: "/kb/tripmasterdetailview",
            description: "Parent component that uses TripDetailSummary in the sidebar"
          },
          {
            name: "TripListView",
            link: "/kb/triplistview",
            description: "Main component that contains the expandable rows using TripMasterDetailView"
          }
        ]
      }
    },
    ar: {
      title: "مكون TripDetailSummary",
      subtitle: "عرض شامل لمعلومات الرحلة مع التنقل",
      tabs: {
        overview: "نظرة عامة",
        props: "الخصائص والاستخدام",
        features: "الميزات والتخطيط",
        demo: "عرض تفاعلي"
      },
      overview: {
        title: "نظرة عامة على المكون",
        description: "TripDetailSummary هو مكون React يعرض معلومات شاملة عن الرحلة في تخطيط بطاقة منظم. يقدم تفاصيل الرحلة ومعلومات المركبة وبيانات السائق وتفاصيل الشحنة ويوفر رموز التنقل للوظائف المتعلقة بالرحلة.",
        features: [
          "عرض منظم لمعلومات الرحلة في تخطيط بطاقة",
          "تخطيط شبكة متجاوب يتكيف مع أحجام الشاشات المختلفة",
          "بيانات شاملة للرحلة بما في ذلك الرموز ومعلومات العبور والتتبع",
          "عرض معلومات المركبة والسائق",
          "تفاصيل الشحنة والأمان",
          "رموز التنقل للإجراءات المتعلقة بالرحلة",
          "قسم تفاصيل التنبيهات مع مؤشرات الحالة",
          "التعامل السلس مع البيانات المفقودة أو الفارغة"
        ],
        location: "/components/TripDetailSummary.tsx",
        usedIn: "يُستخدم داخل TripMasterDetailView كمكون الشريط الجانبي"
      },
      props: {
        title: "الخصائص والواجهة",
        description: "يقبل TripDetailSummary بيانات الرحلة وخصائص التصميم الاختيارية.",
        interface: {
          title: "واجهة الخصائص",
          description: "يقبل المكون الخصائص التالية:"
        },
        usage: {
          title: "أمثلة الاستخدام",
          description: "أنماط الاستخدام الشائعة لمكون TripDetailSummary:"
        },
        nullHandling: {
          title: "التعامل مع البيانات الفارغة",
          description: "يتعامل المكون بسلاسة مع بيانات الرحلة المفقودة أو الفارغة:",
          items: [
            "يعرض رسالة 'الرحلة غير موجودة' عندما تكون خاصية trip فارغة أو غير محددة",
            "يظهر 'غير متوفر' للخصائص المتداخلة المفقودة أو غير المحددة",
            "يصل بأمان إلى خصائص الكائن المتداخل مع التسلسل الاختياري",
            "يوفر قيم احتياطية لجميع الحقول المعروضة"
          ]
        }
      },
      features: {
        title: "الميزات وهيكل التخطيط",
        description: "ينظم المكون في عدة أقسام رئيسية:",
        sections: [
          {
            name: "قسم الرأس",
            description: "رأس البطاقة مع العنوان المتوسط 'تفاصيل الرحلة'",
            elements: ["CardHeader مع عنوان متوسط"]
          },
          {
            name: "شبكة معلومات الرحلة",
            description: "شبكة متجاوبة من عمودين تعرض بيانات الرحلة الرئيسية",
            elements: [
              "رمز الرحلة ورقم العبور",
              "معلومات الأقفال الإلكترونية والمتتبع",
              "مقاييس المسافة (المكتملة والمتبقية)",
              "تواريخ الوصول والانتهاء"
            ]
          },
          {
            name: "المعلومات المفصلة",
            description: "أقسام بعرض كامل للمحتوى الأطول",
            elements: [
              "وصف المالك",
              "تفاصيل المركبة (اللوحة، الطراز، النوع، البلد)",
              "معلومات السائق (الاسم، الجنسية)",
              "ملاحظات الأمان",
              "وصف الشحنة"
            ]
          },
          {
            name: "قسم تفاصيل التنبيهات",
            description: "قسم مخصص لتنبيهات الرحلة",
            elements: [
              "رأس حالة التنبيه",
              "عرض معلومات التنبيه",
              "رسالة احتياطية 'لا توجد تنبيهات'"
            ]
          },
          {
            name: "رموز التنقل",
            description: "أزرار الإجراءات للوظائف المتعلقة بالرحلة",
            elements: [
              "خريطة الرحلة (🗺️)",
              "تنبيهات الرحلة (🔔)",
              "تقرير الحركة (⚠️)",
              "إشارات الرحلة (📊)",
              "تقرير الأنشطة (✉️)"
            ]
          }
        ],
        styling: {
          title: "التصميم والتصميم المتجاوب",
          description: "يستخدم المكون فئات Tailwind CSS للتصميم:",
          features: [
            "تخطيط شبكة متجاوب (عمود واحد على الهاتف المحمول، عمودان على الشاشات الكبيرة)",
            "تباعد وطباعة متسقة",
            "فواصل حدود بين صفوف المعلومات",
            "تأثيرات التمرير على رموز التنقل",
            "اقتطاع النص وكسر الكلمات للمحتوى الطويل",
            "عناصر مرمزة بالألوان (رمادي للتسميات، غامق للقيم)"
          ]
        }
      },
      demo: {
        title: "عرض تفاعلي",
        description: "عرض تفاعلي لمكون TripDetailSummary مع بيانات عينة:",
        note: "يعرض المكون معلومات شاملة عن الرحلة في تنسيق منظم."
      },
      relatedComponents: {
        title: "المكونات ذات الصلة",
        description: "المكونات التي تعمل مع TripDetailSummary:",
        items: [
          {
            name: "TripMasterDetailView",
            link: "/kb/tripmasterdetailview",
            description: "المكون الأصل الذي يستخدم TripDetailSummary في الشريط الجانبي"
          },
          {
            name: "TripListView",
            link: "/kb/triplistview",
            description: "المكون الرئيسي الذي يحتوي على الصفوف القابلة للتوسيع باستخدام TripMasterDetailView"
          }
        ]
      }
    }
  };

  const t = content[language];

  const propsInterfaceCode = `interface TripDetailSummaryProps {
  trip?: Trip | null;
  className?: string;
}

// Usage with the component
<TripDetailSummary 
  trip={tripData} 
  className="custom-styling" 
/>`;

  const usageExamplesCode = `import { TripDetailSummary } from "@/components/TripDetailSummary";

// Basic usage
function TripSidebar({ trip }: { trip: Trip }) {
  return (
    <div className="sidebar">
      <TripDetailSummary trip={trip} />
    </div>
  );
}

// Usage within TripMasterDetailView
function TripMasterDetailView({ row }: { row: Trip }) {
  const { tripData, loading, error } = useTripData(row.tripId);
  
  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-3">
        <TripDetailSummary trip={tripData} />
      </div>
      {/* Map component */}
    </div>
  );
}

// With custom styling
<TripDetailSummary 
  trip={tripData} 
  className="shadow-lg border-2" 
/>`;

  const navigationConfigCode = `// Navigation icons configuration
const navigationIcons = [
  { 
    id: 'trip_map', 
    icon: '🗺️', 
    title: 'Trip Map',
  },
  { 
    id: 'trip_alerts', 
    icon: '🔔', 
    title: 'Trip Alerts',
  },
  { 
    id: 'movement_report', 
    icon: '⚠️', 
    title: 'Movement Report',
  },
  { 
    id: 'trip_pings', 
    icon: '📊', 
    title: 'Trip Pings',
  },
  { 
    id: 'trip_activities_report', 
    icon: '✉️', 
    title: 'Activities Report',
  }
];

// Navigation links generation
{navigationIcons.map((nav) => (
  <Link
    key={nav.id}
    href={\`/trip-details?trip_id=\${trip.tripId}&current_tab=\${nav.id}\`}
    className="w-8 h-8 rounded-md flex items-center justify-center text-white text-sm hover:opacity-80 transition-opacity bg-blue-500"
    title={nav.title}
  >
    {nav.icon}
  </Link>
))}`;

  // Sample trip data for demo
  const sampleTrip = {
    tripId: "TRP-2024-001",
    transitNumber: 12345,
    expectedArrivalDate: "2024-01-16",
    endDate: "2024-01-20",
    tracking: {
      elocks: "2",
      completeDistance: "950",
      remainingDistance: "200"
    },
    vehicle_details: {
      vehicleId: "VH-001",
      vehiclePlateNumber: "ABC-123",
      model: "Heavy Truck",
      type: "Freight",
      plateCountry: "SA",
      trackerNo: "TRK-001"
    },
    driver_details: {
      driverId: "DR-001",
      driverName: "Ahmed Al-Rashid",
      driverNationality: "Saudi",
      driverPassportNumber: "P123456789"
    },
    shipment: {
      shipmentId: "SH-001",
      ownerDescription: "Saudi Electronics Corp",
      shipmentDescription: "Consumer Electronics - Smartphones and Tablets"
    },
    compliance: {
      customsStatus: "cleared",
      documentStatus: "complete",
      securityNotes: "High-value shipment with enhanced security protocols"
    }
  };

  return (
    <div className={`container mx-auto p-6 ${isRTL ? "text-right" : "text-left"}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">{t.title}</h1>
          <p className="text-xl text-muted-foreground">{t.subtitle}</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 border-b">
          {Object.entries(t.tabs).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === key
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              {label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === "overview" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.overview.title}</h2>
                <p className="text-lg mb-4">{t.overview.description}</p>
                <div className="mb-4 space-x-2">
                  <Badge variant="outline">{t.overview.location}</Badge>
                  <Badge variant="secondary">{t.overview.usedIn}</Badge>
                </div>
                <h3 className="text-lg font-semibold mb-3">Key Features:</h3>
                <ul className="space-y-2">
                  {t.overview.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </Card>

              {/* Related Components */}
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.relatedComponents.title}</h2>
                <p className="mb-4">{t.relatedComponents.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {t.relatedComponents.items.map((item, index) => (
                    <Link key={index} href={item.link} className="block">
                      <div className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                        <h3 className="font-semibold text-primary mb-2">{item.name}</h3>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              </Card>
            </>
          )}

          {activeTab === "props" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.props.title}</h2>
                <p className="mb-6">{t.props.description}</p>
                
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t.props.interface.title}</h3>
                  <p className="mb-4">{t.props.interface.description}</p>
                  <CodeBlock code={propsInterfaceCode} />
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t.props.usage.title}</h3>
                  <p className="mb-4">{t.props.usage.description}</p>
                  <CodeBlock code={usageExamplesCode} />
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">{t.props.nullHandling.title}</h3>
                  <p className="mb-3">{t.props.nullHandling.description}</p>
                  <ul className="space-y-2">
                    {t.props.nullHandling.items.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            </>
          )}

          {activeTab === "features" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.features.title}</h2>
                <p className="mb-6">{t.features.description}</p>
                
                <div className="space-y-6">
                  {t.features.sections.map((section, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h3 className="text-lg font-semibold mb-2">{section.name}</h3>
                      <p className="text-muted-foreground mb-3">{section.description}</p>
                      <div>
                        <h4 className="font-medium mb-2">Elements:</h4>
                        <ul className="space-y-1">
                          {section.elements.map((element, elementIndex) => (
                            <li key={elementIndex} className="text-sm bg-muted p-2 rounded">
                              {element}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">{t.features.styling.title}</h3>
                  <p className="mb-3">{t.features.styling.description}</p>
                  <ul className="space-y-2">
                    {t.features.styling.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary mt-1">•</span>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">Navigation Configuration:</h3>
                  <CodeBlock code={navigationConfigCode} />
                </div>
              </Card>
            </>
          )}

          {activeTab === "demo" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.demo.title}</h2>
                <p className="mb-4">{t.demo.description}</p>
                <p className="text-sm text-muted-foreground mb-6">{t.demo.note}</p>
                <div className="border rounded-lg p-4 bg-muted/50">
                  <h3 className="font-semibold mb-3">Sample Trip Detail Summary:</h3>
                  <div className="max-w-md mx-auto">
                    <TripDetailSummary trip={sampleTrip as any} />
                  </div>
                </div>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  );
}