"use client";

import React, { useState } from "react";
import DateTimePicker, { HijriDate } from "@/components/shared/DateTimePicker";
import { useLanguage } from "@/contexts/LanguageContext";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code }: CodeBlockProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
      <pre className="text-sm text-gray-100">
        <code>{code}</code>
      </pre>
    </div>
  );
}

interface ExampleSectionProps {
  title: string;
  description: string;
  code: string;
  children: React.ReactNode;
}

function ExampleSection({ title, description, code, children }: ExampleSectionProps) {
  const [showCode, setShowCode] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
      <div className="mb-4">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <button
          onClick={() => setShowCode(!showCode)}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          {showCode ? "Hide Code" : "Show Code"} {showCode ? "▲" : "▼"}
        </button>
      </div>
      
      {showCode && (
        <div className="mb-6">
          <CodeBlock code={code} />
        </div>
      )}
      
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Live Demo:</h4>
        {children}
      </div>
    </div>
  );
}

export default function DatePickerKnowledgeBase() {
  const { language } = useLanguage();
  const [basicDate, setBasicDate] = useState<Date | undefined>(undefined);
  const [hijriDate, setHijriDate] = useState<Date | undefined>(undefined);
  const [timeDate, setTimeDate] = useState<Date | undefined>(undefined);
  const [switchableDate, setSwitchableDate] = useState<Date | undefined>(undefined);
  const [formattedDate, setFormattedDate] = useState<string>("");
  const [formatDate1, setFormatDate1] = useState<Date | undefined>(undefined);
  const [formatDate2, setFormatDate2] = useState<Date | undefined>(undefined);
  const [formatDate3, setFormatDate3] = useState<Date | undefined>(undefined);
  const [formatDate4, setFormatDate4] = useState<Date | undefined>(undefined);
  const [formatDate5, setFormatDate5] = useState<Date | undefined>(undefined);
  const [formatDate6, setFormatDate6] = useState<Date | undefined>(undefined);

  const basicExample = `import DateTimePicker from "@/components/shared/DateTimePicker";

function BasicExample() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  return (
    <DateTimePicker
      label="Select Date"
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="gregorian"
    />
  );
}`;

  const hijriExample = `import DateTimePicker from "@/components/shared/DateTimePicker";

function HijriExample() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  return (
    <DateTimePicker
      label="اختر التاريخ الهجري"
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="hijri"
      placeholder="5/8/1445"
    />
  );
}`;

  const timeExample = `import DateTimePicker from "@/components/shared/DateTimePicker";

function TimeExample() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  return (
    <DateTimePicker
      label="Select Date & Time"
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="gregorian"
      showTime={true}
      timeFormat="12h"
    />
  );
}`;

  const switchableExample = `import DateTimePicker from "@/components/shared/DateTimePicker";

function SwitchableExample() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  return (
    <DateTimePicker
      label="Switchable Calendar"
      value={selectedDate}
      onChange={setSelectedDate}
      dateType="hijri"
      allowTypeSwitch={true}
      showSelectedDate={true}
    />
  );
}`;

  const advancedExample = `import DateTimePicker from "@/components/shared/DateTimePicker";

function AdvancedExample() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [formattedDate, setFormattedDate] = useState<string>("");

  return (
    <DateTimePicker
      title="Enhanced Date Picker"
      value={selectedDate}
      onChange={setSelectedDate}
      onFormattedDateChange={setFormattedDate}
      dateType="hijri"
      showSelectedDate={true}
      placeholder="Type date: 5/8/1445"
      className="custom-datepicker"
    />
  );
}`;

  return (
    <div className="p-8">
      <div className="max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {language === "ar" ? "منتقي التاريخ والوقت" : "Date Time Picker"}
          </h1>
          <p className="text-xl text-gray-600">
            {language === "ar"
              ? "دليل شامل لاستخدام منتقي التاريخ الهجري والميلادي مع جميع الخصائص والأمثلة"
              : "Comprehensive guide to using Hijri and Gregorian date pickers with all props and examples"}
          </p>
        </div>

        {/* Props Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "الخصائص المتاحة" : "Available Props"}
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Prop</th>
                  <th className="text-left py-2 px-4">Type</th>
                  <th className="text-left py-2 px-4">Default</th>
                  <th className="text-left py-2 px-4">Description</th>
                </tr>
              </thead>
              <tbody className="text-gray-600">
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">value</td>
                  <td className="py-2 px-4">Date | null</td>
                  <td className="py-2 px-4">null</td>
                  <td className="py-2 px-4">Selected date value</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">onChange</td>
                  <td className="py-2 px-4">(date: Date) =&gt; void</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Callback when date changes</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">dateType</td>
                  <td className="py-2 px-4">&quot;hijri&quot; | &quot;gregorian&quot;</td>
                  <td className="py-2 px-4">&quot;gregorian&quot;</td>
                  <td className="py-2 px-4">Calendar type</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">format</td>
                  <td className="py-2 px-4">&quot;dd/mm/yyyy&quot; | &quot;mm/dd/yyyy&quot; | &quot;yyyy-mm-dd&quot; | &quot;dd-mm-yyyy&quot; | &quot;short&quot; | &quot;full&quot;</td>
                  <td className="py-2 px-4">&quot;dd/mm/yyyy&quot;</td>
                  <td className="py-2 px-4">Date display format</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">showTime</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Show time picker</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">allowTypeSwitch</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Allow switching between calendar types</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">showSelectedDate</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Display selected date below picker</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">placeholder</td>
                  <td className="py-2 px-4">string</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Input placeholder text</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">onFormattedDateChange</td>
                  <td className="py-2 px-4">(formatted: string) =&gt; void</td>
                  <td className="py-2 px-4">-</td>
                  <td className="py-2 px-4">Callback for formatted date string</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4 font-mono">showActionButtons</td>
                  <td className="py-2 px-4">boolean</td>
                  <td className="py-2 px-4">false</td>
                  <td className="py-2 px-4">Show cancel and confirm buttons</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Examples */}
        <ExampleSection
          title={language === "ar" ? "مثال أساسي - التقويم الميلادي" : "Basic Example - Gregorian Calendar"}
          description={language === "ar" 
            ? "منتقي تاريخ ميلادي بسيط مع الخصائص الأساسية"
            : "Simple Gregorian date picker with basic props"}
          code={basicExample}
        >
          <DateTimePicker
            label={language === "ar" ? "اختر التاريخ" : "Select Date"}
            value={basicDate}
            onChange={setBasicDate}
            dateType="gregorian"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "منتقي التاريخ الهجري" : "Hijri Date Picker"}
          description={language === "ar"
            ? "منتقي تاريخ هجري مع إمكانية الكتابة المباشرة"
            : "Hijri date picker with direct typing capability"}
          code={hijriExample}
        >
          <DateTimePicker
            label={language === "ar" ? "اختر التاريخ الهجري" : "Select Hijri Date"}
            value={hijriDate}
            onChange={setHijriDate}
            dateType="hijri"
            placeholder={language === "ar" ? "5/8/1445" : "5/8/1445"}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "منتقي التاريخ والوقت" : "Date & Time Picker"}
          description={language === "ar"
            ? "منتقي تاريخ ووقت مع تنسيق 12 ساعة"
            : "Date and time picker with 12-hour format"}
          code={timeExample}
        >
          <DateTimePicker
            label={language === "ar" ? "اختر التاريخ والوقت" : "Select Date & Time"}
            value={timeDate}
            onChange={setTimeDate}
            dateType="gregorian"
            showTime={true}
            timeFormat="12h"
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "منتقي قابل للتبديل" : "Switchable Calendar"}
          description={language === "ar"
            ? "منتقي يمكن التبديل بين الهجري والميلادي"
            : "Picker that can switch between Hijri and Gregorian calendars"}
          code={switchableExample}
        >
          <DateTimePicker
            label={language === "ar" ? "تقويم قابل للتبديل" : "Switchable Calendar"}
            value={switchableDate}
            onChange={setSwitchableDate}
            dateType="hijri"
            allowTypeSwitch={true}
            showSelectedDate={true}
          />
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "مثال متقدم" : "Advanced Example"}
          description={language === "ar"
            ? "منتقي متقدم مع جميع الخصائص وإدارة التنسيق"
            : "Advanced picker with all features and format handling"}
          code={advancedExample}
        >
          <DateTimePicker
            title={language === "ar" ? "منتقي التاريخ المحسن" : "Enhanced Date Picker"}
            value={switchableDate}
            onChange={setSwitchableDate}
            onFormattedDateChange={setFormattedDate}
            dateType="hijri"
            showSelectedDate={true}
            placeholder={language === "ar" ? "اكتب التاريخ: 5/8/1445" : "Type date: 5/8/1445"}
          />
          {formattedDate && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-blue-800">
                <strong>{language === "ar" ? "التاريخ المنسق:" : "Formatted Date:"}</strong> {formattedDate}
              </p>
            </div>
          )}
        </ExampleSection>

        <ExampleSection
          title={language === "ar" ? "مع أزرار التأكيد" : "With Action Buttons"}
          description={language === "ar"
            ? "منتقي تاريخ مع أزرار الإلغاء والتأكيد"
            : "Date picker with cancel and confirm buttons"}
          code={`<DateTimePicker
  label="${language === "ar" ? "اختر التاريخ" : "Select Date"}"
  value={noButtonsDate}
  onChange={setNoButtonsDate}
  dateType="gregorian"
  showActionButtons={true}
/>`}
        >
          <DateTimePicker
            label={language === "ar" ? "اختر التاريخ" : "Select Date"}
            value={basicDate}
            onChange={setBasicDate}
            dateType="gregorian"
            showActionButtons={true}
          />
        </ExampleSection>

        {/* Format Options */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "خيارات التنسيق" : "Format Options"}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === "ar"
              ? "يدعم منتقي التاريخ عدة تنسيقات مختلفة لعرض التاريخ"
              : "The DateTimePicker supports various formats for displaying dates"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق dd/mm/yyyy" : "dd/mm/yyyy Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "يوم/شهر/سنة" : "Day/Month/Year"}
                value={formatDate1}
                onChange={setFormatDate1}
                dateType="gregorian"
                format="dd/mm/yyyy"
                className="w-full"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق mm/dd/yyyy" : "mm/dd/yyyy Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "شهر/يوم/سنة" : "Month/Day/Year"}
                value={formatDate2}
                onChange={setFormatDate2}
                dateType="gregorian"
                format="mm/dd/yyyy"
                className="w-full"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق yyyy-mm-dd" : "yyyy-mm-dd Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "سنة-شهر-يوم" : "Year-Month-Day"}
                value={formatDate3}
                onChange={setFormatDate3}
                dateType="gregorian"
                format="yyyy-mm-dd"
                className="w-full"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق dd-mm-yyyy" : "dd-mm-yyyy Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "يوم-شهر-سنة" : "Day-Month-Year"}
                value={formatDate4}
                onChange={setFormatDate4}
                dateType="gregorian"
                format="dd-mm-yyyy"
                className="w-full"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق مختصر" : "Short Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "تنسيق مختصر" : "Short Format"}
                value={formatDate5}
                onChange={setFormatDate5}
                dateType="gregorian"
                format="short"
                className="w-full"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                {language === "ar" ? "تنسيق كامل" : "Full Format"}
              </h3>
              <DateTimePicker
                label={language === "ar" ? "تنسيق كامل" : "Full Format"}
                value={formatDate6}
                onChange={setFormatDate6}
                dateType="gregorian"
                format="full"
                className="w-full"
              />
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              {language === "ar" ? "التنسيقات المتاحة:" : "Available Formats:"}
            </h4>
            <ul className="text-blue-800 text-sm space-y-1">
              <li><code className="bg-blue-100 px-2 py-1 rounded">dd/mm/yyyy</code> - {language === "ar" ? "يوم/شهر/سنة" : "Day/Month/Year"}</li>
              <li><code className="bg-blue-100 px-2 py-1 rounded">mm/dd/yyyy</code> - {language === "ar" ? "شهر/يوم/سنة" : "Month/Day/Year"}</li>
              <li><code className="bg-blue-100 px-2 py-1 rounded">yyyy-mm-dd</code> - {language === "ar" ? "سنة-شهر-يوم" : "Year-Month-Day"}</li>
              <li><code className="bg-blue-100 px-2 py-1 rounded">dd-mm-yyyy</code> - {language === "ar" ? "يوم-شهر-سنة" : "Day-Month-Year"}</li>
              <li><code className="bg-blue-100 px-2 py-1 rounded">short</code> - {language === "ar" ? "تنسيق مختصر" : "Short format"}</li>
              <li><code className="bg-blue-100 px-2 py-1 rounded">full</code> - {language === "ar" ? "تنسيق كامل مع اسم الشهر" : "Full format with month name"}</li>
            </ul>
          </div>
        </div>

        {/* Hijri Date Utilities */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "أدوات التاريخ الهجري" : "Hijri Date Utilities"}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                {language === "ar" ? "التاريخ الهجري الحالي" : "Current Hijri Date"}
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="font-mono text-lg">
                  {HijriDate.getCurrentHijriDateString(language as "ar" | "en")}
                </p>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                {language === "ar" ? "التاريخ الميلادي الحالي" : "Current Gregorian Date"}
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="font-mono text-lg">
                  {new Date().toLocaleDateString(language === "ar" ? "ar-SA" : "en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long"
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Best Practices */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            {language === "ar" ? "أفضل الممارسات" : "Best Practices"}
          </h2>
          <ul className="space-y-3 text-gray-600">
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "استخدم التقويم الهجري للتطبيقات الإسلامية والرسمية"
                : "Use Hijri calendar for Islamic and official applications"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "قم بتوفير نص توضيحي واضح للمستخدمين"
                : "Provide clear placeholder text for users"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "استخدم خاصية allowTypeSwitch عند الحاجة للمرونة"
                : "Use allowTypeSwitch when flexibility is needed"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "تعامل مع تغييرات التاريخ بشكل صحيح في onChange"
                : "Handle date changes properly in onChange callback"}
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-2">✓</span>
              {language === "ar"
                ? "اختبر التحويلات بين التقويمين للتأكد من الدقة"
                : "Test conversions between calendars for accuracy"}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}