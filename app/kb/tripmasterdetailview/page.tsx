"use client";

import React, { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import TripMasterDetailView from "@/components/TripMasterDetailView";

interface CodeBlockProps {
  code: string;
  language?: string;
}

function CodeBlock({ code, language = "typescript" }: CodeBlockProps) {
  return (
    <div className="relative">
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
        <code className={`language-${language}`}>{code}</code>
      </pre>
    </div>
  );
}

export default function TripMasterDetailViewPage() {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  const [activeTab, setActiveTab] = useState("overview");

  const content = {
    en: {
      title: "TripMasterDetailView Component",
      subtitle: "Expandable Row Content with Trip Details and Map Visualization",
      tabs: {
        overview: "Overview",
        props: "Props & Usage",
        nested: "Nested Components",
        demo: "Live Demo"
      },
      overview: {
        title: "Component Overview",
        description: "TripMasterDetailView is a React component that renders the expanded row content for trip data tables. It provides a comprehensive view combining detailed trip information in a sidebar with an interactive map visualization showing trip routes and alerts.",
        features: [
          "Grid-based responsive layout (3-column sidebar + 9-column map)",
          "Integration with TripDetailSummary for comprehensive trip information",
          "Interactive map visualization using TripAlertMapViewer",
          "Real-time data fetching with loading and error states",
          "Responsive design adapting to different screen sizes",
          "Seamless integration with DataTable expandable rows"
        ],
        location: "/components/TripMasterDetailView.tsx",
        usedIn: "Primarily used as expandedRowRender in TripListView component"
      },
      props: {
        title: "Props & Interface",
        description: "TripMasterDetailView accepts a single prop containing the trip data object.",
        interface: {
          title: "Props Interface",
          description: "The component expects a Trip object with comprehensive trip information:"
        },
        usage: {
          title: "Usage Example",
          description: "Typically used within DataTable's expandedRowRender function:"
        },
        states: {
          title: "Component States",
          description: "The component handles three main states:",
          items: [
            "Loading: Shows loading indicator while fetching trip data",
            "Error: Displays error message if data fetching fails",
            "Success: Renders the master-detail view with trip information"
          ]
        }
      },
      nested: {
        title: "Nested Components & Dependencies",
        description: "TripMasterDetailView orchestrates several specialized components:",
        components: [
          {
            name: "TripDetailSummary",
            path: "/components/TripDetailSummary.tsx",
            kbLink: "/kb/tripdetailsummary",
            description: "Displays comprehensive trip details in the left sidebar including trip code, transit info, vehicle details, driver information, and navigation icons",
            props: [
              "trip: Trip | null - Complete trip data object",
              "className?: string - Optional CSS classes for styling"
            ]
          },
          {
            name: "TripAlertMapViewer",
            path: "/components/TripAlertMapViewer.tsx",
            description: "Interactive map component showing trip route, alert locations, and real-time tracking information",
            props: [
              "tripAlerts: TripAlert[] - Array of trip alerts to display",
              "selectedTripId: string - ID of the currently selected trip",
              "className: string - CSS classes including height specification"
            ]
          }
        ],
        hooks: {
          title: "Custom Hooks",
          description: "The component utilizes custom hooks for data management:",
          items: [
            "useTripData(tripId): Fetches detailed trip information",
            "Returns: { tripData, loading, error } for state management"
          ]
        },
        dataFlow: {
          title: "Data Flow",
          description: "Data flows through the component as follows:",
          steps: [
            "Receives Trip object via row prop",
            "Extracts tripId and calls useTripData hook",
            "Loads trip alerts from static JSON data",
            "Passes data to child components for rendering",
            "Handles loading and error states gracefully"
          ]
        }
      },
      demo: {
        title: "Live Demo",
        description: "Interactive demonstration requires a Trip object. This component is typically used within TripListView's expandable rows.",
        note: "For a complete demo, visit the TripListView component page where this component is used in context."
      },
      relatedComponents: {
        title: "Related Components",
        description: "Components that work together with TripMasterDetailView:",
        items: [
          {
            name: "TripListView",
            link: "/kb/triplistview",
            description: "Parent component that uses TripMasterDetailView for expandable rows"
          },
          {
            name: "TripDetailSummary",
            link: "/kb/tripdetailsummary",
            description: "Child component displaying detailed trip information"
          }
        ]
      }
    },
    ar: {
      title: "مكون TripMasterDetailView",
      subtitle: "محتوى الصف القابل للتوسيع مع تفاصيل الرحلة وتصور الخريطة",
      tabs: {
        overview: "نظرة عامة",
        props: "الخصائص والاستخدام",
        nested: "المكونات المتداخلة",
        demo: "عرض تفاعلي"
      },
      overview: {
        title: "نظرة عامة على المكون",
        description: "TripMasterDetailView هو مكون React يعرض محتوى الصف الموسع لجداول بيانات الرحلات. يوفر عرضًا شاملاً يجمع بين معلومات الرحلة المفصلة في شريط جانبي مع تصور خريطة تفاعلي يظهر مسارات الرحلة والتنبيهات.",
        features: [
          "تخطيط متجاوب قائم على الشبكة (شريط جانبي 3 أعمدة + خريطة 9 أعمدة)",
          "التكامل مع TripDetailSummary للحصول على معلومات شاملة عن الرحلة",
          "تصور خريطة تفاعلي باستخدام TripAlertMapViewer",
          "جلب البيانات في الوقت الفعلي مع حالات التحميل والخطأ",
          "تصميم متجاوب يتكيف مع أحجام الشاشات المختلفة",
          "التكامل السلس مع صفوف DataTable القابلة للتوسيع"
        ],
        location: "/components/TripMasterDetailView.tsx",
        usedIn: "يُستخدم بشكل أساسي كـ expandedRowRender في مكون TripListView"
      },
      props: {
        title: "الخصائص والواجهة",
        description: "يقبل TripMasterDetailView خاصية واحدة تحتوي على كائن بيانات الرحلة.",
        interface: {
          title: "واجهة الخصائص",
          description: "يتوقع المكون كائن Trip مع معلومات شاملة عن الرحلة:"
        },
        usage: {
          title: "مثال على الاستخدام",
          description: "يُستخدم عادة داخل دالة expandedRowRender في DataTable:"
        },
        states: {
          title: "حالات المكون",
          description: "يتعامل المكون مع ثلاث حالات رئيسية:",
          items: [
            "التحميل: يظهر مؤشر التحميل أثناء جلب بيانات الرحلة",
            "الخطأ: يعرض رسالة خطأ إذا فشل جلب البيانات",
            "النجاح: يعرض العرض الرئيسي-التفصيلي مع معلومات الرحلة"
          ]
        }
      },
      nested: {
        title: "المكونات المتداخلة والتبعيات",
        description: "ينسق TripMasterDetailView عدة مكونات متخصصة:",
        components: [
          {
            name: "TripDetailSummary",
            path: "/components/TripDetailSummary.tsx",
            kbLink: "/kb/tripdetailsummary",
            description: "يعرض تفاصيل شاملة عن الرحلة في الشريط الجانبي الأيسر بما في ذلك رمز الرحلة ومعلومات العبور وتفاصيل المركبة ومعلومات السائق ورموز التنقل",
            props: [
              "trip: Trip | null - كائن بيانات الرحلة الكامل",
              "className?: string - فئات CSS اختيارية للتصميم"
            ]
          },
          {
            name: "TripAlertMapViewer",
            path: "/components/TripAlertMapViewer.tsx",
            description: "مكون خريطة تفاعلي يظهر مسار الرحلة ومواقع التنبيهات ومعلومات التتبع في الوقت الفعلي",
            props: [
              "tripAlerts: TripAlert[] - مصفوفة تنبيهات الرحلة للعرض",
              "selectedTripId: string - معرف الرحلة المحددة حاليًا",
              "className: string - فئات CSS بما في ذلك تحديد الارتفاع"
            ]
          }
        ],
        hooks: {
          title: "الخطافات المخصصة",
          description: "يستخدم المكون خطافات مخصصة لإدارة البيانات:",
          items: [
            "useTripData(tripId): يجلب معلومات مفصلة عن الرحلة",
            "يُرجع: { tripData, loading, error } لإدارة الحالة"
          ]
        },
        dataFlow: {
          title: "تدفق البيانات",
          description: "تتدفق البيانات عبر المكون كما يلي:",
          steps: [
            "يستقبل كائن Trip عبر خاصية row",
            "يستخرج tripId ويستدعي خطاف useTripData",
            "يحمل تنبيهات الرحلة من بيانات JSON الثابتة",
            "يمرر البيانات إلى المكونات الفرعية للعرض",
            "يتعامل مع حالات التحميل والخطأ بسلاسة"
          ]
        }
      },
      demo: {
        title: "عرض تفاعلي",
        description: "العرض التفاعلي يتطلب كائن Trip. يُستخدم هذا المكون عادة داخل صفوف TripListView القابلة للتوسيع.",
        note: "للحصول على عرض كامل، قم بزيارة صفحة مكون TripListView حيث يُستخدم هذا المكون في السياق."
      },
      relatedComponents: {
        title: "المكونات ذات الصلة",
        description: "المكونات التي تعمل مع TripMasterDetailView:",
        items: [
          {
            name: "TripListView",
            link: "/kb/triplistview",
            description: "المكون الأصل الذي يستخدم TripMasterDetailView للصفوف القابلة للتوسيع"
          },
          {
            name: "TripDetailSummary",
            link: "/kb/tripdetailsummary",
            description: "المكون الفرعي الذي يعرض معلومات مفصلة عن الرحلة"
          }
        ]
      }
    }
  };

  const t = content[language];

  const propsInterfaceCode = `interface Props {
  row: Trip;
}

// The Trip interface contains comprehensive trip information:
interface Trip {
  id: number;
  tripId: string;
  transitNumber: number;
  description: string;
  tripStatus: 'pending' | 'activated' | 'ended' | 'cancelled';
  driver_details: TripDriver;
  vehicle_details: TripVehicle;
  route: TripRoute;
  shipment: TripShipment;
  tracking: TripTracking;
  compliance: TripCompliance;
  // ... additional properties
}`;

  const usageCode = `import TripMasterDetailView from "@/components/TripMasterDetailView";

// Used within DataTable's expandedRowRender
<DataTable<Trip>
  data={tripData}
  expandable={true}
  expandedRowRender={(row) => (
    <TripMasterDetailView row={row} />
  )}
  // ... other props
/>

// Or used directly with a trip object
function TripDetailPage({ trip }: { trip: Trip }) {
  return (
    <div className="container">
      <TripMasterDetailView row={trip} />
    </div>
  );
}`;

  const componentStructureCode = `export default function TripMasterDetailView({ row }: Props) {
  const { tripData, loading, error } = useTripData(row.tripId);
  const tripAlerts = (tripAlertsData as unknown as TripAlertsData).tripAlerts;

  // Loading state
  if (loading) {
    return <div className="text-center py-8">Loading trip details...</div>;
  }

  // Error state
  if (error) {
    return <div className="text-center py-8 text-red-500">Error: {error}</div>;
  }

  // Main render
  return (
    <div className="grid grid-cols-12 gap-4 p-2">
      {/* Left Sidebar - Trip Details (3 columns) */}
      <div className="col-span-12 md:col-span-3">
        <TripDetailSummary trip={tripData} />
      </div>

      {/* Map Viewer (9 columns) */}
      <div className="col-span-12 md:col-span-9">
        <TripAlertMapViewer
          tripAlerts={tripAlerts}
          selectedTripId={row.tripId}
          className="h-[600px]"
        />
      </div>
    </div>
  );
}`;

  // Mock trip data for demo
  const mockTrip = {
    id: 1,
    tripId: "TRP-2024-001",
    transitNumber: 12345,
    description: "Sample Trip",
    entry: "Riyadh Port",
    lastSeen: "2024-01-15 10:30",
    tracker: "TRK-001",
    driver: "John Doe",
    vehicle: "ABC-123",
    alerts: "None",
    status: ["active"] as ("active" | "charging" | "offline")[],
    tripStatus: "activated" as const,
    transitType: "Import",
    creationDate: "2024-01-15",
    activationDate: "2024-01-15",
    completionDate: undefined,
    expectedArrivalDate: "2024-01-16",
    endDate: undefined,
    driver_details: { driverName: "John Doe", driverNationality: "Saudi", driverId: "D001", licenseNumber: "L001" },
    vehicle_details: { vehiclePlateNumber: "ABC-123", model: "Truck", type: "Heavy", plateCountry: "SA", trackerNo: "TRK-001" },
    route: { origin: "Riyadh", destination: "Jeddah", distance: 950, estimatedDuration: "8 hours" },
    shipment: { shipmentDescription: "Electronics", ownerDescription: "Tech Corp", weight: 1000, value: 50000 },
    tracking: { elocks: "2", completeDistance: "950", remainingDistance: "200", currentLocation: "Highway 40" },
    compliance: { securityNotes: "High security shipment", customsStatus: "Cleared", documentsStatus: "Complete" }
  };

  return (
    <div className={`container mx-auto p-6 ${isRTL ? "text-right" : "text-left"}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">{t.title}</h1>
          <p className="text-xl text-muted-foreground">{t.subtitle}</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 border-b">
          {Object.entries(t.tabs).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === key
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              {label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === "overview" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.overview.title}</h2>
                <p className="text-lg mb-4">{t.overview.description}</p>
                <div className="mb-4 space-x-2">
                  <Badge variant="outline">{t.overview.location}</Badge>
                  <Badge variant="secondary">{t.overview.usedIn}</Badge>
                </div>
                <h3 className="text-lg font-semibold mb-3">Key Features:</h3>
                <ul className="space-y-2">
                  {t.overview.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </Card>

              {/* Related Components */}
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.relatedComponents.title}</h2>
                <p className="mb-4">{t.relatedComponents.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {t.relatedComponents.items.map((item, index) => (
                    <Link key={index} href={item.link} className="block">
                      <div className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                        <h3 className="font-semibold text-primary mb-2">{item.name}</h3>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              </Card>
            </>
          )}

          {activeTab === "props" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.props.title}</h2>
                <p className="mb-6">{t.props.description}</p>
                
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t.props.interface.title}</h3>
                  <p className="mb-4">{t.props.interface.description}</p>
                  <CodeBlock code={propsInterfaceCode} />
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t.props.usage.title}</h3>
                  <p className="mb-4">{t.props.usage.description}</p>
                  <CodeBlock code={usageCode} />
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">{t.props.states.title}</h3>
                  <p className="mb-3">{t.props.states.description}</p>
                  <ul className="space-y-2">
                    {t.props.states.items.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            </>
          )}

          {activeTab === "nested" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.nested.title}</h2>
                <p className="mb-6">{t.nested.description}</p>
                
                <div className="space-y-6">
                  {t.nested.components.map((component, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">{component.name}</h3>
                        <Badge variant="outline" className="text-xs">{component.path}</Badge>
                        {component.kbLink && (
                          <Link href={component.kbLink}>
                            <Badge variant="secondary" className="text-xs hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer">
                              View in KB
                            </Badge>
                          </Link>
                        )}
                      </div>
                      <p className="text-muted-foreground mb-3">{component.description}</p>
                      <div>
                        <h4 className="font-medium mb-2">Props:</h4>
                        <ul className="space-y-1">
                          {component.props.map((prop, propIndex) => (
                            <li key={propIndex} className="text-sm font-mono bg-muted p-2 rounded">
                              {prop}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">{t.nested.hooks.title}</h3>
                  <p className="mb-3">{t.nested.hooks.description}</p>
                  <ul className="space-y-2">
                    {t.nested.hooks.items.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary mt-1">•</span>
                        <span className="font-mono text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">{t.nested.dataFlow.title}</h3>
                  <p className="mb-3">{t.nested.dataFlow.description}</p>
                  <ol className="space-y-2">
                    {t.nested.dataFlow.steps.map((step, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary font-bold">{index + 1}.</span>
                        <span>{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">Component Structure:</h3>
                  <CodeBlock code={componentStructureCode} />
                </div>
              </Card>
            </>
          )}

          {activeTab === "demo" && (
            <>
              <Card className="p-6">
                <h2 className="text-2xl font-semibold mb-4">{t.demo.title}</h2>
                <p className="mb-4">{t.demo.description}</p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-blue-800 text-sm">{t.demo.note}</p>
                  <Link href="/kb/triplistview" className="text-blue-600 hover:text-blue-800 font-medium">
                    → Visit TripListView Component
                  </Link>
                </div>
                <div className="border rounded-lg p-4 bg-muted/50">
                  <h3 className="font-semibold mb-3">Sample with Mock Data:</h3>
                  <TripMasterDetailView row={mockTrip} />
                </div>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  );
}