"use client";

import { useState, useEffect } from "react";
import SummaryCard from "../../../components/shared/SummaryCard";
import { useLanguage } from "../../../contexts/LanguageContext";
import DashboardSummaryCards from "../../../components/shared/DashboardSummaryCards";
import DateTimePicker, {
  HijriDate,
} from "../../../components/shared/DateTimePicker";

export default function DummySafaPage() {
  const { t, direction, language } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [formattedDate, setFormattedDate] = useState<string>("");
  const [testResult, setTestResult] = useState<string>("");

  // Run the conversion test when component mounts
  useEffect(() => {
    const testDate = new Date(2040, 0, 1); // January 1, 2040
    const hijriResult = HijriDate.toHijri(testDate);
    const isCorrect = hijriResult.day === 16 && hijriResult.month === 12 && hijriResult.year === 1461;
    
    const result = `Test: Jan 1, 2040 → ${hijriResult.day}/${hijriResult.month}/${hijriResult.year} AH | Expected: 16/12/1461 AH | Result: ${isCorrect ? 'PASSED ✅' : 'FAILED ❌'}`;
    setTestResult(result);
    
    // Also run the original test function
    HijriDate.testConversion();
  }, []);

  const cardSize = "sm";
  const showTrends = false;
  const layout = "auto";

  const dummyCards = [
    {
      id: "inactive-trips-count",
      titleKey: "dashboard.card.inactiveTrips",
      value: 1551,
      icon: "bell-off" as const,
      color: "gray" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "resolved-trips-count",
      titleKey: "dashboard.card.resolvedToday",
      value: 1562,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "critical-alerts-count",
      titleKey: "dashboard.card.criticalAlerts",
      value: 0,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "pending-reviews-count",
      titleKey: "dashboard.card.pendingAlerts",
      value: 847,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    {
      id: "completed-today-count",
      titleKey: "dashboard.card.totalAlerts",
      value: 2156,
      icon: "check" as const,
      color: "blue" as const,
      trend: { value: 15.2, direction: "up" as const },
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir={direction}>
      <div className="p-6">
        {/* Dashboard Summary Cards Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.dashboardCards")}
          </h2>
          <div className="bg-white rounded-lg shadow-sm p-4">
            <DashboardSummaryCards
              cards={dummyCards}
              layout={layout}
              showTrends={showTrends}
              cardSize={cardSize}
              className="mb-2"
            />
          </div>
        </div>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.individualCards")}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {dummyCards.slice(0, 5).map((card) => (
              <SummaryCard
                key={`individual-${card.id}`}
                id={card.id}
                titleKey={card.titleKey}
                value={card.value}
                icon={card.icon}
                color={card.color}
                size={cardSize}
                showTrends={showTrends}
                trend={card.trend}
                className="w-full"
              />
            ))}
          </div>
        </div>
      </div>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
              {language === "ar"
                ? "منتقي التاريخ الهجري"
                : "Hijri Date Picker Demo"}
            </h1>
            
            {/* Test Result Display */}
            {testResult && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="font-semibold text-yellow-800 mb-2">
                  Date Conversion Test Result:
                </h3>
                <p className="text-yellow-700 font-mono text-sm">
                  {testResult}
                </p>
              </div>
            )}

            <div className="space-y-8">
              {/* Today's Date Display */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                <h2 className="text-xl font-semibold mb-4 text-blue-800">
                  {language === "ar" ? "التاريخ اليوم" : "Today's Date"}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <h3 className="font-medium text-gray-700 mb-2">
                      {language === "ar"
                        ? "التاريخ الميلادي"
                        : "Gregorian Date"}
                    </h3>
                    <p className="text-lg text-gray-900">
                      {new Date().toLocaleDateString(
                        language === "ar" ? "ar-SA" : "en-US",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                          weekday: "long",
                        }
                      )}
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <h3 className="font-medium text-gray-700 mb-2">
                      {language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
                    </h3>
                    <p
                      className="text-lg text-gray-900"
                      dir={language === "ar" ? "rtl" : "ltr"}
                    >
                      {HijriDate.getCurrentHijriDateString(
                        language as "ar" | "en"
                      )}
                    </p>
                  </div>
                </div>
              </div>

              {/* Demo Examples */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Basic Hijri Date Picker with Input Sync */}
                <div className="bg-white p-6 rounded-lg shadow-sm border-2 border-green-200">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-green-800 mb-2">
                      {language === "ar"
                        ? "منتقي التاريخ الهجري المحدث"
                        : "Enhanced Hijri Date Picker"}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === "ar"
                        ? "اكتب التاريخ مباشرة: 5/8/1445 أو انقر على التقويم"
                        : "Type date directly: 5/8/1445 or click calendar"}
                    </p>
                  </div>
                  <DateTimePicker
                    title={
                      language === "ar"
                        ? "منتقي التاريخ الهجري"
                        : "Hijri Date Picker"
                    }
                    dateType="hijri"
                    showSelectedDate={true}
                    onChange={setSelectedDate}
                    onFormattedDateChange={setFormattedDate}
                    placeholder={
                      language === "ar"
                        ? "اكتب التاريخ: 5/8/1445"
                        : "Type date: 5/8/1445"
                    }
                  />
                  {selectedDate && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-800">
                        <strong>
                          {language === "ar" ? "التاريخ المحدد:" : "Selected:"}
                        </strong>{" "}
                        {selectedDate.toDateString()}
                      </p>
                      <p className="text-sm text-green-700">
                        <strong>
                          {language === "ar" ? "منسق:" : "Formatted:"}
                        </strong>{" "}
                        {formattedDate}
                      </p>
                    </div>
                  )}
                </div>

                {/* Hijri Date & Time Picker */}
                <div className="bg-white p-6 rounded-lg shadow-sm border-2 border-blue-200">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-blue-800 mb-2">
                      {language === "ar"
                        ? "منتقي التاريخ والوقت الهجري"
                        : "Hijri Date & Time Picker"}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === "ar"
                        ? "تحديد التاريخ والوقت معاً"
                        : "Select both date and time"}
                    </p>
                  </div>
                  <DateTimePicker
                    title={language === "ar" ? "التاريخ والوقت" : "Date & Time"}
                    dateType="hijri"
                    showTime={true}
                    showSelectedDate={true}
                  />
                </div>

                {/* Switchable Calendar Type */}
                <div className="bg-white p-6 rounded-lg shadow-sm border-2 border-purple-200">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-purple-800 mb-2">
                      {language === "ar"
                        ? "منتقي التاريخ القابل للتبديل"
                        : "Switchable Calendar Type"}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === "ar"
                        ? "تبديل بين الهجري والميلادي"
                        : "Switch between Hijri and Gregorian"}
                    </p>
                  </div>
                  <DateTimePicker
                    title={
                      language === "ar"
                        ? "تقويم قابل للتبديل"
                        : "Switchable Calendar"
                    }
                    dateType="hijri"
                    allowTypeSwitch={true}
                    showSelectedDate={true}
                  />
                </div>

                {/* Gregorian Date Picker */}
                <div className="bg-white p-6 rounded-lg shadow-sm border-2 border-orange-200">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-orange-800 mb-2">
                      {language === "ar"
                        ? "منتقي التاريخ الميلادي"
                        : "Gregorian Date Picker"}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === "ar"
                        ? "اكتب التاريخ: 22/07/2025"
                        : "Type date: 22/07/2025"}
                    </p>
                  </div>
                  <DateTimePicker
                    title={
                      language === "ar" ? "التاريخ الميلادي" : "Gregorian Date"
                    }
                    dateType="gregorian"
                    showSelectedDate={true}
                    placeholder={
                      language === "ar"
                        ? "اكتب التاريخ: 22/07/2025"
                        : "Type date: 22/07/2025"
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
