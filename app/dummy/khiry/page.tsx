'use client';

import React from 'react';
// import { PageTemplate } from '../../../components/layout';
// import { TripFormView } from './trip-form/components/TripFormView';
import { TripTabsComponent } from './trip-form/components/TripTabsComponent';
// import { mockTripData } from './data/mockTripData';
import { TripDetailSummary } from '@/components/TripDetailSummary';
import { useTripData } from '@/hooks/useTripData';
import { useSearchParams } from 'next/navigation';
// import TripAlertFormView from '@/components/TripAlertFormView';

export default function TripDetailsPage() {

  const searchParams = useSearchParams();
    const tripId = searchParams.get('trip_id') || 'TRP001';

    const { tripData } = useTripData(tripId);

  
  return (
    <div className="mx-auto py-8">
      <div className="flex gap-6 h-full">
        {/* Left Side - Trip Form */}
        {/* <div className="w-1/5">
          <TripFormView
            trip={mockTripData["TRP001"]}
            showActions={true}
            onTrackTrip={(trip) => console.log('Track trip:', trip.id)}
            onStopTrip={() => console.log('Stop trip')}
            onEditSecurityNotes={() => {
              alert('Edit Security Notes functionality');
            }}
          />
        </div> */}
        <div className="w-1/4">
          <TripDetailSummary
            trip={tripData}
            className="h-full"
          />

          {/* <TripAlertFormView
              alertDetail={alertDetail}
              className='h-full'
          /> */}
        </div>

        {/* Right Side - Tabs */}
        <TripTabsComponent className="flex-1" />
      </div>
    </div>
  );
}
