import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface FilterCriteria {
  tripStatus?: string[];
  transitType?: string[];
  searchText?: string;
  entryPort?: string;
  dateRange?: {
    from: string;
    to: string;
  };
}

interface FilterStoreState {
  criteria: FilterCriteria;
}

interface FilterStoreActions {
  setTripStatus: (status: string[]) => void;
  setTransitType: (types: string[]) => void;
  setSearchText: (text: string) => void;
  setEntryPort: (port: string) => void;
  setDateRange: (range: { from: string; to: string }) => void;
  resetFilters: () => void;
  getFilterCriteria: () => FilterCriteria;
}

type FilterStoreType = FilterStoreState & FilterStoreActions;

export const useFilterStore = create<FilterStoreType>()(
  devtools((set, get) => ({
    criteria: {
      tripStatus: [],
      transitType: [],
      searchText: "",
      entryPort: "",
      dateRange: undefined,
    },

    setTripStatus: (status) =>
      set((state) => ({
        criteria: {
          ...state.criteria,
          tripStatus: status,
        },
      })),

    setTransitType: (types) =>
      set((state) => ({
        criteria: {
          ...state.criteria,
          transitType: types,
        },
      })),

    setSearchText: (text) =>
      set((state) => ({
        criteria: {
          ...state.criteria,
          searchText: text,
        },
      })),

    setEntryPort: (port) =>
      set((state) => ({
        criteria: {
          ...state.criteria,
          entryPort: port,
        },
      })),

    setDateRange: (range) =>
      set((state) => ({
        criteria: {
          ...state.criteria,
          dateRange: range,
        },
      })),

    resetFilters: () =>
      set(() => ({
        criteria: {
          tripStatus: [],
          transitType: [],
          searchText: "",
          entryPort: "",
          dateRange: undefined,
        },
      })),

    getFilterCriteria: () => get().criteria,
  }))
);
