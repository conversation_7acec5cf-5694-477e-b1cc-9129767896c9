import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import React from 'react'; 
import {  TripSchema } from '@/infrastructure/validation/tripSchema';
import { tripService } from '@/infrastructure/api/tripService';
import { logger } from '@/infrastructure/logging/logger';
import { useFilterStore } from './filterStore'; 
import { FilterCriteria } from './filterStore';

// -----------------------------
// Store Types
// -----------------------------
export interface TripStoreState {
  trips: typeof TripSchema._type[];
  selectedTrip: typeof TripSchema._type | null;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  connectionStatus: 'disconnected' | 'connected' | 'error';
  lastUpdate: Date | null;
  createTripFormError: Record<string, string> | null;
  updateTripFormError: Record<string, string> | null;
}

export interface TripStoreActions {
  initializeStore: () => Promise<void>;
  fetchTrips: (filters?: FilterCriteria) => Promise<void>;
  fetchTripById: (tripId: string) => Promise<void>;
  selectTrip: (tripId: string | null) => void;
  createTrip: (tripData: Partial<typeof TripSchema._type>) => Promise<void>;
  updateTripStatus: (tripId: string, status: typeof TripSchema._type['tripStatus']) => Promise<void>;
  handleTripUpdate: (update: Partial<TripSchema> & { tripId: string }) => void;
  applyClientSideFiltering: (trips: TripSchema[], criteria: FilterCriteria) => TripSchema[];
  cleanupStore: () => void;
  clearErrors: () => void;
}

export type TripStoreType = TripStoreState & TripStoreActions;

// -----------------------------
// Store Factory
// -----------------------------
const createTripStoreLogic = (instanceId: string) =>
  (set: any, get: any): TripStoreType => ({
    // State...
    trips: [],
    selectedTrip: null,
    isLoading: false,
    isInitialized: false,
    error: null,
    connectionStatus: 'disconnected',
    lastUpdate: null,
    createTripFormError: null,
    updateTripFormError: null,

    // Actions...
    initializeStore: async () => {
      if (get().isInitialized) return;
      try {
        set({ isLoading: true, error: null });
        logger.info(`TripStore[${instanceId}]: Initializing store`);
        await get().fetchTrips();
        set({
          isInitialized: true,
          isLoading: false,
          connectionStatus: 'connected',
          lastUpdate: new Date()
        });
        logger.info(`TripStore[${instanceId}]: Store initialized successfully`);
      } catch (error) {
        logger.error(`TripStore[${instanceId}]: Failed to initialize`, error as Error);
        set({
          error: 'Failed to initialize trip store',
          isLoading: false,
          connectionStatus: 'error'
        });
      }
    },

    fetchTrips: async (filters?: FilterCriteria) => {
      try {
        set({ isLoading: true, error: null });
        const filterCriteria = filters || useFilterStore.getState().getFilterCriteria();

        try {
          const trips = await tripService.getTrips(filterCriteria);
          const validatedTrips = trips
            .map(trip => {
              const result = TripSchema.safeParse(trip);
              if (!result.success) {
                logger.warn(`Invalid trip`, { tripId: trip.tripId, errors: result.error });
                return null;
              }
              return result.data;
            })
            .filter(Boolean) as TripSchema[];

          set({
            trips: validatedTrips,
            isLoading: false,
            connectionStatus: 'connected',
            lastUpdate: new Date()
          });

          logger.info(`Fetched ${validatedTrips.length} trips`);
        } catch {
          logger.warn(`API unavailable, using fallback`);
          const fallbackTrips = await tripService.getTrips();
          const filteredTrips = filterCriteria
            ? get().applyClientSideFiltering(fallbackTrips, filterCriteria)
            : fallbackTrips;

          set({
            trips: filteredTrips,
            isLoading: false,
            connectionStatus: 'error'
          });
        }
      } catch (error) {
        logger.error(`Failed to fetch trips`, error as Error);
        set({ error: 'Failed to load trips', isLoading: false });
      }
    },

    fetchTripById: async (tripId: string) => {
      try {
        set({ isLoading: true, error: null });
        const trip = await tripService.getTripById(tripId);
        const result = TripSchema.safeParse(trip);
        if (!result.success) throw new Error(`Invalid trip data for ${tripId}`);

        const currentTrips = get().trips;
        const index = currentTrips.findIndex((t: { tripId: string; }) => t.tripId === tripId);
        const updatedTrips = index >= 0
          ? [...currentTrips.slice(0, index), result.data, ...currentTrips.slice(index + 1)]
          : [...currentTrips, result.data];

        set({
          trips: updatedTrips,
          selectedTrip: result.data,
          isLoading: false,
          lastUpdate: new Date()
        });
      } catch (error) {
        logger.error(`Failed to fetch trip ${tripId}`, error as Error);
        set({ error: `Failed to load trip ${tripId}`, isLoading: false });
      }
    },

    selectTrip: (tripId: string | null) => {
      const trip = tripId ? get().trips.find((t: { tripId: string; }) => t.tripId === tripId) || null : null;
      set({ selectedTrip: trip });
    },

    createTrip: async (tripData: Partial<TripSchema>) => {
      try {
        set({ createTripFormError: null });
        const result = TripSchema.safeParse(tripData);
        if (!result.success) {
          const errors: Record<string, string> = {};
          result.error.errors.forEach(err => {
            const path = err.path.join('.');
            errors[path] = err.message;
          });
          set({ createTripFormError: errors });
          return;
        }

        const newTrip = await tripService.createTrip(result.data);
        set({
          trips: [...get().trips, newTrip],
          lastUpdate: new Date(),
          createTripFormError: null
        });
      } catch (error) {
        logger.error(`Failed to create trip`, error as Error);
        set({ createTripFormError: { root: 'Failed to create trip' } });
      }
    },

    updateTripStatus: async (tripId: string, status: TripSchema['tripStatus']) => {
      try {
        set({ updateTripFormError: null });
        const updatedTrip = await tripService.updateTripStatus(tripId, status);

        const currentTrips = get().trips;
        const index = currentTrips.findIndex((t: { tripId: string; }) => t.tripId === tripId);
        if (index >= 0) {
          const updatedTrips = [...currentTrips];
          updatedTrips[index] = updatedTrip;

          set({
            trips: updatedTrips,
            selectedTrip: get().selectedTrip?.tripId === tripId ? updatedTrip : get().selectedTrip,
            lastUpdate: new Date()
          });
        }
      } catch (error) {
        logger.error(`Failed to update status`, error as Error);
        set({ updateTripFormError: { root: 'Failed to update trip status' } });
      }
    },

    handleTripUpdate: (update: Partial<TripSchema> & { tripId: string }) => {
      const currentTrips = get().trips;
      const index = currentTrips.findIndex((t: { tripId: string; }) => t.tripId === update.tripId);

      if (index >= 0) {
        const updatedTrips = [...currentTrips];
        updatedTrips[index] = { ...updatedTrips[index], ...update };

        set({
          trips: updatedTrips,
          selectedTrip: get().selectedTrip?.tripId === update.tripId
            ? { ...get().selectedTrip!, ...update }
            : get().selectedTrip,
          lastUpdate: new Date()
        });
      }
    },

    applyClientSideFiltering: (trips, criteria) => {
      return trips.filter(trip => {
        if (criteria.tripStatus && !criteria.tripStatus.includes(trip.tripStatus)) return false;
        if (criteria.transitType && !criteria.transitType.includes(trip.transitType)) return false;
        if (criteria.searchText) {
          const search = criteria.searchText.toLowerCase();
          return [
            trip.tripId,
            trip.description,
            trip.driver,
            trip.vehicle,
            trip.driver_details?.driverName
          ].some(val => val?.toLowerCase().includes(search));
        }
        return true;
      });
    },

    cleanupStore: () => {
      set({
        isInitialized: false,
        connectionStatus: 'disconnected',
        trips: [],
        selectedTrip: null
      });
    },

    clearErrors: () => {
      set({ error: null, createTripFormError: null, updateTripFormError: null });
    }
  });

// -----------------------------
// Global Shared Store
// -----------------------------
export const useTripStore = create<TripStoreType>()(
  devtools(createTripStoreLogic('main'), { name: 'trip-store-main' })
);

// -----------------------------
// Isolated Instance Hook
// -----------------------------
const storeInstanceCache = new Map<string, any>();

export const useTripStoreInstance = (newInstance: boolean = false) => {
  const instanceStore = React.useMemo(() => {
    if (!newInstance) return null;

    const key = 'isolated-trips';
    if (storeInstanceCache.has(key)) return storeInstanceCache.get(key);

    const id = `instance-${Math.random().toString(36).substring(2, 9)}`;
    const newStore = create<TripStoreType>()(
      devtools(createTripStoreLogic(id), { name: `trip-store-${id}` })
    );

    storeInstanceCache.set(key, newStore);
    return newStore;
  }, [newInstance]);

  return newInstance ? instanceStore! : useTripStore;
};
