import { FilterCriteria } from "@/stores/filterStore";
import { TripSchema } from "../validation/tripSchema";

export type Trip = typeof TripSchema._type;

export interface TripService {
  getTrips(filters?: FilterCriteria): Promise<Trip[]>;
  getTripById(tripId: string): Promise<Trip>;
  updateTripStatus(tripId: string, status: Trip["tripStatus"]): Promise<Trip>;
  createTrip(tripData: Partial<Trip>): Promise<Trip>;
}

class MockTripService implements TripService {
  async getTrips(filters?: FilterCriteria): Promise<Trip[]> {
    const response = await fetch("/data/trips.json");
    let trips: Trip[] = await response.json();

    if (filters) {
      const { tripStatus, transitType,  entryPort, dateRange } = filters;

      if (tripStatus && tripStatus.length > 0) {
        trips = trips.filter((t) => tripStatus.includes(t.tripStatus));
      }

      if (transitType && transitType.length > 0) {
        trips = trips.filter((t) => transitType.includes(t.transitType));
      }

      if (entryPort) {
        trips = trips.filter((t) => t.entry === entryPort);
      }

     

      if (dateRange?.from && dateRange?.to) {
        trips = trips.filter((t) => {
          const date = new Date(t.creationDate).getTime();
          const from = new Date(dateRange.from).getTime();
          const to = new Date(dateRange.to).getTime();
          return date >= from && date <= to;
        });
      }
    }

    return trips;
  }

  async getTripById(tripId: string): Promise<Trip> {
    const trips = await this.getTrips();
    const trip = trips.find((t) => t.tripId === tripId);
    if (!trip) throw new Error(`Trip ${tripId} not found`);
    return trip;
  }

  async updateTripStatus(tripId: string, status: Trip["tripStatus"]): Promise<Trip> {
    const trip = await this.getTripById(tripId);
    return { ...trip, tripStatus: status };
  }

  async createTrip(tripData: Partial<Trip>): Promise<Trip> {
    return {
      ...tripData,
      tripId: crypto.randomUUID(),
      creationDate: new Date().toISOString(),
    } as Trip;
  }
}

class ApiTripService implements TripService {
  async getTrips(): Promise<Trip[]> {
    throw new Error("Not implemented");
  }
  async getTripById(): Promise<Trip> {
    throw new Error("Not implemented");
  }
  async updateTripStatus(): Promise<Trip> {
    throw new Error("Not implemented");
  }
  async createTrip(): Promise<Trip> {
    throw new Error("Not implemented");
  }
}

// Factory لاختيار نوع الخدمة
export const createTripService = (): TripService => {
  const useApi = process.env.NEXT_PUBLIC_USE_API === "true";
  return useApi ? new ApiTripService() : new MockTripService();
};

export const tripService = createTripService();
