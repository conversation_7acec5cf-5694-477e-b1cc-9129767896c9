import { z } from 'zod';

/* ----------------------------- Driver Schema ----------------------------- */
export const TripDriverSchema = z.object({
  driverId: z.string().min(1, 'Driver ID is required'),
  driverName: z.string().min(1, 'Driver name is required'),
  driverPassportNumber: z.string().min(1, 'Passport number is required'),
  driverNationality: z.string().min(1, 'Nationality is required'),
  driverContactNo: z.string().optional()
});
export type TripDriver = z.infer<typeof TripDriverSchema>;

/* ----------------------------- Vehicle Schema ---------------------------- */
export const TripVehicleSchema = z.object({
  vehicleId: z.string().min(1, 'Vehicle ID is required'),
  vehiclePlateNumber: z.string().min(1, 'Plate number is required'),
  trackerNo: z.string().min(1, 'Tracker number is required'),
  model: z.string().optional(),
  color: z.string().optional(),
  type: z.string().optional(),
  plateCountry: z.string().optional()
});
export type TripVehicle = z.infer<typeof TripVehicleSchema>;

/* ------------------------------ Route Schema ----------------------------- */
export const TripRouteSchema = z.object({
  routeId: z.string(),
  routeName: z.string(),
  entryPort: z.string(),
  exitPort: z.string()
});
export type TripRoute = z.infer<typeof TripRouteSchema>;

/* ----------------------------- Shipment Schema --------------------------- */
export const TripShipmentSchema = z.object({
  shipmentId: z.string(),
  shipmentDescription: z.string(),
  ownerDescription: z.string(),
  shipmentDescriptionArabic: z.string()
});
export type TripShipment = z.infer<typeof TripShipmentSchema>;

/* ----------------------------- Tracking Schema --------------------------- */
export const TripTrackingSchema = z.object({
  currentLocation: z.object({
    latitude: z.number(),
    longitude: z.number(),
    timestamp: z.string().datetime()
  }),
  completeDistance: z.number(),
  remainingDistance: z.number(),
  estimatedArrival: z.string().datetime(),
  elocks: z.string()
});
export type TripTracking = z.infer<typeof TripTrackingSchema>;

/* ---------------------------- Compliance Schema -------------------------- */
export const TripComplianceSchema = z.object({
  securityNotes: z.string(),
  customsStatus: z.string(),
  documentStatus: z.string()
});
export type TripCompliance = z.infer<typeof TripComplianceSchema>;

/* ------------------------------ Main Schema ------------------------------ */
export const TripSchema = z.object({
  id: z.number(),
  transitNumber: z.number(),
  description: z.string(),
  entry: z.string(),
  lastSeen: z.string().datetime(),
  tracker: z.string(),
  driver: z.string(),
  vehicle: z.string(),
  alerts: z.string(),

  status: z.array(z.enum(['active', 'charging', 'offline'])),
  tripId: z.string().min(1, 'Trip ID is required'),
  tripStatus: z.enum(['pending', 'activated', 'ended', 'cancelled']),
  transitType: z.string(),

  creationDate: z.string().datetime(),
  activationDate: z.string().nullable(),
  completionDate: z.string().nullable(),

  transitSeqNo: z.string(),
  declarationDate: z.string(),
  startingDate: z.string(),
  expectedArrivalDate: z.string(),
  endDate: z.string(),

  driver_details: TripDriverSchema,
  vehicle_details: TripVehicleSchema,
  route: TripRouteSchema,
  shipment: TripShipmentSchema,
  tracking: TripTrackingSchema,
  compliance: TripComplianceSchema
});
export type TripSchema = z.infer<typeof TripSchema>;
