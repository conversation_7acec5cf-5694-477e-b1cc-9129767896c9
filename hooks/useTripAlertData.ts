/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import { TripAlert } from '@/types/trip_alert';
import tripAlertsData from '../data/trip_alerts.json';

export function useTripAlertData(alertId?: string) {
  const [tripAlert, setTripAlert] = useState<TripAlert | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      setLoading(true);
      setError(null);
      
      // Use imported data directly
      const alertsData = tripAlertsData as any;
            
      if (alertId) {
        const detail = alertsData.tripAlerts?.find(
          (alert: TripAlert) => alert.alertId === alertId
        );
        if (!detail) {
          throw new Error(`Alert with ID ${alertId} not found`);
        }
        setTripAlert(detail);
      } else {
        const firstAlert = alertsData.tripAlerts?.[0];
        if (!firstAlert) {
          throw new Error('No alerts available');
        }
        setTripAlert(firstAlert);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setTripAlert(null);
    } finally {
      setLoading(false);
    }
  }, [alertId]);

  return { tripAlert, loading, error };
}
