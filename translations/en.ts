export const en = {
  'header.language': 'English',
  'header.languageSwitch': 'Arabic',
  'header.userAccount': '<PERSON>',
  'header.profile': 'Profile',
  'header.settings': 'Settings',
  'header.logout': 'Logout',
  'header.agencyName': 'Zakat, Tax, Customs Authority',
  'header.agencyNameArabic': 'هيئة الزكاة والضريبة والجمارك',
  'navigation.locationMonitor': 'Location Monitor',
  'navigation.focusedTrips': 'Focused Trips',
  'navigation.myAssignedPorts': 'My Assigned Ports',
  'navigation.dashboard': 'Dashboard',
  'navigation.configuration': 'Configuration',
  'navigation.suspiciousTrips': 'Suspicious Trips',
  'navigation.reports': 'Reports',
  'navigation.demoTrip': 'Demo Trip',
  'navigation.demo': 'Demo',
  'navigation.kb': 'KB',
  'navigation.listDemo': 'List Demo',
  'navigation.mapDemo': 'Map Demo',
  'footer.poweredBy': 'Powered by',
  'footer.technicalUnited': 'لابلاس سوفتوير',
  'footer.technicalUnitedArabic': 'Laplacesoftware',

  // Demo Trip Page
  "demoTrip.title": "Demo Trip Management",
  "demoTrip.description": "Comprehensive trip tracking and management system",
  "demoTrip.backToTable": "Back to Trip Table",
  "demoTrip.tripDetails": "Trip Details",
  "demoTrip.tripNumber": "Trip Number",
  "demoTrip.status": "Status",
  "demoTrip.type": "Type",
  "demoTrip.driver": "Driver",
  "demoTrip.vehicle": "Vehicle",
  "demoTrip.route": "Route",
  "demoTrip.shipment": "Shipment",
  "demoTrip.tracking": "Tracking",
  "demoTrip.compliance": "Compliance",
  "demoTrip.moreInfo": "More Info",
  "demoTrip.progress": "Progress",
  "demoTrip.estimatedArrival": "Estimated Arrival",
  "demoTrip.creationDate": "Creation Date",
  "demoTrip.activationDate": "Activation Date",
  "demoTrip.completionDate": "Completion Date",

  // Trip Alert Map Page
  "tripAlertMap.title": "Trip Alert Map Viewer",

  // Dashboard Cards
  "dashboard.card.currentTime": "Current Time",
  "dashboard.card.activeTrips": "Active Trips",
  "dashboard.card.closedTrips": "Closed Trips",
  "dashboard.card.notAcknowledgedAlerts": "Not Acknowledged Alerts",
  "dashboard.card.acknowledgedAlerts": "Acknowledged Alerts",
  "dashboard.card.activeTripsWithAlerts": "Active Trips with Alerts",
  "dashboard.card.activeTripsWithoutAlerts": "Active Trips without Alerts",
  "dashboard.card.totalActiveTripsWithAlerts": "Total Active Trips with Alerts",
  "dashboard.card.totalActiveTrips": "Total Active Trips",
  "dashboard.card.totalClosedTrips": "Total Closed Trips",
  "dashboard.card.inactiveTrips": "Inactive Trips",
  "dashboard.card.resolvedToday": "Resolved Today",
  "dashboard.card.criticalAlerts": "Critical Alerts",
  "dashboard.card.pendingAlerts": "Pending Alerts",
} as const;
